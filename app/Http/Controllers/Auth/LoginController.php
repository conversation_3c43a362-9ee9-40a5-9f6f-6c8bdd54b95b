<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;

class LoginController extends Controller
{
    function index()
    {
        return view("pages.auth.login");
    }

    function login(Request $request)
    {
        $credentials = $request->validate([
            "username" => "required|string",
            "password" => "required|string"
        ]);

        try {
            $user = User::where(["email" => $credentials["username"]])->first();

            // if (!$user) {
            //     return back()->with("error", "User tidak ditemukan");
            // }

            if (Auth::attempt(["email" => $credentials["username"], "password" => $credentials["password"]], $request->remember)) {
                return redirect()->intended("/home");
            }

            $body = [
                "Username" => $credentials["username"],
                "Password" => $credentials["password"]
            ];

            $url = "https://itrsudsoedarso.kalbarprov.go.id/api-soedarso/Asset/Login";
            // $url = "https://kucnhhgdr5kod6gpp2ryacfcue0tqvex.lambda-url.ap-southeast-1.on.aws/auth/login";

            $loginApi = Http::withHeaders(["SIMRS-KEY" => "vcl4imbpjs3h1eadjk"])->withOptions(['verify' => false])->asForm()->post($url, $body);

            if ($loginApi['status'] == true) {
                $response = $loginApi["data"];
                if ($response["NIP"] == null) {
                    return back()->with("error", "NIP belum terdaftar pada SIMRS");
                }

                $nip = str_replace(" ", "", $response["NIP"]);
                $employee = Employee::firstWhere("employee_identification_number", $nip);

                if (!$employee) {
                    return back()->with("error", $nip . "NIP belum terdaftar pada SENSI");
                }

                if (Auth::guard("employee")->loginUsingId($employee->id)) {
                    return redirect()->intended("/home");
                }

                return back()->with("error", "User tidak ditemukan");
            }

            return back()->with("error", "Email atau password salah");
        } catch (\Throwable $th) {
            dd($th->getMessage());
            return back()->with("error", $th->getMessage());
        }
    }

    function logout(Request $request)
    {
        Auth::guard('web')->logout();
        Auth::guard('employee')->logout();
        session_unset();
        session_abort();
        $request->session()->flush();
        $request->session()->forget('employee');
        Session::flush();
        Cookie::queue(Cookie::forget('laravel_session'));
        Cookie::queue(Cookie::forget('XSRF-TOKEN'));

        sleep(1);

        return redirect('/login')
            ->header('Cache-Control', 'no-store, no-cache, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0')
            ->with('message', 'You have been logged out successfully.');
    }

}
