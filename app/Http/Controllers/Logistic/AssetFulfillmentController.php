<?php

namespace App\Http\Controllers\Logistic;

use App\Http\Controllers\Controller;
use App\Models\Asset;
use App\Models\AssetEntry;
use App\Models\ConfigStockField;
use App\Models\Logistic;
use App\Models\LogisticDetail;
use App\Models\RequestLogistic;
use App\Models\Room;
use Illuminate\Support\Facades\DB;
use App\Models\RequestLogisticDetail;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class AssetFulfillmentController extends Controller
{
    protected $logisticType;

    public function __construct()
    {
        $this->logisticType = "OUT";
    }

    function index()
    {
        if (!hasPermissionInGuard('Realisasi Permintaan - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Realisasi Permintaan";
        $breadcrumbs = ["Logistik", "Realisasi Permintaan"];
        $dataConfigStockField = ConfigStockField::where("type", "LOGISTIC")->where("active", 1)->get();
        return view('logistic.asset-fulfillment.index', compact('title', 'breadcrumbs', 'dataConfigStockField'));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Realisasi Permintaan - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $query = RequestLogistic::getRequestLogisticQuery($this->logisticType, $request->room_id, $request->field_option);
            
            // Filter by date range
            if ($request->has('filter_date_start') && $request->filter_date_start != '') {
                $query->where('request_logistics.logistic_date', '>=', $request->filter_date_start);
            }
            
            if ($request->has('filter_date_end') && $request->filter_date_end != '') {
                $query->where('request_logistics.logistic_date', '<=', $request->filter_date_end);
            }
            
            return DataTables::eloquent($query)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return '
                        <a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-detail" data-bs-toggle="modal" data-route="' . route("logistic.asset-request.show", $row->id) . '"><i class="fas fa-search"></i></a>
                        <a href="#modal-update" class="btn btn-sm btn-outline-primary btn-edit" data-route="' . route("logistic.asset-fulfillment.show", $row->id) . '"><i class="fas fa-edit"></i></a>
                        ';
                })
                ->rawColumns(["action"])
                ->make(true);
        }
    }

    function update(Request $request, RequestLogistic $requestLogistic)
    {
        if (!hasPermissionInGuard('Realisasi Permintaan - Action')) {
            abort(403, "Unauthorized action.");
        }
        $userId = getAuthUserId();
        $userName = getAuthUserName();


        if ($requestLogistic->active == "0") {
            return response()->json(["message" => "error, request telah direalisasi"], 500);
        }

        foreach ($request->request_logistic_detail_id as $key => $requestDetailId) {
            $qty = str_replace(',', '', $request->asset_qty[$key]) ?? 0;
            RequestLogisticDetail::where('id', $requestDetailId)->update(['quantity' => $qty]);
        }

        $dataDetailRequest = RequestLogisticDetail::where("request_logistic_id", $requestLogistic->id)->get();
        $listAssetEntryId = [];
        foreach ($dataDetailRequest as $item) {
            $listAssetEntryId[] = $item->asset_entry_id;
        }

        $assets = Asset::whereIn("asset_entry_id", $listAssetEntryId)->get();
        $listAssetId = [];
        foreach ($assets as $item) {
            $listAssetId[] = $item->id;
        }

        # GET StockOpname
        $stockOpnameAsset = Logistic::listStockOpnameByAssetId($listAssetId)->groupBy('asset_entry_id');
        $sumsAssetEntries = $stockOpnameAsset->mapWithKeys(function ($group, $key) {
            return [$key => $group->sum('product_opname')];
        });

        # Check Balance
        foreach ($dataDetailRequest as $item) {
            if (($sumsAssetEntries[$item->asset_entry_id] - $item->quantity) < 0) {
                return response()->json(["message" => "error, realisasi tidak dapat dilakukan, saldo asset hasil pengurangan " . $stockOpnameAsset[$item->asset_entry_id][0]->asset_name . " minus"], 500);
            }
        }

        $detailAssetSave = [];
        $totalQty = 0;
        $totalPrice = 0;
        foreach ($dataDetailRequest as $item) {
            $itemRequired = $item->quantity;
            $itemFulfillment = 0;
            foreach ($stockOpnameAsset[$item->asset_entry_id] as $detailAssetPrice) {
                if ($itemRequired != $itemFulfillment) {
                    if (($itemRequired - $itemFulfillment) <= $detailAssetPrice->product_opname) {
                        $qty = $itemRequired - $itemFulfillment;
                        $price = $detailAssetPrice->unit_price * $qty;
                        $detailAssetSave[] = [
                            "logistic_type" => "OUT",
                            "asset_id" => $detailAssetPrice->id,
                            "asset_entry_id" => $item->asset_entry_id,
                            "uom_id" => $item->uom_id,
                            "uom_name" => $item->uom_name,
                            "unit_price" => $detailAssetPrice->unit_price,
                            "quantity" => $qty,
                            "total_price" => $price,
                        ];
                        $itemFulfillment = $itemRequired;
                        $totalQty += $qty;
                        $totalPrice += $price;
                    } else {
                        $qty = $detailAssetPrice->product_opname;
                        $price = ($detailAssetPrice->unit_price * $detailAssetPrice->product_opname);
                        $detailAssetSave[] = [
                            "logistic_type" => "OUT",
                            "asset_id" => $detailAssetPrice->id,
                            "asset_entry_id" => $item->asset_entry_id,
                            "uom_id" => $item->uom_id,
                            "uom_name" => $item->uom_name,
                            "unit_price" => $detailAssetPrice->unit_price,
                            "quantity" => $qty,
                            "total_price" => $price,
                        ];
                        $itemFulfillment += $detailAssetPrice->product_opname;
                        $totalQty += $qty;
                        $totalPrice += $price;
                    }
                }
            }
        }

        try {
            DB::beginTransaction();

            $logistic = Logistic::create([
                "logistic_type" => "OUT",
                "room_id" => $requestLogistic->room_id,
                "logistic_number" => "LGC-" . date('ymd') . "." . random_int(10000000, 99999999),
                "logistic_date" => date('Y-m-d'),
                "logistic_notes" => $requestLogistic->logistic_notes,
                "total_logistic_quantity" => $totalQty,
                "total_logistic_price" => $totalPrice,
                "request_date" => $requestLogistic->request_date,
                "requester_id" => $requestLogistic->requester_id,
                "requester_name" => $requestLogistic->requester_name,
                "requester_identification_number" => $requestLogistic->requester_identification_number,
                "requester_grade" => $requestLogistic->requester_grade,
                "requester_position" => $requestLogistic->requester_position,
                "logistic_document_path" => $requestLogistic->logistic_document_path,
                "approval_date" => date('Y-m-d'),
                "approver_id" => $userId,
                "approver_name" => $userName,
                "approver_identification_number" => null,
                "approver_grade" => null,
                "approver_position" => null,
                "created_by" => $userId,
                "created_by_name" => $userName,
                "created_at" => date('Y-m-d H:i:s')
            ]);

            foreach ($detailAssetSave as $item) {
                if ($item['quantity'] != 0) {
                    LogisticDetail::create([
                        "logistic_id" => $logistic->id,
                        "logistic_type" => $item['logistic_type'],
                        "asset_id" => $item['asset_id'],
                        "uom_id" => $item['uom_id'],
                        "uom_name" => $item['uom_name'],
                        "unit_price" => $item['unit_price'],
                        "quantity" => $item['quantity'],
                        "total_price" => $item['total_price'],
                    ]);

                    $latestBalanceAssetEntry = AssetEntry::where('id', $item['asset_entry_id'])->value('latest_balance') ?? 0;
                    AssetEntry::where('id', $item['asset_entry_id'])->update(['latest_balance' => ($latestBalanceAssetEntry - $item['quantity'])]);

                    $latestBalanceAsset = Asset::where('id', $item['asset_id'])->value('latest_balance') ?? 0;
                    Asset::where('id', $item['asset_id'])->update(['latest_balance' => ($latestBalanceAsset - $item['quantity'])]);
                }
            }

            $requestLogistic->update([
                "active" => 0,
                "logistic_id" => $logistic->id,
                "updated_at" => now(),
                "updated_by" => $userId,
                "updated_by_name" => $userName,
            ]);

            DB::commit();

            return response()->json(["message" => "Data berhasil disimpan"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(['message' => 'error, proses penyimpanan data error'], 400);
        }
    }

    function destroy(RequestLogistic $requestLogistic) {}

    function show(RequestLogistic $requestLogistic)
    {
        if (!hasPermissionInGuard('Realisasi Permintaan - View')) {
            abort(403, "Unauthorized action.");
        }
        $logisticDetail = RequestLogisticDetail::select(
            'request_logistic_details.*',
            'asset_entries.asset_entry_code',
            'asset_entries.item_name',
            'asset_entries.asset_name',
            'asset_entries.latest_balance',
            'items.item_code',
            'items.item_name'
        )
            ->join("asset_entries", "asset_entries.id", "=", "request_logistic_details.asset_entry_id")
            ->join("items", "items.id", "=", "asset_entries.item_id")
            ->where(["request_logistic_id" => $requestLogistic->id])->get();

        $logistic = RequestLogistic::select('request_logistics.*', 'rooms.room_code', 'rooms.room_name')
            ->join("rooms", "rooms.id", "=", "request_logistics.room_id")
            ->where(["request_logistics.id" => $requestLogistic->id])
            ->first();

        return view('components/logistic/detail-fulfillment-logistic', [
            "logistic" => $logistic,
            "logisticDetail" => $logisticDetail
        ]);
    }
}
