<?php

namespace App\Http\Controllers\Logistic;

use App\Http\Controllers\Controller;
use App\Models\AssetEntry;
use App\Models\RequestLogistic;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    function index()
    {
        $title = "Logistik";
        $breadcrumbs = ["Logistik"];

        // Get total logistic assets
        $totalLogisticAssets = AssetEntry::where('category_type', 'LOGISTIC')->count();

        // Get total active logistic requests
        $totalLogisticRequests = RequestLogistic::where('logistic_type', 'OUT')
            ->where('active', 1)
            ->count();

        return view("logistic.home.index", compact(
            "title", 
            "breadcrumbs",
            "totalLogisticAssets",
            "totalLogisticRequests"
        ));
    }
}
