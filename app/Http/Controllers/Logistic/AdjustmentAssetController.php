<?php

namespace App\Http\Controllers\Logistic;

use App\Http\Controllers\Controller;
use App\Models\Asset;
use App\Models\AssetEntry;
use App\Models\Logistic;
use App\Models\LogisticDetail;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class AdjustmentAssetController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Penyesuaian Barang - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Penyesuaian Barang";
        $breadcrumbs = ["Logistik", "Penyesuaian Barang"];
        return view('logistic.adjustment-asset.index', compact('title', 'breadcrumbs'));
    }

    public function create()
    {
        if (!hasPermissionInGuard('Penyesuaian Barang - Action')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Penyesuaian Barang";
        $breadcrumbs = ["Logistik", "Penyesuaian Barang"];
        return view("logistic.adjustment-asset.create", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Penyesuaian Barang - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $query = Logistic::getLogisticAdjustment();
            
            // Add date filter
            if ($request->has('filter_date_start') && $request->filter_date_start != '') {
                $query->where('logistics.logistic_date', '>=', $request->filter_date_start);
            }
            if ($request->has('filter_date_end') && $request->filter_date_end != '') {
                $query->where('logistics.logistic_date', '<=', $request->filter_date_end);
            }
            
            return DataTables::eloquent($query)
                ->addIndexColumn()
                ->editColumn('logistic_category', function($row) {
                    return $row->logistic_category === 'ADJUSTMENT_EXCESS' ? 'Penambahan' : 'Pengurangan';
                })
                ->addColumn("action", function ($row) {
                    return '
                        <a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-detail" data-bs-toggle="modal" data-route="' . route("logistic.stock-adjustment.show", $row->id) . '"><i class="fas fa-search"></i></a>';
                })
                ->rawColumns(["action"])
                ->make(true);
        }
    }

    public function store(Request $request)
    {
        if (!hasPermissionInGuard('Penyesuaian Barang - Action')) {
            abort(403, "Unauthorized action.");
        }
        $userId = getAuthUserId();
        $userName = getAuthUserName();

        if (!$request->adjustment_type) {
            return response()->json(['message' => 'error, Tipe Penyesuaian dibutuhkan'], 400);
        }
        if (!$request->asset_id) {
            return response()->json(['message' => 'error, data asset dibutuhkan'], 400);
        }

        $totalQty = 0;
        $totalPrice = 0;

        try {
            DB::beginTransaction();

            // Handle file upload jika ada
            $file_docs = $request->file("document");
            $fileDocsUrl = null;
            if ($file_docs) {
                $folder = "public/logistic/adjustment";
                $fileDocsUrl = $file_docs->storeAs($folder, now()->format("YmdHis") . "_logistic_adjustment" . "." . $file_docs->extension());
            }

            // Create Logistic record
            $logistic = Logistic::create([
                "logistic_type" => ($request->adjustment_type === 'ADJUSTMENT_SHORTAGE') ? "OUT" : "IN",
                "logistic_category" => $request->adjustment_type,
                "logistic_number" => "ADJ-" . date('ymd') . "." . random_int(10000000, 99999999),
                "logistic_date" => $request->logistic_date,
                "logistic_notes" => "PENYESUAIAN " . $request->notes,
                "logistic_document_path" => $fileDocsUrl,
                "created_by" => $userId,
                "created_by_name" => $userName,
                "created_at" => date('Y-m-d H:i:s')
            ]);

            // Process each asset
            for ($i = 0; $i < count($request->asset_id); $i++) {
                $assetId = $request->asset_id[$i];
                $qty = str_replace(',', '', $request->qty[$i]) ?? 0;

                if ($qty == 0) {
                    return response()->json(['message' => 'error, kuantitas barang tidak boleh 0'], 400);
                }

                // Get asset details
                $asset = Asset::find($assetId);
                if (!$asset) {
                    throw new \Exception("Asset tidak ditemukan");
                }

                $totalQty += $qty;
                $totalPrice += ($asset->unit_price * $qty);

                // Create LogisticDetail record
                LogisticDetail::create([
                    "logistic_id" => $logistic->id,
                    "logistic_type" => ($request->adjustment_type === 'ADJUSTMENT_SHORTAGE') ? "OUT" : "IN",
                    "asset_id" => $assetId,
                    "uom_id" => $asset->uom_id,
                    "uom_name" => $asset->uom_name,
                    "unit_price" => $asset->unit_price,
                    "quantity" => $qty,
                    "total_price" => ($asset->unit_price * $qty),
                ]);

                // Update balances
                $latestBalanceAsset = $asset->latest_balance ?? 0;
                $newBalance = $request->adjustment_type === 'ADJUSTMENT_EXCESS' ? ($latestBalanceAsset + $qty) : ($latestBalanceAsset - $qty);
                if ($request->adjustment_type === 'ADJUSTMENT_SHORTAGE' && $newBalance < 0) {
                    throw new \Exception("Stok tidak mencukupi untuk melakukan pengurangan " . $newBalance . " " . $asset->asset_name);
                }

                if ($request->adjustment_type === 'ADJUSTMENT_SHORTAGE') {
                    $latestBalanceAssetEntry = AssetEntry::where('id', $asset->asset_entry_id)->value('latest_balance') ?? 0;
                    AssetEntry::where('id', $asset->asset_entry_id)->update(['latest_balance' => ($latestBalanceAssetEntry - $qty)]);
                    $latestBalanceAsset = Asset::where('id', $assetId)->value('latest_balance') ?? 0;
                    Asset::where('id', $assetId)->update(['latest_balance' => ($latestBalanceAsset - $qty)]);
                } else {
                    $latestBalanceAssetEntry = AssetEntry::where('id', $asset->asset_entry_id)->value('latest_balance') ?? 0;
                    AssetEntry::where('id', $asset->asset_entry_id)->update(['latest_balance' => ($latestBalanceAssetEntry + $qty)]);
                    $latestBalanceAsset = Asset::where('id', $assetId)->value('latest_balance') ?? 0;
                    Asset::where('id', $assetId)->update(['latest_balance' => ($latestBalanceAsset + $qty)]);
                }
            }

            // Update total quantities and prices
            $logistic->update([
                "total_logistic_quantity" => $totalQty,
                "total_logistic_price" => $totalPrice
            ]);

            DB::commit();
            return response()->json(["message" => "Data berhasil disimpan"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(['message' => 'error, ' . $th->getMessage()], 400);
        }
    }

    function destroy(Logistic $logistic)
    {
        return response()->json(['message' => 'error, data barang telah digunakan, tidak dapat dihapus'], 400);
    }

    function show(Logistic $logistic)
    {
        if (!hasPermissionInGuard('Penyesuaian Barang - View')) {
            abort(403, "Unauthorized action.");
        }
        $logisticDetail = LogisticDetail::select(
            'logistic_details.*',
            'assets.qr_code',
            'assets.register_code',
            'assets.serial_number',
            'assets.asset_code',
            'assets.asset_name',
            'items.item_code',
            'items.item_name'
        )
            ->join("assets", "assets.id", "=", "logistic_details.asset_id")
            ->join("items", "items.id", "=", "assets.item_id")
            ->where(["logistic_id" => $logistic->id])->get();
        $logistic = Logistic::where(["id" => $logistic->id])->first();

        return view('components/logistic/detail-adjustment-logistic', [
            "logistic" => $logistic,
            "logisticDetail" => $logisticDetail
        ]);
    }
}
