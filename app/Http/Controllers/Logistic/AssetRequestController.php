<?php

namespace App\Http\Controllers\Logistic;

use App\Models\ConfigStockField;
use App\Models\Room;
use App\Models\Asset;
use App\Models\AssetEntry;
use Illuminate\Http\Request;
use App\Models\RequestLogistic;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\RequestLogisticDetail;
use Yajra\DataTables\Facades\DataTables;

class AssetRequestController extends Controller
{
    protected $logisticType;

    public function __construct()
    {
        $this->logisticType = "OUT";
    }

    function index()
    {
        if (!hasPermissionInGuard('Permintaan Barang - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Permintaan Barang";
        $breadcrumbs = ["Logistik", "Permintaan Barang"];
        return view('logistic.asset-request.index', compact('title', 'breadcrumbs'));
    }

    public function create()
    {
        if (!hasPermissionInGuard('Permintaan Barang - Action')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Permintaan Barang";
        $breadcrumbs = ["Logistik", "Permintaan Barang"];
        $dataConfigStockField = ConfigStockField::where("type", "LOGISTIC")->where("active", 1)->get();
        return view("logistic.asset-request.create", compact("title", "breadcrumbs", "dataConfigStockField"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Permintaan Barang - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $query = RequestLogistic::getRequestLogisticQuery($this->logisticType, $request->room_id, null);
            
            // Filter by date range
            if ($request->has('filter_date_start') && $request->filter_date_start != '') {
                $query->where('request_logistics.logistic_date', '>=', $request->filter_date_start);
            }
            
            if ($request->has('filter_date_end') && $request->filter_date_end != '') {
                $query->where('request_logistics.logistic_date', '<=', $request->filter_date_end);
            }
            
            return DataTables::eloquent($query)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return '
                    <a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-detail"
                        data-bs-toggle="modal"
                        data-route="' . route("logistic.asset-request.show", $row->id) . '"
                        data-id="' . $row->id . '"><i class="fas fa-search"></i></a>
                    <button type="button" class="btn btn-sm btn-outline-danger btn-delete" data-route="' . route("logistic.asset-request.destroy", $row->id) . '"><i class="fas fa-trash"></i></button>';
                })
                ->rawColumns(["action"])
                ->make(true);
        }
    }

    public function store(Request $request)
    {
        if (!hasPermissionInGuard('Permintaan Barang - Action')) {
            abort(403, "Unauthorized action.");
        }
        $userId = getAuthUserId();
        $userName = getAuthUserName();

        $logisticType = "OUT";
        $totalQty = 0;
        if (!$request->field_option) {
            return response()->json(['message' => 'error, Bidang permintaan dibutuhkan'], 400);
        }
        if (!$request->target_room) {
            return response()->json(['message' => 'error, Ruangan dibutuhkan'], 400);
        }
        if (!$request->asset_entry_id) {
            return response()->json(['message' => 'error, data asset dibutuhkan'], 400);
        }

        $assetEntryData = AssetEntry::whereIn("id", $request->asset_entry_id)->get()->keyBy('id');
        $dataInputAssetEntry = [];
        foreach ($request->asset_entry_id as $key => $assetEntryId) {
            $qty = str_replace(',', '', $request->asset_qty[$key]) ?? 0;
            if ($qty == 0 || $qty == "") {
                return response()->json(['message' => 'error, harga atau kuantitas barang tidak boleh kurang dari 0'], 400);
            }
            if ($qty < 1) {
                return response()->json(['message' => 'error, kuantitas barang tidak boleh kurang dari 1'], 400);
            }
            $totalQty += $qty;

            $dataInputAssetEntry[] = [
                'asset_entry_id' => $assetEntryId,
                'qty' => str_replace(',', '', $request->asset_qty[$key]) ?? 0
            ];
        }

        $result = [];
        foreach ($dataInputAssetEntry as $entry) {
            $id = $entry['asset_entry_id'];
            $qty = (int)$entry['qty'];
            if (!isset($result[$id])) {
                $result[$id] = 0;
            }
            $result[$id] += $qty;
        }
        $uniqInputData = [];
        foreach ($result as $id => $qty) {
            $uniqInputData[] = [
                'asset_entry_id' => $id,
                'qty' => $qty
            ];
        }

        $roomId = $request->target_room;
        $fieldOption = $request->field_option;
        $roomData = Room::leftJoin('employees', 'rooms.pic_room', '=', 'employees.id')->where("rooms.id", $roomId)->first();
        $requester_id = ($roomData) ? $roomData->pic_room : null;
        $requester_name = ($roomData) ? $roomData->employee_name : null;
        $requester_identification_number = ($roomData) ? $roomData->employee_identification_number : null;
        $requester_grade = ($roomData) ? $roomData->employee_grade : null;
        $requester_position = ($roomData) ? $roomData->employee_position : null;

        try {
            DB::beginTransaction();

            $file_docs = $request->file("document");
            $fileDocsUrl = null;
            if ($file_docs) {
                $folder = "public/logistic/request";
                $fileDocsUrl = $file_docs->storeAs($folder, now()->format("YmdHis") . "_logistic_request" . "." . $file_docs->extension());
            }

            $logistic = RequestLogistic::create([
                "logistic_type" => $logisticType,
                "room_id" => $roomId,
                "config_stock_field_id" => $fieldOption,
                "logistic_number" => "RLGC-" . date('ymd') . "." . random_int(10000000, 99999999),
                "logistic_date" => $request->request_date,
                "logistic_notes" => $request->notes,
                "total_logistic_quantity" => $totalQty,
                "request_date" => $request->request_date,
                "requester_id" => $requester_id,
                "requester_name" => $requester_name,
                "requester_identification_number" => $requester_identification_number,
                "requester_grade" => $requester_grade,
                "requester_position" => $requester_position,
                "logistic_document_path" => $fileDocsUrl,
                "created_by" => $userId,
                "created_by_name" => $userName,
                "created_at" => date('Y-m-d H:i:s')
            ]);

            foreach ($uniqInputData as $x) {
                RequestLogisticDetail::create([
                    "request_logistic_id" => $logistic->id,
                    "logistic_type" => $logisticType,
                    "asset_entry_id" => $assetEntryData[$x['asset_entry_id']]->id,
                    "uom_id" => $assetEntryData[$x['asset_entry_id']]->uom_id,
                    "uom_name" => $assetEntryData[$x['asset_entry_id']]->uom_name,
                    "quantity" => $x['qty'],
                    "request_quantity" => $x['qty'],
                ]);
            }

            DB::commit();

            return response()->json(["message" => "Data berhasil disimpan"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(['message' => 'error, proses penyimpanan data error'], 400);
        }
    }

    function destroy(RequestLogistic $requestLogistic)
    {
        if (!hasPermissionInGuard('Permintaan Barang - Action')) {
            abort(403, "Unauthorized action.");
        }
        try {
            DB::beginTransaction();
            RequestLogisticDetail::where('request_logistic_id', $requestLogistic->id)->delete();
            $requestLogistic->delete();
            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus"
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function show(RequestLogistic $requestLogistic)
    {
        if (!hasPermissionInGuard('Permintaan Barang - View')) {
            abort(403, "Unauthorized action.");
        }
        $logisticDetail = RequestLogisticDetail::select(
            'request_logistic_details.*',
            'asset_entries.asset_entry_code',
            'asset_entries.item_name',
            'asset_entries.asset_name',
            'items.item_code',
            'items.item_name'
        )
            ->join("asset_entries", "asset_entries.id", "=", "request_logistic_details.asset_entry_id")
            ->join("items", "items.id", "=", "asset_entries.item_id")
            ->where(["request_logistic_id" => $requestLogistic->id])->get();

        $logistic = RequestLogistic::select('request_logistics.*', 'rooms.room_code', 'rooms.room_name', 'config_stock_fields.code AS field_code', 'config_stock_fields.name AS field_name')
            ->join("rooms", "rooms.id", "=", "request_logistics.room_id")
            ->join("config_stock_fields", "config_stock_fields.id", "=", "request_logistics.config_stock_field_id")
            ->where(["request_logistics.id" => $requestLogistic->id])
            ->first();

        return view('components/logistic/detail-request-logistic', [
            "logistic" => $logistic,
            "logisticDetail" => $logisticDetail
        ]);
    }

    public function print(RequestLogistic $requestLogistic)
    {
        try {
            $logisticDetail = RequestLogisticDetail::select(
                'request_logistic_details.*',
                'asset_entries.asset_entry_code',
                'asset_entries.item_name',
                'asset_entries.asset_name',
                'asset_entries.unit_price',
                'asset_entries.total_price',
                'items.item_code',
                'items.item_name'
            )
                ->join("asset_entries", "asset_entries.id", "=", "request_logistic_details.asset_entry_id")
                ->join("items", "items.id", "=", "asset_entries.item_id")
                ->where(["request_logistic_id" => $requestLogistic->id])->get();

            $logistic = RequestLogistic::select('request_logistics.*', 'rooms.room_code', 'rooms.room_name', 'config_stock_fields.code AS field_code', 'config_stock_fields.name AS field_name')
                ->join("rooms", "rooms.id", "=", "request_logistics.room_id")
                ->join("config_stock_fields", "config_stock_fields.id", "=", "request_logistics.config_stock_field_id")
                ->where(["request_logistics.id" => $requestLogistic->id])
                ->first();

            $pdf = Pdf::loadView('components/logistic/print-request-logistic', [
                "logistic" => $logistic,
                "logisticDetail" => $logisticDetail
            ])->setPaper('a4', 'portrait');

            return $pdf->download('Permintaan_Barang_' . $logistic->logistic_number . '.pdf');
        } catch (\Throwable $th) {
            return back()->with("error", $th->getMessage());
        }
    }
}
