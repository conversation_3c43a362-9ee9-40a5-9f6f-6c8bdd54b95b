<?php

namespace App\Http\Controllers\Logistic;

use App\Http\Controllers\Controller;
use App\Models\Asset;
use App\Models\AssetEntry;
use App\Models\Logistic;
use App\Models\LogisticDetail;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class IncomingAssetController extends Controller
{
    protected $logisticType;

    public function __construct()
    {
        $this->logisticType = "IN";
    }

    function index()
    {
        if (!hasPermissionInGuard('Barang Masuk - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Barang Masuk";
        $breadcrumbs = ["Logistik", "Barang Masuk"];
        return view('logistic.incoming-asset.index', compact('title', 'breadcrumbs'));
    }

    public function create()
    {
        if (!hasPermissionInGuard('Barang Masuk - Action')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Barang Masuk";
        $breadcrumbs = ["Logistik", "Barang Masuk"];
        return view("logistic.incoming-asset.create", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Barang Masuk - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $query = Logistic::getLogisticQuery($this->logisticType, "");
            
            // Filter by distributor
            if ($request->has('filter_distributor') && $request->filter_distributor != '') {
                $query->where('logistics.distributor_id', $request->filter_distributor);
            }
            
            // Filter by date range
            if ($request->has('filter_date_start') && $request->filter_date_start != '') {
                $query->where('logistics.logistic_date', '>=', $request->filter_date_start);
            }
            
            if ($request->has('filter_date_end') && $request->filter_date_end != '') {
                $query->where('logistics.logistic_date', '<=', $request->filter_date_end);
            }
            
            return DataTables::eloquent($query)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return '
                        <a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-detail" data-bs-toggle="modal" data-route="' . route("logistic.incoming-asset.show", $row->id) . '"><i class="fas fa-search"></i></a> 
                        <a href="' . route("logistic.incoming-asset.print-qr", $row->id) . '" class="btn btn-sm btn-outline-info btn-print-qr" target="_blank"><i class="fas fa-qrcode"></i></a>
                        <button style="display: none" type="button" class="btn btn-sm btn-outline-danger btn-delete" data-route="' . route("logistic.incoming-asset.destroy", $row->id) . '"><i class="fas fa-trash"></i></button>';
                })
                ->rawColumns(["action"])
                ->make(true);
        }
    }

    public function store(Request $request)
    {
        if (!hasPermissionInGuard('Barang Masuk - Action')) {
            abort(403, "Unauthorized action.");
        }
        $userId = getAuthUserId();
        $userName = getAuthUserName();

        if (!$request->distributor) {
            return response()->json(['message' => 'error, data Distributor dibutuhkan'], 400);
        }
        if (!$request->asset_entry_id) {
            return response()->json(['message' => 'error, data asset dibutuhkan'], 400);
        }

        $totalQty = 0;
        $totalPrice = 0;
        for ($i = 0; $i < count($request['asset_entry_id']); $i++) {
            $assetEntryId = $request['asset_entry_id'][$i];
            $price = str_replace(',', '', $request['price'][$i]) ?? 0;
            $qty = str_replace(',', '', $request['qty'][$i]) ?? 0;
            $key = $assetEntryId . '-' . $price;
            if ($price == 0 || $qty == 0) {
                return response()->json(['message' => 'error, harga atau kuantitas barang tidak boleh kurang dari 0'], 400);
            }
            $totalQty += ($qty);
            $totalPrice += ($price * $qty);
            if (!isset($inputItem[$key])) {
                $inputItem[$key] = [
                    'key' => $key,
                    'asset_entry_id' => $assetEntryId,
                    'price' => $price,
                    'qty' => $qty,
                ];
            } else {
                $inputItem[$key]['qty'] += $qty;
            }
        }
        $inputItem = array_values($inputItem);

        $assetsEntries = AssetEntry::whereIn("id", $request->asset_entry_id)->get()->keyBy('id')->toArray();

        try {
            DB::beginTransaction();

            $file_docs = $request->file("document");
            $fileDocsUrl = null;
            if ($file_docs) {
                $folder = "public/logistic/incoming";
                $fileDocsUrl = $file_docs->storeAs($folder, now()->format("YmdHis") . "_logistic_incoming" . "." . $file_docs->extension());
            }

            $logistic = Logistic::create([
                "logistic_type" => $this->logisticType,
                "logistic_number" => "LGC-" . date('ymd') . "." . random_int(10000000, 99999999),
                "distributor_id" => $request->distributor,
                "logistic_date" => $request->logistic_date,
                "logistic_notes" => $request->notes,
                "total_logistic_quantity" => $totalQty,
                "total_logistic_price" => $totalPrice,
                "logistic_document_path" => $fileDocsUrl,
                "created_by" => $userId,
                "created_by_name" => $userName,
                "created_at" => date('Y-m-d H:i:s')
            ]);

            foreach ($inputItem as $item) {
                $dataAsset = Asset::where('asset_entry_id', $item['asset_entry_id'])->where('unit_price', $item['price'])->first();
                $assetId = null;
                $uomId = null;
                $uomName = null;
                if (isset($dataAsset)) {
                    $assetId = $dataAsset->id;
                    $uomId = $dataAsset->uom_id;
                    $uomName = $dataAsset->uom_name;
                } else {
                    $latestSubRegisterCode = Asset::where('asset_entry_id', $item['asset_entry_id'])->orderBy('id', 'desc')->first();
                    $latestCode = ($latestSubRegisterCode) ? (int)$latestSubRegisterCode->sub_register_code : 0;
                    $newSubRegisterCode = str_pad($latestCode + 1, 2, '0', STR_PAD_LEFT);
                    $dataNewAsset = [
                        'category_type' => 'LOGISTIC',
                        'asset_entry_id' => $item['asset_entry_id'],
                        'item_id' => $assetsEntries[$item['asset_entry_id']]['item_id'],
                        'asset_code' => $assetsEntries[$item['asset_entry_id']]['asset_entry_code'] . "." . $newSubRegisterCode,
                        'register_code' => substr($assetsEntries[$item['asset_entry_id']]['asset_entry_code'], -3),
                        'sub_register_code' => $newSubRegisterCode,
                        'qr_code' => $assetsEntries[$item['asset_entry_id']]['asset_entry_code'] . "." . $newSubRegisterCode,
                        'unit_price' => $item['price'],
                        'uom_id' => $assetsEntries[$item['asset_entry_id']]['uom_id'],
                        'uom_name' => $assetsEntries[$item['asset_entry_id']]['uom_name'],
                        'asset_name' => $assetsEntries[$item['asset_entry_id']]['asset_name']
                    ];
                    $newAsset = Asset::create($dataNewAsset);
                    $assetId = $newAsset->id;
                    $uomId = $assetsEntries[$item['asset_entry_id']]['uom_id'];
                    $uomName = $assetsEntries[$item['asset_entry_id']]['uom_name'];
                }

                LogisticDetail::create([
                    "logistic_id" => $logistic->id,
                    "logistic_type" => $this->logisticType,
                    "asset_id" => $assetId,
                    "uom_id" => $uomId,
                    "uom_name" => $uomName,
                    "unit_price" => $item['price'],
                    "quantity" => $item['qty'],
                    "total_price" => ($item['price'] * $item['qty']),
                ]);

                $latestBalanceAssetEntry = AssetEntry::where('id', $item['asset_entry_id'])->value('latest_balance') ?? 0;
                AssetEntry::where('id', $item['asset_entry_id'])->update(['latest_balance' => ($latestBalanceAssetEntry + $item['qty'])]);

                $latestBalanceAsset = Asset::where('id', $assetId)->value('latest_balance') ?? 0;
                Asset::where('id', $assetId)->update(['latest_balance' => ($latestBalanceAsset + $item['qty'])]);
            }

            DB::commit();

            return response()->json(["message" => "Data berhasil disimpan"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(['message' => 'error, proses penyimpanan data error'], 400);
        }
    }

    function destroy(Logistic $logistic)
    {
        return response()->json(['message' => 'error, data barang telah digunakan, tidak dapat dihapus'], 400);
    }

    function show(Logistic $logistic)
    {
        if (!hasPermissionInGuard('Barang Masuk - View')) {
            abort(403, "Unauthorized action.");
        }
        $logisticDetail = LogisticDetail::select(
            'logistic_details.*',
            'assets.qr_code',
            'assets.register_code',
            'assets.serial_number',
            'assets.asset_code',
            'assets.asset_name',
            'items.item_code',
            'items.item_name'
        )
            ->join("assets", "assets.id", "=", "logistic_details.asset_id")
            ->join("items", "items.id", "=", "assets.item_id")
            ->where(["logistic_id" => $logistic->id])->get();
        $logistic = Logistic::where(["id" => $logistic->id])->first();

        return view('components/logistic/detail-incoming-logistic', [
            "logistic" => $logistic,
            "logisticDetail" => $logisticDetail
        ]);
    }

    function printQr(Logistic $logistic)
    {
        if (!hasPermissionInGuard('Barang Masuk - View')) {
            abort(403, "Unauthorized action.");
        }
        
        $logisticDetail = LogisticDetail::select(
            'logistic_details.*',
            'assets.qr_code',
            'assets.register_code',
            'assets.serial_number',
            'assets.asset_code',
            'assets.asset_name',
            'items.item_code',
            'items.item_name'
        )
            ->join("assets", "assets.id", "=", "logistic_details.asset_id")
            ->join("items", "items.id", "=", "assets.item_id")
            ->where(["logistic_id" => $logistic->id])->get();
        
        $logistic = Logistic::where(["id" => $logistic->id])->first();

        // Hitung total QR yang akan dicetak (maksimal 50 per item)
        $totalQrToPrint = 0;
        foreach ($logisticDetail as $detail) {
            $totalQrToPrint += min($detail->quantity, 50);
        }

        return view('logistic.incoming-asset.print-qr', [
            "logistic" => $logistic,
            "logisticDetail" => $logisticDetail,
            "totalQrToPrint" => $totalQrToPrint
        ]);
    }
}
