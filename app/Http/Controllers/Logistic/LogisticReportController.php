<?php

namespace App\Http\Controllers\Logistic;

use Carbon\Carbon;
use App\Models\Room;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ResumeOutgoingExport;
use App\Exports\IncomingLogisticsExport;
use App\Exports\LogisticRecapExport;
use App\Models\LogisticDetail;
use App\Models\ConfigStockRecap;
use Yajra\DataTables\Facades\DataTables;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Exports\OutgoingLogisticsExport;
use App\Exports\RequestLogisticsExport;
use App\Exports\StockOpnameExport;
use App\Exports\InOutLogisticsExport;
use App\Exports\KartuStockExport;

class LogisticReportController extends Controller
{
    function request(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Request Barang - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            list($start, $end) = explode(" - ", request("periode"));
            $startDate = Carbon::parse($start)->format('Y-m-d');
            $endDate = Carbon::parse($end)->addDay(1)->format('Y-m-d');

            $params = [
                "start" => $startDate,
                "end" => $endDate,
            ];

            $query = DB::table('request_logistic_details')
                ->join('request_logistics', 'request_logistic_details.request_logistic_id', '=', 'request_logistics.id')
                ->join('asset_entries', 'request_logistic_details.asset_entry_id', '=', 'asset_entries.id')
                ->join('rooms', 'request_logistics.room_id', '=', 'rooms.id');

            if ($request->room_id) {
                $query->where('request_logistics.room_id', $request->room_id);
                $room = Room::find($request->room_id);
                $params['room_name'] = $room ? $room->room_name : '';
            }

            $data = $query->select(
                    'request_logistics.logistic_number',
                    'request_logistics.logistic_date',
                    'rooms.room_name',
                    'asset_entries.asset_entry_code',
                    'asset_entries.asset_name',
                    'request_logistic_details.quantity',
                    'request_logistic_details.request_quantity',
                    'request_logistic_details.uom_name',
                    'request_logistics.logistic_id'
                )
                ->where('request_logistics.logistic_type', 'OUT')
                ->whereBetween('request_logistics.logistic_date', [$startDate, $endDate])
                ->get();

            return view('components.logistic.report-request', ["params" => $params, "data" => $data]);
        }

        $title = "Laporan Request Barang";
        $breadcrumbs = ["Logistik", "Laporan Request Barang"];
        $exportRoute = route('logistic.report-logistic.request.export');

        return view('logistic.report.request', compact('title', 'breadcrumbs', 'exportRoute'));
    }

    public function requestExport(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Request Barang - View')) {
            abort(403, "Unauthorized action.");
        }
        $startDate = Carbon::parse($request->start_date)->format('Y-m-d');
        $endDate = Carbon::parse($request->end_date)->addDay(1)->format('Y-m-d');

        $params = [
            "start" => $startDate,
            "end" => $endDate,
        ];

        $query = DB::table('request_logistic_details')
                ->join('request_logistics', 'request_logistic_details.request_logistic_id', '=', 'request_logistics.id')
                ->join('asset_entries', 'request_logistic_details.asset_entry_id', '=', 'asset_entries.id')
                ->join('rooms', 'request_logistics.room_id', '=', 'rooms.id');

        if ($request->room_id) {
            $query->where('request_logistics.room_id', $request->room_id);
            $room = Room::find($request->room_id);
            $params['room_name'] = $room ? $room->room_name : '';
        }

        $data = $query->select(
                    'request_logistics.logistic_number',
                    'request_logistics.logistic_date',
                    'rooms.room_name',
                    'asset_entries.asset_entry_code',
                    'asset_entries.asset_name',
                    'request_logistic_details.quantity',
                    'request_logistic_details.request_quantity',
                    'request_logistic_details.uom_name',
                    'request_logistics.logistic_id'
                )
                ->where('request_logistics.logistic_type', 'OUT')
                ->whereBetween('request_logistics.logistic_date', [$startDate, $endDate])
                ->get();

        return Excel::download(new RequestLogisticsExport($data, $params), 'Request Barang.xls');
    }

    function incoming(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Barang Masuk - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            list($start, $end) = explode(" - ", request("periode"));
            $startDate = Carbon::parse($start)->format('Y-m-d');
            $endDate = Carbon::parse($end)->addDay(1)->format('Y-m-d');

            $params = [
                "start" => $startDate,
                "end" => $endDate,
            ];
            $data = DB::table('logistic_details')
                ->join('logistics', 'logistic_details.logistic_id', '=', 'logistics.id')
                ->join('assets', 'logistic_details.asset_id', '=', 'assets.id')
                ->leftjoin('distributors', 'logistics.distributor_id', '=', 'distributors.id')
                ->select(
                    'logistics.logistic_number',
                    'logistics.logistic_date',
                    'logistics.logistic_notes',
                    'distributors.distributor_name',
                    'assets.asset_code',
                    'assets.asset_name',
                    'logistic_details.quantity',
                    'logistic_details.unit_price',
                    'logistic_details.total_price',
                    'logistic_details.uom_name'
                )
                ->where('logistics.logistic_type', 'IN')
                ->whereBetween('logistics.logistic_date', [$startDate, $endDate])
                ->get();

            return view('components.logistic.report-incoming', ["params" => $params, "data" => $data]);
        }

        $title = "Laporan Barang Masuk";
        $breadcrumbs = ["Logistik", "Laporan Barang Masuk"];
        $exportRoute = route('logistic.report-logistic.incoming.export');

        return view('logistic.report.incoming', compact('title', 'breadcrumbs', 'exportRoute'));
    }

    public function incomingExport(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Barang Masuk - View')) {
            abort(403, "Unauthorized action.");
        }
        $startDate = Carbon::parse($request->start_date)->format('Y-m-d');
        $endDate = Carbon::parse($request->end_date)->addDay(1)->format('Y-m-d');

        $params = [
            "start" => $startDate,
            "end" => $endDate,
        ];

        $data = DB::table('logistic_details')
            ->join('logistics', 'logistic_details.logistic_id', '=', 'logistics.id')
            ->join('assets', 'logistic_details.asset_id', '=', 'assets.id')
            ->select(
                'logistics.logistic_number',
                'logistics.logistic_date',
                'logistics.logistic_notes',
                'assets.asset_code',
                'assets.asset_name',
                'logistic_details.quantity',
                'logistic_details.unit_price',
                'logistic_details.total_price',
                'logistic_details.uom_name'
            )
            ->where('logistics.logistic_type', 'IN')
            ->whereBetween('logistics.logistic_date', [$startDate, $endDate])
            ->get();

        return Excel::download(new IncomingLogisticsExport($data, $params), 'Barang Masuk.xls');
    }

    public function outgoingExport(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Barang Keluar - View')) {
            abort(403, "Unauthorized action.");
        }
        $startDate = Carbon::parse($request->start_date)->format('Y-m-d');
        $endDate = Carbon::parse($request->end_date)->addDay(1)->format('Y-m-d');

        $params = [
            "start" => $startDate,
            "end" => $endDate,
        ];

        $data = DB::table('logistic_details')
            ->join('logistics', 'logistic_details.logistic_id', '=', 'logistics.id')
            ->join('assets', 'logistic_details.asset_id', '=', 'assets.id')
            ->leftJoin('rooms', 'logistics.room_id', '=', 'rooms.id')
            ->select(
                'logistics.logistic_number',
                'logistics.logistic_date',
                'logistics.logistic_notes',
                'rooms.room_name',
                'assets.asset_code',
                'assets.asset_name',
                'logistic_details.quantity',
                'logistic_details.unit_price',
                'logistic_details.total_price',
                'logistic_details.uom_name'
            )
            ->where('logistics.logistic_type', 'OUT')
            ->whereBetween('logistics.logistic_date', [$startDate, $endDate])
            ->get();

        return Excel::download(new OutgoingLogisticsExport($data, $params), 'Barang Keluar.xls');
    }


    function outgoing(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Barang Keluar - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            list($start, $end) = explode(" - ", request("periode"));
            $startDate = Carbon::parse($start)->format('Y-m-d');
            $endDate = Carbon::parse($end)->addDay(1)->format('Y-m-d');

            $params = [
                "start" => $startDate,
                "end" => $endDate,
            ];
            $data = DB::table('logistic_details')
                ->join('logistics', 'logistic_details.logistic_id', '=', 'logistics.id')
                ->join('assets', 'logistic_details.asset_id', '=', 'assets.id')
                ->leftJoin('rooms', 'logistics.room_id', '=', 'rooms.id')
                ->select(
                    'logistics.logistic_number',
                    'logistics.logistic_date',
                    'rooms.room_name',
                    'assets.asset_code',
                    'assets.asset_name',
                    'logistic_details.quantity',
                    'logistic_details.unit_price',
                    'logistic_details.total_price',
                    'logistic_details.uom_name'
                )
                ->where('logistics.logistic_type', 'OUT')
                ->whereBetween('logistics.logistic_date', [$startDate, $endDate])
                ->get();
            return view('components.logistic.report-outgoing', ["params" => $params, "data" => $data]);
        }

        $title = "Laporan Barang Keluar";
        $breadcrumbs = ["Logistik", "Laporan Barang Keluar"];
        $exportRoute = route('logistic.report-logistic.outgoing.export');

        return view('logistic.report.outgoing', compact('title', 'breadcrumbs', 'exportRoute'));
    }

    function resumeOutgoing(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Resume Barang Keluar - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Laporan Resume Barang Keluar";
        $breadcrumbs = ["Logistik", "Resume Barang Keluar"];

        return view('logistic.report.resumeoutgoing', compact('title', 'breadcrumbs'));
    }

    public function resumeOutgoingData(Request $request)
    {
        $type = $request->input('type', 'table');
        $assetEntryId = $request->input('asset_entry_id', null);

        if ($request->has('periode')) {
            list($start, $end) = explode(' - ', $request->input('periode'));
            $startDate = Carbon::createFromFormat('F d, Y', trim($start))->startOfDay();
            $endDate = Carbon::createFromFormat('F d, Y', trim($end))->endOfDay();
        } else {
            $startDate = Carbon::now()->subMonth()->startOfDay();
            $endDate = Carbon::now()->endOfDay();
        }

        $logistics = DB::table('logistics')
            ->join('rooms', 'logistics.room_id', '=', 'rooms.id')
            ->join('logistic_details', 'logistics.id', '=', 'logistic_details.logistic_id')
            ->join('assets', 'logistic_details.asset_id', '=', 'assets.id')
            ->select(
                'rooms.id',
                'rooms.room_code',
                'rooms.room_name',
                DB::raw('SUM(logistic_details.quantity) as total_logistic_quantity'),
                DB::raw('SUM(logistic_details.total_price) as total_logistic_price')
            )
            ->where('logistics.logistic_type', 'OUT')
            ->whereBetween('logistics.logistic_date', [$startDate, $endDate])
            ->when($assetEntryId, function ($query, $assetEntryId) {
                return $query->where('assets.asset_entry_id', $assetEntryId);
            })
            ->groupBy('logistics.room_id')
            ->get();

        if ($type === 'chart') {
            $labels = $logistics->pluck('room_name')->toArray();
            $totalQuantities = $logistics->pluck('total_logistic_quantity')->toArray();
            $totalPrices = $logistics->pluck('total_logistic_price')->toArray();

            return response()->json([
                'labels' => $labels,
                'total_assets' => $totalQuantities,
                'total_prices' => $totalPrices,
            ]);
        } else {
            return DataTables::of($logistics)
                ->addIndexColumn()
                ->addColumn('action', function ($row) {
                    return '<a href="#modal-dialog" class="btn btn-sm btn-outline-primary btn-show" data-bs-toggle="modal" data-id="' . $row->id . '"><i class="fas fa-search"></i></a>';
                })
                ->rawColumns(['action'])
                ->make(true);
        }
    }


    function resumeoutgoingShow(Room $room, Request $request)
    {
        if ($request->input('periode')) {
            list($start, $end) = explode(" - ", $request->input("periode"));
            $startDate = Carbon::createFromFormat('F d, Y', trim($start))->startOfDay();
            $endDate = Carbon::createFromFormat('F d, Y', trim($end))->endOfDay();
        } else {
            $startDate = Carbon::now()->subMonth()->startOfDay();
            $endDate = Carbon::now()->endOfDay();
        }

        $assetEntryId = $request->input('asset_entry_id', null);
        $groupByItemCode = $request->input('group_by_item_code', 0);

        $query = DB::table('logistics')
            ->join('logistic_details', 'logistics.id', '=', 'logistic_details.logistic_id')
            ->join('assets', 'assets.id', '=', 'logistic_details.asset_id')
            ->leftJoin('items', 'assets.item_id', '=', 'items.id')
            ->where('logistics.room_id', $room->id)
            ->where('logistics.logistic_type', 'OUT')
            ->whereBetween('logistics.logistic_date', [$startDate, $endDate])
            ->when($assetEntryId, function ($query, $assetEntryId) {
                return $query->where('assets.asset_entry_id', $assetEntryId);
            });

        if ($groupByItemCode) {
            // Group by item code - combine assets with same item code
            $data = $query->select(
                'items.item_code',
                'items.item_name',
                'assets.asset_name',
                DB::raw('SUM(logistic_details.quantity) as quantity'),
                DB::raw('AVG(logistic_details.unit_price) as unit_price'),
                DB::raw('SUM(logistic_details.total_price) as total_price')
            )
            ->groupBy('items.item_code', 'items.item_name', 'assets.asset_name')
            ->get();
        } else {
            // Normal view - show individual transactions
            $data = $query->select(
                'logistics.logistic_number',
                'logistics.logistic_date',
                'logistic_details.asset_id',
                'assets.asset_name',
                'items.item_code',
                'items.item_name',
                'logistic_details.quantity',
                'logistic_details.unit_price',
                'logistic_details.total_price'
            )
            ->groupBy('logistics.logistic_number', 'logistics.logistic_date', 'logistic_details.asset_id')
            ->get();
        }

        return DataTables::of($data)
            ->addIndexColumn()
            ->rawColumns(['action'])
            ->make(true);
    }

    public function resumeoutgoingExport(Request $request, Room $room)
    {
        if (!hasPermissionInGuard('Laporan Resume Barang Keluar - View')) {
            abort(403, "Unauthorized action.");
        }
        
        // Load relasi pic untuk mencegah N+1 query
        $room->load('pic');
        
        // Parse tanggal periode
        if ($request->input('periode')) {
            list($start, $end) = explode(" - ", $request->input("periode"));
            $startDate = Carbon::createFromFormat('F d, Y', trim($start))->startOfDay();
            $endDate = Carbon::createFromFormat('F d, Y', trim($end))->endOfDay();
        } else {
            $startDate = Carbon::now()->subMonth()->startOfDay();
            $endDate = Carbon::now()->endOfDay();
        }

        $assetEntryId = $request->input('asset_entry_id', null);
        $groupByItemCode = $request->input('group_by_item_code', 0);

        $query = DB::table('logistics')
            ->join('logistic_details', 'logistics.id', '=', 'logistic_details.logistic_id')
            ->join('assets', 'assets.id', '=', 'logistic_details.asset_id')
            ->leftJoin('items', 'assets.item_id', '=', 'items.id')
            ->where('logistics.room_id', $room->id)
            ->where('logistics.logistic_type', 'OUT')
            ->whereBetween('logistics.logistic_date', [$startDate, $endDate])
            ->when($assetEntryId, function ($query, $assetEntryId) {
                return $query->where('assets.asset_entry_id', $assetEntryId);
            });

        if ($groupByItemCode) {
            // Group by item code - combine assets with same item code
            $data = $query->select(
                'items.item_code',
                'items.item_name',
                'assets.asset_name',
                DB::raw('SUM(logistic_details.quantity) as quantity'),
                DB::raw('AVG(logistic_details.unit_price) as unit_price'),
                DB::raw('SUM(logistic_details.total_price) as total_price')
            )
            ->groupBy('items.item_code', 'items.item_name', 'assets.asset_name')
            ->orderBy('items.item_code', 'asc')
            ->get()
            ->map(function($item) {
                return (array) $item;
            })
            ->toArray();
        } else {
            // Normal view - show individual transactions
            $data = $query->select(
                'logistics.logistic_number',
                'logistics.logistic_date',
                'logistic_details.asset_id',
                'assets.asset_name',
                'items.item_code',
                'items.item_name',
                'logistic_details.quantity',
                'logistic_details.unit_price',
                'logistic_details.total_price'
            )
            ->orderBy('logistics.logistic_date', 'asc')
            ->get()
            ->map(function($item) {
                return (array) $item;
            })
            ->toArray();
        }

        // Format periode untuk display
        $periodeDisplay = '';
        if ($request->input('periode')) {
            $periodeDisplay = $request->input('periode');
        } else {
            $periodeDisplay = $startDate->format('d F Y') . ' - ' . $endDate->format('d F Y');
        }

        $roomData = [
            'room_code' => $room->room_code,
            'room_name' => $room->room_name,
            'pic_room' => $room->pic->name ?? '-',
            'is_grouped' => $groupByItemCode,
            'periode' => $periodeDisplay,
        ];

        $fileName = $groupByItemCode ? 
            'resume_room_' . $room->room_code . '_grouped.xlsx' : 
            'resume_room_' . $room->room_code . '_detail.xlsx';

        return Excel::download(new ResumeOutgoingExport($roomData, $data), $fileName);
    }

    function stockopname(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Stock Opname - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            if ($request->has('periode')) {
                list($start, $end) = explode(' - ', $request->input('periode'));
                $startDate = Carbon::createFromFormat('F d, Y', trim($start))->startOfDay();
                $endDate = Carbon::createFromFormat('F d, Y', trim($end))->endOfDay();
            } else {
                $startDate = Carbon::now()->subMonth()->startOfDay();
                $endDate = Carbon::now()->endOfDay();
            }

            $data = DB::table('assets as a')
                ->join('asset_entries as ae', 'a.asset_entry_id', '=', 'ae.id')
                ->selectRaw("
                    a.id,
                    a.asset_name,
                    a.qr_code,
                    a.unit_price,
                    IFNULL(b.total_in, 0) - IFNULL(b.total_out, 0) AS qty_saldo_awal_asset,
                    IFNULL(c.total_in, 0) AS qty_asset_masuk,
                    IFNULL(c.total_out, 0) AS qty_asset_keluar,
                    d.latest_year AS tahun_terakhir
                ")
                ->leftJoinSub(
                    DB::table('logistic_details as a')
                        ->select('a.asset_id')
                        ->selectRaw("
                        SUM(CASE WHEN a.logistic_type = 'IN' THEN a.quantity ELSE 0 END) AS total_in,
                        SUM(CASE WHEN a.logistic_type = 'OUT' THEN a.quantity ELSE 0 END) AS total_out
                    ")
                        ->join('logistics as lg', 'lg.id', '=', 'a.logistic_id')
                        ->where('lg.logistic_date', '<', $startDate)
                        ->groupBy('a.asset_id'),
                    'b',
                    'b.asset_id',
                    '=',
                    'a.id'
                )
                ->leftJoinSub(
                    DB::table('logistic_details as a')
                        ->select('a.asset_id')
                        ->selectRaw("
                        SUM(CASE WHEN a.logistic_type = 'IN' THEN a.quantity ELSE 0 END) AS total_in,
                        SUM(CASE WHEN a.logistic_type = 'OUT' THEN a.quantity ELSE 0 END) AS total_out
                    ")
                        ->join('logistics as lg', 'lg.id', '=', 'a.logistic_id')
                        ->whereBetween('lg.logistic_date', [$startDate, $endDate])
                        ->groupBy('a.asset_id'),
                    'c',
                    'c.asset_id',
                    '=',
                    'a.id'
                )
                ->leftJoinSub(
                    DB::table('logistic_details as ld')
                        ->select('ld.asset_id')
                        ->selectRaw('YEAR(MAX(lg.logistic_date)) as latest_year')
                        ->where('ld.logistic_type', 'IN')
                        ->join('logistics as lg', 'lg.id', '=', 'ld.logistic_id')
                        ->groupBy('ld.asset_id'),
                    'd',
                    'd.asset_id',
                    '=',
                    'a.id'
                )
                ->where('a.category_type', 'LOGISTIC')
                ->when($request->stock_recap_id, function ($query) use ($request) {
                    return $query->where('ae.config_stock_recapitulation_id', $request->stock_recap_id);
                })
                ->get()
                ->when($request->total_keluar === 'GT_ZERO', function ($collection) {
                    return $collection->filter(function ($item) {
                        return $item->qty_asset_keluar > 0;
                    });
                });

            return view('components.logistic.report-stock-opname', ["data" => $data]);
        }

        $configStockRecap = ConfigStockRecap::where("type", "LOGISTIC")->get();
        $title = "Stock Opname Logistik";
        $breadcrumbs = ["Logistik", "Laporan Stock Opname"];

        return view('logistic.report.stock-opname', compact('title', 'breadcrumbs', 'configStockRecap'));
    }

    function generalledger()
    {
        if (request()->ajax()) {
            if (request()->has('periode')) {
                list($start, $end) = explode(' - ', request()->input('periode'));
                $startDate = Carbon::createFromFormat('F d, Y', trim($start))->startOfDay();
                $endDate = Carbon::createFromFormat('F d, Y', trim($end))->endOfDay();
            } else {
                $startDate = Carbon::now()->subMonth()->startOfDay();
                $endDate = Carbon::now()->endOfDay();
            }

            $summary = DB::table('assets as a')
                ->selectRaw("
                        a.id, a.asset_name, a.qr_code, a.unit_price,
                        IFNULL(b.total_in, 0) - IFNULL(b.total_out, 0) AS qty_saldo_awal_asset,
                        IFNULL(c.total_in, 0) AS qty_asset_masuk,
                        IFNULL(c.total_out, 0) AS qty_asset_keluar
                    ")
                ->leftJoinSub(
                    DB::table('logistic_details as a')
                        ->select('a.asset_id')
                        ->selectRaw("
                        SUM(CASE WHEN a.logistic_type = 'IN' THEN a.quantity ELSE 0 END) AS total_in,
                        SUM(CASE WHEN a.logistic_type = 'OUT' THEN a.quantity ELSE 0 END) AS total_out
                    ")
                        ->join('logistics as lg', 'lg.id', '=', 'a.logistic_id')
                        ->where('lg.logistic_date', '<', $startDate)
                        ->groupBy('a.asset_id'),
                    'b',
                    'b.asset_id',
                    '=',
                    'a.id'
                )
                ->leftJoinSub(
                    DB::table('logistic_details as a')
                        ->select('a.asset_id')
                        ->selectRaw("
                        SUM(CASE WHEN a.logistic_type = 'IN' THEN a.quantity ELSE 0 END) AS total_in,
                        SUM(CASE WHEN a.logistic_type = 'OUT' THEN a.quantity ELSE 0 END) AS total_out
                    ")
                        ->join('logistics as lg', 'lg.id', '=', 'a.logistic_id')
                        ->whereBetween('lg.logistic_date', [$startDate, $endDate])
                        ->groupBy('a.asset_id'),
                    'c',
                    'c.asset_id',
                    '=',
                    'a.id'
                )
                ->where('a.category_type', 'LOGISTIC')
                ->where("a.id", request("asset"))
                ->first();


            $details = LogisticDetail::with(["logistic"])
                ->select('logistic_details.*', 'rooms.room_code', 'rooms.room_name')
                ->join('logistics', 'logistics.id', '=', 'logistic_details.logistic_id')
                ->leftJoin('rooms', 'rooms.id', '=', 'logistics.room_id')
                ->whereHas("logistic", function ($query) use ($startDate, $endDate) {
                    $query->whereBetween("logistic_date", [$startDate, $endDate]);
                })
                ->where(["asset_id" => request("asset")])
                ->get();

            return view('components.logistic.report-kartu-stock', ["summary" => $summary, "details" => $details]);
        }

        $title = "Laporan Kartu Stock";
        $breadcrumbs = ["Logistik", "Laporan Kartu Stock"];
        return view('logistic.report.kartu-stock', compact('title', 'breadcrumbs'));
    }


    public function recap(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Stock Opname - View')) {
            abort(403, "Unauthorized action.");
        }
        
        if ($request->ajax()) {
            $startDate = null;
            $endDate = null;
            $periodLabel = '';
            
            if ($request->period_type === 'month') {
                $date = Carbon::createFromFormat('Y-m', $request->periode);
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d');
                $periodLabel = $date->locale('id')->isoFormat('MMMM YYYY');
            } else {
                list($start, $end) = explode(" - ", $request->periode);
                $startDate = Carbon::parse($start)->format('Y-m-d');
                $endDate = Carbon::parse($end)->format('Y-m-d');
                $periodLabel = $startDate . ' - ' . Carbon::parse($end)->format('Y-m-d');
            }

            $params = [
                "start" => $startDate,
                "end" => $endDate,
                "period_type" => $request->period_type,
                "period_label" => $periodLabel,
                "logistic_type" => $request->logistic_type
            ];

            $query = DB::table('config_stock_recapitulations')
                ->leftJoin('stock_recapitulation', function ($join) use ($startDate, $endDate) {
                    $join->on('config_stock_recapitulations.id', '=', 'stock_recapitulation.config_stock_recapitulation_id')
                        ->whereBetween('stock_recapitulation.recapitulation_date', [$startDate, $endDate]);
                })
                ->leftJoinSub(function ($query) use ($endDate) {
                    $query->from('assets as a')
                        ->join('asset_entries as ae', 'a.asset_entry_id', '=', 'ae.id')
                        ->leftJoinSub(
                            DB::table('logistic_details as ld')
                                ->select('ld.asset_id')
                                ->selectRaw('
                                    SUM(CASE WHEN ld.logistic_type = "IN" THEN ld.quantity ELSE -ld.quantity END) as total_qty
                                ')
                                ->join('logistics as lg', 'lg.id', '=', 'ld.logistic_id')
                                ->where('lg.logistic_date', '<=', $endDate)
                                ->groupBy('ld.asset_id'),
                            'stock',
                            'stock.asset_id',
                            '=',
                            'a.id'
                        )
                        ->select('ae.config_stock_recapitulation_id')
                        ->selectRaw('SUM(COALESCE(stock.total_qty, 0) * a.unit_price) as total_value')
                        ->where('a.category_type', 'LOGISTIC')
                        ->groupBy('ae.config_stock_recapitulation_id');
                }, 'logistic_totals', function ($join) {
                    $join->on('config_stock_recapitulations.id', '=', 'logistic_totals.config_stock_recapitulation_id');
                })
                ->select(
                    'config_stock_recapitulations.id',
                    'config_stock_recapitulations.name as jenis_barang',
                    'config_stock_recapitulations.type',
                    DB::raw('CASE
                        WHEN config_stock_recapitulations.type = "EXTERNAL"
                        THEN COALESCE(SUM(stock_recapitulation.recapitulation_amount), 0)
                        ELSE COALESCE(logistic_totals.total_value, 0)
                    END as jumlah_harga')
                )
                ->where('config_stock_recapitulations.active', 1);

            // Filter berdasarkan logistic_type
            if ($request->logistic_type === 'internal') {
                $query->where('config_stock_recapitulations.type', 'LOGISTIC');
            } elseif ($request->logistic_type === 'external') {
                $query->where('config_stock_recapitulations.type', 'EXTERNAL');
            }

            $data = $query->groupBy(
                    'config_stock_recapitulations.id',
                    'config_stock_recapitulations.name',
                    'config_stock_recapitulations.type',
                    'logistic_totals.total_value'
                )
                ->orderBy('config_stock_recapitulations.name')
                ->get();

            return view('components.logistic.report-recap', ["params" => $params, "data" => $data]);
        }

        $title = "Stock Opname RS";
        $breadcrumbs = ["Logistik", "Stock Opname RS"];
        $exportRoute = route('logistic.report-logistic.recap.export');

        return view('logistic.report.recap', compact('title', 'breadcrumbs', 'exportRoute'));
    }

    public function recapExport(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Stock Opname - View')) {
            abort(403, "Unauthorized action.");
        }

        $startDate = null;
        $endDate = null;
        $periodLabel = '';
        
        if ($request->period_type === 'month') {
            $date = Carbon::createFromFormat('Y-m', $request->start_date);
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d');
            $periodLabel = $date->locale('id')->isoFormat('MMMM YYYY');
        } else {
            $startDate = Carbon::parse($request->start_date)->format('Y-m-d');
            $endDate = Carbon::parse($request->end_date)->format('Y-m-d');
            $periodLabel = $startDate . ' - ' . Carbon::parse($request->end_date)->format('Y-m-d');
        }

        $params = [
            "start" => $startDate,
            "end" => $endDate,
            "period_type" => $request->period_type,
            "period_label" => $periodLabel,
            "logistic_type" => $request->logistic_type
        ];

        $query = DB::table('config_stock_recapitulations')
            ->leftJoin('stock_recapitulation', function ($join) use ($startDate, $endDate) {
                $join->on('config_stock_recapitulations.id', '=', 'stock_recapitulation.config_stock_recapitulation_id')
                    ->whereBetween('stock_recapitulation.recapitulation_date', [$startDate, $endDate]);
            })
            ->leftJoinSub(function ($query) use ($endDate) {
                $query->from('assets as a')
                    ->join('asset_entries as ae', 'a.asset_entry_id', '=', 'ae.id')
                    ->leftJoinSub(
                        DB::table('logistic_details as ld')
                            ->select('ld.asset_id')
                            ->selectRaw('
                                SUM(CASE WHEN ld.logistic_type = "IN" THEN ld.quantity ELSE -ld.quantity END) as total_qty
                            ')
                            ->join('logistics as lg', 'lg.id', '=', 'ld.logistic_id')
                            ->where('lg.logistic_date', '<=', $endDate)
                            ->groupBy('ld.asset_id'),
                        'stock',
                        'stock.asset_id',
                        '=',
                        'a.id'
                    )
                    ->select('ae.config_stock_recapitulation_id')
                    ->selectRaw('SUM(COALESCE(stock.total_qty, 0) * a.unit_price) as total_value')
                    ->where('a.category_type', 'LOGISTIC')
                    ->groupBy('ae.config_stock_recapitulation_id');
            }, 'logistic_totals', function ($join) {
                $join->on('config_stock_recapitulations.id', '=', 'logistic_totals.config_stock_recapitulation_id');
            })
            ->select(
                'config_stock_recapitulations.id',
                'config_stock_recapitulations.name as jenis_barang',
                'config_stock_recapitulations.type',
                DB::raw('CASE
                    WHEN config_stock_recapitulations.type = "EXTERNAL"
                    THEN COALESCE(SUM(stock_recapitulation.recapitulation_amount), 0)
                    ELSE COALESCE(logistic_totals.total_value, 0)
                END as jumlah_harga')
            )
            ->where('config_stock_recapitulations.active', 1);

        // Filter berdasarkan logistic_type
        if ($request->logistic_type === 'internal') {
            $query->where('config_stock_recapitulations.type', 'LOGISTIC');
        } elseif ($request->logistic_type === 'external') {
            $query->where('config_stock_recapitulations.type', 'EXTERNAL');
        }

        $data = $query->groupBy(
                'config_stock_recapitulations.id',
                'config_stock_recapitulations.name',
                'config_stock_recapitulations.type',
                'logistic_totals.total_value'
            )
            ->orderBy('config_stock_recapitulations.name')
            ->get();

        return Excel::download(new LogisticRecapExport($data, $params), 'Rekap Logistik.xls');
    }

    public function exportStockOpnamePdf(Request $request)
    {
        if ($request->has('periode')) {
            list($start, $end) = explode(' - ', $request->input('periode'));
            $startDate = Carbon::createFromFormat('F d, Y', trim($start))->startOfDay();
            $endDate = Carbon::createFromFormat('F d, Y', trim($end))->endOfDay();
        } else {
            $startDate = Carbon::now()->subMonth()->startOfDay();
            $endDate = Carbon::now()->endOfDay();
        }

        $params = [
            "start" => $startDate->format('Y-m-d'),
            "end" => $endDate->format('Y-m-d')
        ];

        // Add stock recap name if selected
        if ($request->stock_recap_id) {
            $stockRecap = ConfigStockRecap::find($request->stock_recap_id);
            $params['stock_recap_name'] = $stockRecap ? $stockRecap->name : '';
        }

        $data = DB::table('assets as a')
            ->join('asset_entries as ae', 'a.asset_entry_id', '=', 'ae.id')
            ->selectRaw("
                a.id,
                a.asset_name,
                a.qr_code,
                a.unit_price,
                IFNULL(b.total_in, 0) - IFNULL(b.total_out, 0) AS qty_saldo_awal_asset,
                IFNULL(c.total_in, 0) AS qty_asset_masuk,
                IFNULL(c.total_out, 0) AS qty_asset_keluar,
                d.latest_year AS tahun_terakhir
            ")
            ->leftJoinSub(
                DB::table('logistic_details as a')
                    ->select('a.asset_id')
                    ->selectRaw("
                    SUM(CASE WHEN a.logistic_type = 'IN' THEN a.quantity ELSE 0 END) AS total_in,
                    SUM(CASE WHEN a.logistic_type = 'OUT' THEN a.quantity ELSE 0 END) AS total_out
                ")
                    ->join('logistics as lg', 'lg.id', '=', 'a.logistic_id')
                    ->where('lg.logistic_date', '<', $startDate)
                    ->groupBy('a.asset_id'),
                'b',
                'b.asset_id',
                '=',
                'a.id'
            )
            ->leftJoinSub(
                DB::table('logistic_details as a')
                    ->select('a.asset_id')
                    ->selectRaw("
                    SUM(CASE WHEN a.logistic_type = 'IN' THEN a.quantity ELSE 0 END) AS total_in,
                    SUM(CASE WHEN a.logistic_type = 'OUT' THEN a.quantity ELSE 0 END) AS total_out
                ")
                    ->join('logistics as lg', 'lg.id', '=', 'a.logistic_id')
                    ->whereBetween('lg.logistic_date', [$startDate, $endDate])
                    ->groupBy('a.asset_id'),
                'c',
                'c.asset_id',
                '=',
                'a.id'
            )
            ->leftJoinSub(
                DB::table('logistic_details as ld')
                    ->select('ld.asset_id')
                    ->selectRaw('YEAR(MAX(lg.logistic_date)) as latest_year')
                    ->where('ld.logistic_type', 'IN')
                    ->join('logistics as lg', 'lg.id', '=', 'ld.logistic_id')
                    ->groupBy('ld.asset_id'),
                'd',
                'd.asset_id',
                '=',
                'a.id'
            )
            ->where('a.category_type', 'LOGISTIC')
            ->when($request->stock_recap_id, function ($query) use ($request) {
                return $query->where('ae.config_stock_recapitulation_id', $request->stock_recap_id);
            })
            ->get()
            ->when($request->total_keluar === 'GT_ZERO', function ($collection) {
                return $collection->filter(function ($item) {
                    return $item->qty_asset_keluar > 0;
                });
            });

        $pdf = Pdf::loadView('components.logistic.report-stock-opname-pdf', [
            'data' => $data,
            'params' => $params
        ])->setPaper('a4', 'portrait');

        return $pdf->download('stock-opname-' . date('Y-m-d') . '.pdf');
    }

    public function exportStockOpnameExcel(Request $request)
    {
        if ($request->has('periode')) {
            list($start, $end) = explode(' - ', $request->input('periode'));
            $startDate = Carbon::createFromFormat('F d, Y', trim($start))->startOfDay();
            $endDate = Carbon::createFromFormat('F d, Y', trim($end))->endOfDay();
        } else {
            $startDate = Carbon::now()->subMonth()->startOfDay();
            $endDate = Carbon::now()->endOfDay();
        }

        $params = [
            "start" => $startDate->format('Y-m-d'),
            "end" => $endDate->format('Y-m-d')
        ];

        if ($request->stock_recap_id) {
            $stockRecap = ConfigStockRecap::find($request->stock_recap_id);
            $params['stock_recap_name'] = $stockRecap ? $stockRecap->name : '';
        }

        $data = DB::table('assets as a')
            ->join('asset_entries as ae', 'a.asset_entry_id', '=', 'ae.id')
            ->selectRaw("
                a.id,
                a.asset_name,
                a.qr_code,
                a.unit_price,
                IFNULL(b.total_in, 0) - IFNULL(b.total_out, 0) AS qty_saldo_awal_asset,
                IFNULL(c.total_in, 0) AS qty_asset_masuk,
                IFNULL(c.total_out, 0) AS qty_asset_keluar,
                d.latest_year AS tahun_terakhir
            ")
            ->leftJoinSub(
                DB::table('logistic_details as a')
                    ->select('a.asset_id')
                    ->selectRaw("
                    SUM(CASE WHEN a.logistic_type = 'IN' THEN a.quantity ELSE 0 END) AS total_in,
                    SUM(CASE WHEN a.logistic_type = 'OUT' THEN a.quantity ELSE 0 END) AS total_out
                ")
                    ->join('logistics as lg', 'lg.id', '=', 'a.logistic_id')
                    ->where('lg.logistic_date', '<', $startDate)
                    ->groupBy('a.asset_id'),
                'b',
                'b.asset_id',
                '=',
                'a.id'
            )
            ->leftJoinSub(
                DB::table('logistic_details as a')
                    ->select('a.asset_id')
                    ->selectRaw("
                    SUM(CASE WHEN a.logistic_type = 'IN' THEN a.quantity ELSE 0 END) AS total_in,
                    SUM(CASE WHEN a.logistic_type = 'OUT' THEN a.quantity ELSE 0 END) AS total_out
                ")
                    ->join('logistics as lg', 'lg.id', '=', 'a.logistic_id')
                    ->whereBetween('lg.logistic_date', [$startDate, $endDate])
                    ->groupBy('a.asset_id'),
                'c',
                'c.asset_id',
                '=',
                'a.id'
            )
            ->leftJoinSub(
                DB::table('logistic_details as ld')
                    ->select('ld.asset_id')
                    ->selectRaw('YEAR(MAX(lg.logistic_date)) as latest_year')
                    ->where('ld.logistic_type', 'IN')
                    ->join('logistics as lg', 'lg.id', '=', 'ld.logistic_id')
                    ->groupBy('ld.asset_id'),
                'd',
                'd.asset_id',
                '=',
                'a.id'
            )
            ->where('a.category_type', 'LOGISTIC')
            ->when($request->stock_recap_id, function ($query) use ($request) {
                return $query->where('ae.config_stock_recapitulation_id', $request->stock_recap_id);
            })
            ->get()
            ->when($request->total_keluar === 'GT_ZERO', function ($collection) {
                return $collection->filter(function ($item) {
                    return $item->qty_asset_keluar > 0;
                });
            });

        return Excel::download(new StockOpnameExport($data, $params), 'Stock Opname ' . date('Y-m-d') . '.xlsx');
    }

    function inout(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Penerimaan dan Pengeluaran Barang - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            list($start, $end) = explode(" - ", request("periode"));
            $startDate = Carbon::parse($start)->format('Y-m-d');
            $endDate = Carbon::parse($end)->addDay(1)->format('Y-m-d');

            $params = [
                "start" => $startDate,
                "end" => $endDate,
            ];

            // Data Incoming (Penerimaan)
            $dataIncoming = DB::table('logistic_details')
                ->join('logistics', 'logistic_details.logistic_id', '=', 'logistics.id')
                ->join('assets', 'logistic_details.asset_id', '=', 'assets.id')
                ->leftjoin('distributors', 'logistics.distributor_id', '=', 'distributors.id')
                ->select(
                    'logistics.logistic_number',
                    'logistics.logistic_date',
                    'logistics.logistic_notes',
                    'distributors.distributor_name',
                    'assets.asset_code',
                    'assets.asset_name',
                    'logistic_details.quantity',
                    'logistic_details.unit_price',
                    'logistic_details.total_price',
                    'logistic_details.uom_name'
                )
                ->where('logistics.logistic_type', 'IN')
                ->whereBetween('logistics.logistic_date', [$startDate, $endDate])
                ->get();

            // Data Outgoing (Pengeluaran)
            $data = DB::table('logistic_details')
                ->join('logistics', 'logistic_details.logistic_id', '=', 'logistics.id')
                ->join('assets', 'logistic_details.asset_id', '=', 'assets.id')
                ->leftJoin('rooms', 'logistics.room_id', '=', 'rooms.id')
                ->select(
                    'logistics.logistic_number',
                    'logistics.logistic_date',
                    'rooms.room_name',
                    'assets.asset_code',
                    'assets.asset_name',
                    'logistic_details.quantity',
                    'logistic_details.unit_price',
                    'logistic_details.total_price',
                    'logistic_details.uom_name'
                )
                ->where('logistics.logistic_type', 'OUT')
                ->whereBetween('logistics.logistic_date', [$startDate, $endDate])
                ->get();

            return view('components.logistic.report-inout', [
                "params" => $params, 
                "data" => $data,
                "dataIncoming" => $dataIncoming
            ]);
        }

        $title = "Laporan Penerimaan & Pengeluaran Barang";
        $breadcrumbs = ["Logistik", "Laporan Penerimaan & Pengeluaran Barang"];
        $exportRoute = route('logistic.report-logistic.inout.export');

        return view('logistic.report.inout', compact('title', 'breadcrumbs', 'exportRoute'));
    }

    public function inoutExport(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Penerimaan dan Pengeluaran Barang - View')) {
            abort(403, "Unauthorized action.");
        }
        $startDate = Carbon::parse($request->start_date)->format('Y-m-d');
        $endDate = Carbon::parse($request->end_date)->addDay(1)->format('Y-m-d');

        $params = [
            "start" => $startDate,
            "end" => $endDate,
        ];

        // Data Incoming (Penerimaan)
        $dataIncoming = DB::table('logistic_details')
            ->join('logistics', 'logistic_details.logistic_id', '=', 'logistics.id')
            ->join('assets', 'logistic_details.asset_id', '=', 'assets.id')
            ->leftjoin('distributors', 'logistics.distributor_id', '=', 'distributors.id')
            ->select(
                'logistics.logistic_number',
                'logistics.logistic_date',
                'logistics.logistic_notes',
                'distributors.distributor_name',
                'assets.asset_code',
                'assets.asset_name',
                'logistic_details.quantity',
                'logistic_details.unit_price',
                'logistic_details.total_price',
                'logistic_details.uom_name'
            )
            ->where('logistics.logistic_type', 'IN')
            ->whereBetween('logistics.logistic_date', [$startDate, $endDate])
            ->get();

        // Data Outgoing (Pengeluaran)
        $data = DB::table('logistic_details')
            ->join('logistics', 'logistic_details.logistic_id', '=', 'logistics.id')
            ->join('assets', 'logistic_details.asset_id', '=', 'assets.id')
            ->leftJoin('rooms', 'logistics.room_id', '=', 'rooms.id')
            ->select(
                'logistics.logistic_number',
                'logistics.logistic_date',
                'rooms.room_name',
                'assets.asset_code',
                'assets.asset_name',
                'logistic_details.quantity',
                'logistic_details.unit_price',
                'logistic_details.total_price',
                'logistic_details.uom_name'
            )
            ->where('logistics.logistic_type', 'OUT')
            ->whereBetween('logistics.logistic_date', [$startDate, $endDate])
            ->get();

        return Excel::download(
            new InOutLogisticsExport($data, $dataIncoming, $params), 
            'Laporan Penerimaan dan Pengeluaran Barang.xlsx'
        );
    }

    public function exportKartuStock(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Kartu Stock - View')) {
            abort(403, "Unauthorized action.");
        }

        $startDate = Carbon::parse($request->start_date)->format('Y-m-d');
        $endDate = Carbon::parse($request->end_date)->format('Y-m-d');

        $params = [
            "start" => $startDate,
            "end" => $endDate,
        ];

        $summary = DB::table('assets as a')
            ->selectRaw("
                a.id, a.asset_name, a.qr_code, a.unit_price,
                IFNULL(b.total_in, 0) - IFNULL(b.total_out, 0) AS qty_saldo_awal_asset,
                IFNULL(c.total_in, 0) AS qty_asset_masuk,
                IFNULL(c.total_out, 0) AS qty_asset_keluar
            ")
            ->leftJoinSub(
                DB::table('logistic_details as a')
                    ->select('a.asset_id')
                    ->selectRaw("
                    SUM(CASE WHEN a.logistic_type = 'IN' THEN a.quantity ELSE 0 END) AS total_in,
                    SUM(CASE WHEN a.logistic_type = 'OUT' THEN a.quantity ELSE 0 END) AS total_out
                ")
                    ->join('logistics as lg', 'lg.id', '=', 'a.logistic_id')
                    ->where('lg.logistic_date', '<', $startDate)
                    ->groupBy('a.asset_id'),
                'b',
                'b.asset_id',
                '=',
                'a.id'
            )
            ->leftJoinSub(
                DB::table('logistic_details as a')
                    ->select('a.asset_id')
                    ->selectRaw("
                    SUM(CASE WHEN a.logistic_type = 'IN' THEN a.quantity ELSE 0 END) AS total_in,
                    SUM(CASE WHEN a.logistic_type = 'OUT' THEN a.quantity ELSE 0 END) AS total_out
                ")
                    ->join('logistics as lg', 'lg.id', '=', 'a.logistic_id')
                    ->whereBetween('lg.logistic_date', [$startDate, $endDate])
                    ->groupBy('a.asset_id'),
                'c',
                'c.asset_id',
                '=',
                'a.id'
            )
            ->where('a.category_type', 'LOGISTIC')
            ->where("a.id", $request->asset)
            ->first();

        $details = LogisticDetail::with(["logistic", "asset"])
            ->select('logistic_details.*', 'rooms.room_code', 'rooms.room_name')
            ->join('logistics', 'logistics.id', '=', 'logistic_details.logistic_id')
            ->leftJoin('rooms', 'rooms.id', '=', 'logistics.room_id')
            ->whereHas("logistic", function ($query) use ($startDate, $endDate) {
                $query->whereBetween("logistic_date", [$startDate, $endDate]);
            })
            ->where(["asset_id" => $request->asset])
            ->get();

        return Excel::download(new KartuStockExport($summary, $details, $params), 'Kartu Stock.xls');
    }

}
