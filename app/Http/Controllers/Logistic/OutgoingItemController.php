<?php

namespace App\Http\Controllers\Logistic;

use App\Http\Controllers\Controller;
use App\Models\Room;
use App\Models\Logistic;
use App\Models\LogisticDetail;
use App\Models\Employee;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;

class OutgoingItemController extends Controller
{
    protected $logisticType;

    public function __construct()
    {
        $this->logisticType = "OUT";
    }

    function index()
    {
        if (!hasPermissionInGuard('Barang Keluar - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Barang Keluar";
        $breadcrumbs = ["Logistik", "Barang Keluar"];
        return view('logistic.outgoing-item.index', compact('title', 'breadcrumbs'));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Barang Keluar - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $query = Logistic::getLogisticQueryGroupByDay($this->logisticType, $request->room_id);
            
            // Add date filter
            if ($request->has('start_date') && $request->has('end_date')) {
                $query->whereBetween('logistics.logistic_date', [
                    Carbon::parse($request->start_date)->startOfDay(),
                    Carbon::parse($request->end_date)->endOfDay()
                ]);
            }
            
            return DataTables::eloquent($query)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return '
                        <a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-detail" data-bs-toggle="modal" data-route="' . route("logistic.outgoing-asset.show", $row->id) . '"><i class="fas fa-search"></i></a>';
                })
                ->rawColumns(["action"])
                ->make(true);
        }
    }

    function destroy(Logistic $logistic) {}

    private function convertLessThanOneThousand($n, $units, $teens, $tens) 
    {
        if ($n < 0) {
            return 'minus ' . $this->convertLessThanOneThousand(abs($n), $units, $teens, $tens);
        }
        
        if ($n < 10) {
            return $units[$n];
        }
        if ($n < 20) {
            return $teens[$n - 10];
        }
        if ($n < 100) {
            return $tens[(int)($n / 10)] . ($n % 10 !== 0 ? ' ' . $units[$n % 10] : '');
        }
        $hundreds = (int)($n / 100);
        $remainder = $n % 100;
        return ($hundreds === 1 ? 'seratus' : $units[$hundreds] . ' ratus') 
               . ($remainder !== 0 ? ' ' . $this->convertLessThanOneThousand($remainder, $units, $teens, $tens) : '');
    }

    private function numberToWords($number)
    {
        if ($number < 0) {
            return 'minus ' . $this->numberToWords(abs($number));
        }
        
        $units = ['', 'satu', 'dua', 'tiga', 'empat', 'lima', 'enam', 'tujuh', 'delapan', 'sembilan'];
        $teens = ['sepuluh', 'sebelas', 'dua belas', 'tiga belas', 'empat belas', 'lima belas', 'enam belas', 'tujuh belas', 'delapan belas', 'sembilan belas'];
        $tens = ['', '', 'dua puluh', 'tiga puluh', 'empat puluh', 'lima puluh', 'enam puluh', 'tujuh puluh', 'delapan puluh', 'sembilan puluh'];

        if ($number === 0) {
            return 'nol';
        }

        if ($number < 1000) {
            return $this->convertLessThanOneThousand($number, $units, $teens, $tens);
        }
        if ($number < 1000000) {
            $thousands = (int)($number / 1000);
            $remainder = $number % 1000;
            return ($thousands === 1 ? 'seribu' : $this->convertLessThanOneThousand($thousands, $units, $teens, $tens) . ' ribu')
                   . ($remainder !== 0 ? ' ' . $this->convertLessThanOneThousand($remainder, $units, $teens, $tens) : '');
        }
        if ($number < 1000000000) {
            $millions = (int)($number / 1000000);
            $remainder = $number % 1000000;
            return $this->convertLessThanOneThousand($millions, $units, $teens, $tens) . ' juta'
                   . ($remainder !== 0 ? ' ' . $this->numberToWords($remainder) : '');
        }
        $billions = (int)($number / 1000000000);
        $remainder = $number % 1000000000;
        return $this->convertLessThanOneThousand($billions, $units, $teens, $tens) . ' milyar'
               . ($remainder !== 0 ? ' ' . $this->numberToWords($remainder) : '');
    }

    private function getLogisticDetail($roomId, $logisticDate)
    {
        $logisticDetail = LogisticDetail::select(
            'logistics.logistic_number',
            'logistic_details.*',
            'assets.qr_code',
            'assets.register_code',
            'assets.serial_number',
            'assets.asset_code',
            'assets.asset_name',
            'items.item_code',
            'items.item_name'
        )
            ->join("assets", "assets.id", "=", "logistic_details.asset_id")
            ->join("items", "items.id", "=", "assets.item_id")
            ->join("logistics", "logistics.id", "=", "logistic_details.logistic_id")
            ->where("logistics.logistic_date", $logisticDate)
            ->where("logistics.room_id", $roomId)
            ->get()
            ->map(function ($item) {
                $item->quantity_in_words = $this->numberToWords($item->quantity);
                return $item;
            });

        $listUniqueLogistic = $logisticDetail->pluck('logistic_number')
            ->unique()
            ->implode(', ');

        return [
            'logisticDetail' => $logisticDetail,
            'listUniqueLogistic' => $listUniqueLogistic
        ];
    }

    private function getLogistic($logisticId)
    {
        return Logistic::select('logistics.*', 'rooms.room_code', 'rooms.room_name')
            ->join("rooms", "rooms.id", "=", "logistics.room_id")
            ->where("logistics.id", $logisticId)
            ->first();
    }

    function show(Logistic $logistic, Request $request)
    {
        if (!hasPermissionInGuard('Barang Keluar - View')) {
            abort(403, "Unauthorized action.");
        }

        $roomId = $request->query('room_id');
        $logisticDate = $request->query('logistic_date');

        $logisticDetail = $this->getLogisticDetail($roomId, $logisticDate);
        $logistic = $this->getLogistic($logistic->id);
        $roomData = Room::leftJoin('employees', 'rooms.pic_room', '=', 'employees.id')->where("rooms.id", $roomId)->first();
        $recipientName = ($roomData) ? $roomData->employee_name : "";
        $partyOne = Employee::where("pic_type", "PIC_PENGURUS_BARANG")->first();
        $partyTwo = Employee::where("pic_type", "KETUA_TEAM_ASET")->first();
    
        return view('components/logistic/detail-outgoing-logistic', [
            "logistic" => $logistic,
            "logisticDetail" => $logisticDetail['logisticDetail'],
            "listUniqueLogistic" => $logisticDetail['listUniqueLogistic'],
            "recipientName" => $recipientName,
            "goodsManager" => $partyOne->employee_name,
            "leadAsset" => $partyTwo->employee_name
        ]);
    }

    public function print(Request $request)
    {
        $roomId = $request->query('room_id');
        $logisticDate = $request->query('logistic_date');

        if (!$roomId || !$logisticDate) {
            abort(404, 'Data tidak ditemukan');
        }
        
        $logisticDetail = $this->getLogisticDetail($roomId, $logisticDate);
        $logistic = $this->getLogistic($logisticDetail['logisticDetail'][0]->logistic_id);
        $roomData = Room::leftJoin('employees', 'rooms.pic_room', '=', 'employees.id')->where("rooms.id", $roomId)->first();
        $recipientName = ($roomData) ? $roomData->employee_name : "";
        $recipientNIP = ($roomData) ? $roomData->employee_identification_number : "";
        $partyOne = Employee::where("pic_type", "PIC_PENGURUS_BARANG")->first();
        $partyTwo = Employee::where("pic_type", "KETUA_TEAM_ASET")->first();
        
        $pdf = Pdf::loadView('components/logistic/print-detail-outgoing-logistic', [
            "logistic" => $logistic,
            "logisticDetail" => $logisticDetail['logisticDetail'],
            "listUniqueLogistic" => $logisticDetail['listUniqueLogistic'],
            "recipientName" => $recipientName,
            "recipientNIP" => $recipientNIP,
            "goodsManager" => $partyOne->employee_name,
            "goodsManagerNIP" => $partyOne->employee_identification_number,
            "leadAsset" => $partyTwo->employee_name,
            "leadAssetNIP" => $partyTwo->employee_identification_number
        ])->setPaper('a4', 'portrait');

        return $pdf->download('Barang_Keluar_' . $logistic->room_code . '.pdf');
    }
}
