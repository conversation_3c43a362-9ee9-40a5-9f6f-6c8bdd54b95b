<?php

namespace App\Http\Controllers\Logistic;

use Illuminate\Http\Request;
use App\Models\ConfigStockRecap;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;

class StockRecapController extends Controller
{
    function index()
    {

        $title = "Stock Opname Non Logistik";
        $breadcrumbs = ["Logistik", "Stock Opname Non Logistik"];

        return view('logistic.stock-recap.index', compact('title', 'breadcrumbs'));
    }

    function dropdown_config()
    {
        $configs = ConfigStockRecap::where("type", "LOGISTIC")->paginate(25);

        return response()->json($configs);
    }

    function dropdown_config_recap()
    {
        $configs = ConfigStockRecap::where("type", "EXTERNAL")->paginate(25);

        return response()->json($configs);
    }

    public function create()
    {
        $title = "Rekapitulasi Stock";
        $breadcrumbs = ["Logistik", "Rekapitulasi Stock"];
        return view("logistic.stock-recap.create", compact("title", "breadcrumbs"));
    }

    public function list(Request $request)
    {
        if ($request->ajax()) {
            Log::info($request->tipe_id);
            $data = DB::table('stock_recapitulation')
                ->join('config_stock_recapitulations', 'stock_recapitulation.config_stock_recapitulation_id', '=', 'config_stock_recapitulations.id')
                ->select(
                    'stock_recapitulation.id',
                    'config_stock_recapitulations.name as tipe_rekap',
                    'stock_recapitulation.recapitulation_date',
                    'stock_recapitulation.recapitulation_title',
                    'stock_recapitulation.recapitulation_amount',
                    'stock_recapitulation.recapitulation_file_path'
                )
                ->where('config_stock_recapitulations.type', 'EXTERNAL')
                ->where('config_stock_recapitulations.active', 1);

            if ($request->has('tipe_id') && $request->tipe_id) {
                $data->where('config_stock_recapitulations.id', $request->tipe_id);
            }
            
            // Add date filter
            if ($request->has('filter_date_start') && $request->filter_date_start != '') {
                $data->where('stock_recapitulation.recapitulation_date', '>=', $request->filter_date_start);
            }
            if ($request->has('filter_date_end') && $request->filter_date_end != '') {
                $data->where('stock_recapitulation.recapitulation_date', '<=', $request->filter_date_end);
            }
            
            return DataTables::of($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return '
                        <a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-detail" data-bs-toggle="modal" data-route="' . route("logistic.stock-recap.show", $row->id) . '"><i class="fas fa-search"></i></a>
                        <button type="button" class="btn btn-sm btn-outline-danger btn-delete" data-route="' . route("logistic.stock-recap.destroy", $row->id) . '"><i class="fas fa-trash"></i></button>';
                })
                ->editColumn('recapitulation_amount', function ($row) {
                    return number_format($row->recapitulation_amount, 2);
                })
                ->rawColumns(['action'])
                ->make(true);
        }
    }

    public function store(Request $request)
    {
        $userId = getAuthUserId();
        $userName = getAuthUserName();

        try {
            DB::beginTransaction();

            $file_docs = $request->file("document");
            $fileDocsUrl = null;

            if ($file_docs) {
                $folder = "logistic/rekapitulation";
                $fileDocsUrl = $file_docs->storeAs($folder, now()->format("YmdHis") . "_logistic_rekapitulation" . "." . $file_docs->extension(), 'public');
            }

            DB::table('stock_recapitulation')->insert([
                "config_stock_recapitulation_id" => $request->tipe_rekap,
                "recapitulation_date" => $request->request_date,
                "recapitulation_title" => $request->judul_rekap,
                "recapitulation_notes" => $request->notes,
                "recapitulation_file_path" => $fileDocsUrl,
                "recapitulation_amount" => $request->total_nilai_rekapitulasi,
                "created_by" => $userId,
                "created_by_name" => $userName,
                "created_at" => now(),
            ]);

            DB::commit();

            return response()->json(["message" => "Data berhasil disimpan"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(['message' => 'Terjadi kesalahan saat menyimpan data.'], 400);
        }
    }


    public function destroy($id)
    {
        try {
            DB::beginTransaction();

            $filePath = DB::table('stock_recapitulation')
                ->where('id', $id)
                ->value('recapitulation_file_path');

            if ($filePath && Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
            }

            DB::table('stock_recapitulation')
                ->where('id', $id)
                ->delete();

            DB::commit();

            return response()->json([
                "message" => "Data berhasil dihapus"
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function show($id)
    {
        $logistic = DB::table('stock_recapitulation')
            ->join('config_stock_recapitulations', 'stock_recapitulation.config_stock_recapitulation_id', '=', 'config_stock_recapitulations.id')
            ->where('stock_recapitulation.id', $id)
            ->select('stock_recapitulation.*', 'config_stock_recapitulations.name as recap_type')
            ->first();

        return view('components.logistic.detail-stock-recap-logistic', [
            'logistic' => $logistic,
        ]);
    }
}
