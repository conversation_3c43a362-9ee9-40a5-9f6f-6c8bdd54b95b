<?php

namespace App\Http\Controllers\Logistic;

use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Controllers\Controller;
use App\Imports\AssetLogisticImport;
use App\Imports\IncomingAssetImport;
use App\Models\Asset;
use App\Models\AssetEntry;
use App\Models\ConfigStockField;
use App\Models\Item;
use App\Models\Uom;
use App\Exports\AssetLogisticExport;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;

class AssetLogisticController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Daftar Aset Logistik - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Daftar Barang Logistik";
        $breadcrumbs = ["Logistik", "Daftar Barang Logistik"];

        return view('logistic.asset.index', compact('title', 'breadcrumbs'));
    }

    function print_label()
    {
        $title = "Print Label BHP";
        $breadcrumbs = ["Logistik", "Print Label BHP"];

        return view('logistic.asset.print-label', compact('title', 'breadcrumbs'));
    }

    function showAssetLabel(AssetEntry $assetEntry)
    {
        $assetEntry->load(['item']);

        $response = '<div class="mb-3" style="min-width: 90mm !important; max-width: 90mm !important; min-height: 25mm !important">
                <div class="card">
                    <div class="card-body border border-1 border-dark rounded d-flex align-items-center p-1" style="height: 25mm;">
                        <div class="border-end border-2 border-dark text-center d-flex align-items-center justify-content-center" style="width: 30%; min-height: 100%; padding: 0px;">
                            <div class="d-block qr-container" data-qr-text="' . $assetEntry->asset_entry_code . '">
                                <!-- QR Code akan di-generate di client-side -->
                            </div>
                        </div>

                        <div class="text-center d-flex flex-column justify-content-center" style="width: 70%; padding-left: 3px;">
                            <div class="d-block fw-800 fs-18px mb-1">' . config('app.report_header_hospital') . '</div>
                            <div class="d-block border-bottom border-3 border-dark mb-1"><strong>' . $assetEntry->asset_entry_code . '</strong></div>
                            <div class="d-block fw-bold">' . $assetEntry->asset_name . '</div>
                        </div>
                    </div>
                </div>
            </div>';

        $qrCode = '<div style="">
            <div class="card">
                <div class="card-body border border-1 border-dark rounded p-2 text-center">
                    <div class="d-block qr-container" data-qr-text="' . $assetEntry->asset_entry_code . '">
                        <!-- QR Code akan di-generate di client-side -->
                    </div>
                    <div class="d-block mt-2">
                        <div class="d-block fw-bold">' . $assetEntry->asset_name . '</div>
                        <div class="d-block">' . $assetEntry->asset_entry_code . '</div>
                    </div>
                </div>
            </div>
        </div>';

        return response()->json([
            'data' => $assetEntry,
            'label' => $response,
            'qrcode' => $qrCode
        ], 200);
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Daftar Aset Logistik - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $query = AssetEntry::join("config_stock_fields", "config_stock_fields.id", "=", "asset_entries.config_stock_field_id")
                ->join("config_stock_recapitulations", "config_stock_recapitulations.id", "=", "asset_entries.config_stock_recapitulation_id")
                ->join("items", "items.id", "=", "asset_entries.item_id")
                ->select('asset_entries.*', 'asset_entries.default_image', 'config_stock_fields.name AS stock_name', 'config_stock_recapitulations.name AS recapitulation_name', 'items.item_name', 'items.item_code')
                ->where("asset_entries.category_type", "LOGISTIC");

            // Filter by kode barang (item_id)
            if ($request->has('filter_kode_barang') && $request->filter_kode_barang !== '' && $request->filter_kode_barang !== null) {
                $query->where('asset_entries.item_id', $request->filter_kode_barang);
            }

            // Filter by status
            if ($request->has('filter_status') && $request->filter_status !== '' && $request->filter_status !== null) {
                if ($request->filter_status === 'active') {
                    $query->where('asset_entries.active', 1);
                } elseif ($request->filter_status === 'inactive') {
                    $query->where('asset_entries.active', 0);
                }
            }

            return DataTables::eloquent($query)
                ->addIndexColumn()
                ->filter(function ($query) {
                    if (request()->has('search') && request('search')['value']) {
                        $searchValue = request('search')['value'];
                        $query->where(function ($q) use ($searchValue) {
                            $q->where('asset_entries.asset_entry_code', 'like', '%' . $searchValue . '%')
                                ->orWhere('asset_entries.asset_name', 'like', '%' . $searchValue . '%')
                                ->orWhere('config_stock_fields.name', 'like', '%' . $searchValue . '%')
                                ->orWhere('config_stock_recapitulations.name', 'like', '%' . $searchValue . '%');
                        });
                    }
                })
                ->addColumn("default_image", function ($row) {
                    if ($row->default_image) {
                        return '<img src="' . asset('/storage/' . $row->default_image) . '" width="70px" height="70px" style="cursor: pointer;" class="img-fluid text-center img-asset-logistic">';
                    }

                    return "<span class='text-info' style='font-size: 10px;'>Belum ada foto</span>";
                })
                ->addColumn("qr_code", function ($row) {
                    return '<div class="text-center qr-code-container" style="cursor: pointer;" data-qr-code="' . $row->asset_entry_code . '">
                        <div class="qr-code-placeholder" style="width: 40px; height: 40px; border: 1px solid #ddd; border-radius: 4px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; margin: 0 auto;">
                            <i class="fas fa-qrcode text-primary"></i>
                        </div>
                    </div>';
                })
                ->addColumn("status", function ($row) {
                    return $row->active == 1 ? '<span class="badge bg-primary">Aktif</span>' : '<span class="badge bg-danger">Tidak Aktif</span>';
                })
                ->addColumn("action", function ($row) {
                    return '
                        <a href="#modal-dialog" class="btn btn-sm btn-outline-primary btn-detail" data-bs-toggle="modal" data-route="' . route("logistic.asset.show", $row->id) . '"><i class="fas fa-search"></i> Lihat Detail</a> 
                        <button class="btn btn-sm btn-outline-danger btn-delete" data-route="' . route("logistic.asset.destroy", $row->id) . '"><i class="fas fa-trash"></i> Hapus Data </button>
                        <button class="btn btn-sm btn-outline-warning btn-status" data-id="' . $row->id . '"><i class="fas fa-toggle-on"></i> Ubah Status</button>';
                })
                ->rawColumns(["action", "status", "qr_code", "default_image"])
                ->make(true);
        }
    }

    function dropdown()
    {
        $data = AssetEntry::select(
            'asset_entries.id',
            'asset_entries.asset_name',
            'asset_entries.asset_entry_code',
            'items.item_code',
            'items.item_name'
        )
            ->join('items', 'items.id', '=', 'asset_entries.item_id');
        if (request()->has('q')) {
            $search = request()->get('q');
            $data = $data->where(function ($query) use ($search) {
                $query->where('asset_entries.asset_entry_code', 'like', '%' . $search . '%')
                    ->orWhere('asset_entries.asset_name', 'like', '%' . $search . '%')
                    ->orWhere('items.item_name', 'like', '%' . $search . '%')
                    ->orWhere('items.item_code', 'like', '%' . $search . '%');
            });
        }

        if (request()->has('category_type')) {
            $data = $data->where('asset_entries.category_type', request()->get('category_type'));
        }
        if (request()->has('active')) {
            $data = $data->where('asset_entries.active', "1");
        }
        if (request()->has('balance_qty')) {
            $data = $data->where('asset_entries.latest_balance', '>', 0);
        }
        if (request()->has('config_stock_field_id')) {
            $data = $data->where('asset_entries.config_stock_field_id', request()->get('config_stock_field_id'));
        }

        $assetEntries = $data->paginate(25);
        return response()->json($assetEntries);
    }

    function create()
    {
        if (!hasPermissionInGuard('Daftar Aset Logistik - Action')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Tambah Data Aset Logistik";
        $breadcrumbs = ["Logistik", "Data Aset", "Tambah Data Aset Logistik"];

        $dataConfigStockField = ConfigStockField::where("type", "LOGISTIC")->where("active", 1)->get();

        return view('logistic.asset.create', compact('title', 'breadcrumbs', 'dataConfigStockField'));
    }

    public function store(Request $request)
    {
        if (!hasPermissionInGuard('Daftar Aset Logistik - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "kode_barang" => "required",
            "stok_rekapitulasi" => "required",
            "satuan" => "required",
            "nama_asset" => "required",
            "bidang_permintaan" => "required",
            "kode_register" => "required|max:3"
        ]);

        try {
            DB::beginTransaction();

            $item = Item::find($request->kode_barang);
            $uom = Uom::find($request->satuan);

            // Generate asset_entry_code
            $asset_entry_code = $item->item_code . "." . $request->kode_register;

            // Check if asset_entry_code already exists
            $existingAsset = AssetEntry::where('asset_entry_code', $asset_entry_code)->first();
            if ($existingAsset) {
                return response()->json([
                    "message" => "Kode Asset Logistik '" . $asset_entry_code . "' sudah ada dalam sistem. Silakan gunakan kode register yang berbeda.",
                ], 422);
            }

            AssetEntry::create([
                "item_id" => $item->id,
                "item_name" => $item->item_name,
                "category_type" => "LOGISTIC",
                "category_id" => $item->category_id,
                "config_stock_recapitulation_id" => $request->stok_rekapitulasi,
                "config_stock_field_id" => $request->bidang_permintaan,
                "uom_id" => $uom->id,
                "uom_name" => $uom->uom_name,
                "asset_name" => $request->nama_asset,
                "asset_entry_code" => $asset_entry_code,
                "created_by" => getAuthUserId(),
                "created_by_name" => getAuthUserName(),
            ]);

            DB::commit();

            if ($request->add == 1) {
                return response()->json(["message" => "Data berhasil disimpan",], 200);
            }

            return response()->json(["message" => "Data berhasil disimpan", "create" => 0], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(["message" => $th->getMessage(),], 500);
        }
    }

    function destroy(AssetEntry $assetEntry)
    {
        try {
            DB::beginTransaction();

            if ($assetEntry->assets()->count() > 0) {
                return response()->json(["message" => "Data tidak dapat dihapus karena sudah ada asset yang terkait",], 500);
            }

            $assetEntry->delete();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(["message" => $th->getMessage()], 500);
        }
    }

    function example_import()
    {
        return response()->download(public_path('example/example-import-logistic.xlsx'));
    }

    function import(Request $request)
    {
        $request->validate([
            "file" => "required|mimes:xls,xlsx"
        ]);

        try {
            DB::beginTransaction();

            $file = $request->file('file');

            Excel::import(new AssetLogisticImport(), $file);

            DB::commit();
            return response()->json(["message" => "Data berhasil diimport"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(["message" => $th->getMessage()], 500);
        }
    }

    function example_import_incoming()
    {
        return response()->download(public_path('example/example-import-saldo-awal.xlsx'));
    }

    function import_incoming(Request $request)
    {
        $request->validate([
            "tanggal" => "required|date",
            "file" => "required|mimes:xls,xlsx"
        ]);

        try {
            DB::beginTransaction();

            $file = $request->file('file');

            Excel::import(new IncomingAssetImport($request->tanggal), $file);

            DB::commit();
            return response()->json(["message" => "Data berhasil diimport"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(["message" => $th->getMessage()], 500);
        }
    }

    function show(AssetEntry $assetEntry)
    {
        $assets = Asset::where("asset_entry_id", $assetEntry->id)->get();
        return view("logistic.components.asset.show", compact("assetEntry", "assets"));
    }

    function dropdown_item()
    {
        $items = Item::with("category")->whereHas("category", function ($query) {
            $query->where("category_type", "LOGISTIC");
        })
            ->search()
            ->paginate(25);

        return response()->json($items);
    }

    function change_status(AssetEntry $assetEntry)
    {
        try {
            DB::beginTransaction();

            if ($assetEntry->active == 1) {
                $status = 0;
            } else {
                $status = 1;
            }

            $assetEntry->update([
                "active" => $status,
                "updated_by" => getAuthUserId(),
            ]);

            foreach ($assetEntry->assets as $asset) {
                $asset->update([
                    "active" => $status,
                    "updated_by" => getAuthUserId(),
                ]);
            }

            DB::commit();
            return response()->json(["status" => "success", "message" => "Status berhasil diubah",], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(["status" => "error", "message" => $th->getMessage()], 500);
        }
    }

    public function exportExcel(Request $request)
    {
        if (!hasPermissionInGuard('Daftar Aset Logistik - View')) {
            abort(403, "Unauthorized action.");
        }

        $query = AssetEntry::join("config_stock_fields", "config_stock_fields.id", "=", "asset_entries.config_stock_field_id")
            ->join("config_stock_recapitulations", "config_stock_recapitulations.id", "=", "asset_entries.config_stock_recapitulation_id")
            ->select(
                'asset_entries.asset_entry_code',
                'asset_entries.asset_name',
                'asset_entries.uom_name',
                'config_stock_recapitulations.name AS recapitulation_name',
                'config_stock_fields.name AS stock_name',
                'asset_entries.active'
            )
            ->where("asset_entries.category_type", "LOGISTIC");

        // Filter by kode barang (item_id)
        if ($request->has('filter_kode_barang') && $request->filter_kode_barang !== '' && $request->filter_kode_barang !== null) {
            $query->where('asset_entries.item_id', $request->filter_kode_barang);
        }

        // Filter by status
        if ($request->has('filter_status') && $request->filter_status !== '' && $request->filter_status !== null) {
            if ($request->filter_status === 'active') {
                $query->where('asset_entries.active', 1);
            } elseif ($request->filter_status === 'inactive') {
                $query->where('asset_entries.active', 0);
            }
        }

        // Search filter
        if ($request->has('search') && $request->search !== '' && $request->search !== null) {
            $searchValue = $request->search;
            $query->where(function ($q) use ($searchValue) {
                $q->where('asset_entries.asset_entry_code', 'like', '%' . $searchValue . '%')
                    ->orWhere('asset_entries.asset_name', 'like', '%' . $searchValue . '%')
                    ->orWhere('config_stock_fields.name', 'like', '%' . $searchValue . '%')
                    ->orWhere('config_stock_recapitulations.name', 'like', '%' . $searchValue . '%');
            });
        }

        $data = $query->get();

        // Prepare parameters for export
        $params = [];
        if ($request->has('filter_kode_barang') && $request->filter_kode_barang !== '' && $request->filter_kode_barang !== null) {
            $params['filter_kode_barang'] = $request->filter_kode_barang;
        }
        if ($request->has('filter_status') && $request->filter_status !== '' && $request->filter_status !== null) {
            $params['filter_status'] = $request->filter_status;
        }
        if ($request->has('search') && $request->search !== '' && $request->search !== null) {
            $params['search'] = $request->search;
        }

        return Excel::download(new AssetLogisticExport($data, $params), 'Daftar Aset Logistik ' . date('Y-m-d') . '.xlsx');
    }

    public function exportPdf(Request $request)
    {
        if (!hasPermissionInGuard('Daftar Aset Logistik - View')) {
            abort(403, "Unauthorized action.");
        }

        $query = AssetEntry::join("config_stock_fields", "config_stock_fields.id", "=", "asset_entries.config_stock_field_id")
            ->join("config_stock_recapitulations", "config_stock_recapitulations.id", "=", "asset_entries.config_stock_recapitulation_id")
            ->select(
                'asset_entries.asset_entry_code',
                'asset_entries.asset_name',
                'asset_entries.uom_name',
                'config_stock_recapitulations.name AS recapitulation_name',
                'config_stock_fields.name AS stock_name',
                'asset_entries.active'
            )
            ->where("asset_entries.category_type", "LOGISTIC");

        // Filter by kode barang (item_id)
        if ($request->has('filter_kode_barang') && $request->filter_kode_barang !== '' && $request->filter_kode_barang !== null) {
            $query->where('asset_entries.item_id', $request->filter_kode_barang);
        }

        // Filter by status
        if ($request->has('filter_status') && $request->filter_status !== '' && $request->filter_status !== null) {
            if ($request->filter_status === 'active') {
                $query->where('asset_entries.active', 1);
            } elseif ($request->filter_status === 'inactive') {
                $query->where('asset_entries.active', 0);
            }
        }

        // Search filter
        if ($request->has('search') && $request->search !== '' && $request->search !== null) {
            $searchValue = $request->search;
            $query->where(function ($q) use ($searchValue) {
                $q->where('asset_entries.asset_entry_code', 'like', '%' . $searchValue . '%')
                    ->orWhere('asset_entries.asset_name', 'like', '%' . $searchValue . '%')
                    ->orWhere('config_stock_fields.name', 'like', '%' . $searchValue . '%')
                    ->orWhere('config_stock_recapitulations.name', 'like', '%' . $searchValue . '%');
            });
        }

        $data = $query->get();

        // Prepare parameters for export
        $params = [];
        if ($request->has('filter_kode_barang') && $request->filter_kode_barang !== '' && $request->filter_kode_barang !== null) {
            $params['filter_kode_barang'] = $request->filter_kode_barang;
        }
        if ($request->has('filter_status') && $request->filter_status !== '' && $request->filter_status !== null) {
            $params['filter_status'] = $request->filter_status;
        }
        if ($request->has('search') && $request->search !== '' && $request->search !== null) {
            $params['search'] = $request->search;
        }

        $pdf = Pdf::loadView('components.logistic.asset-logistic-pdf', [
            'data' => $data,
            'title' => 'Daftar Aset Logistik',
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'params' => $params
        ])->setPaper('a4', 'portrait');

        return $pdf->download('Daftar Aset Logistik ' . date('Y-m-d') . '.pdf');
    }

    function uploadPhoto(Request $request)
    {
        if (!hasPermissionInGuard('Daftar Aset Logistik - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            'asset_entry_id' => 'required|exists:asset_entries,id',
            'photo' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:10240'
        ]);

        try {
            DB::beginTransaction();

            $assetEntry = AssetEntry::findOrFail($request->asset_entry_id);

            // Upload foto
            $photo = $request->file('photo');
            $photoName = 'asset_entry_' . $assetEntry->id . '_' . time() . '.' . $photo->getClientOriginalExtension();
            $photoPath = $photo->storeAs('asset_entries/photos', $photoName, 'public');

            // Delete old photo if exists
            if ($assetEntry->default_image) {
                Storage::disk('public')->delete($assetEntry->default_image);
            }

            $assetEntry->update([
                'default_image' => $photoPath
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Foto berhasil diupload'
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $th->getMessage()
            ], 500);
        }
    }
}
