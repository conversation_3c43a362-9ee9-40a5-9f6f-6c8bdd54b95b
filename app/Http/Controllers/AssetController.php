<?php

namespace App\Http\Controllers;

use App\DataTables\AssetDataTable;
use App\Models\Asset;
use App\Models\AssetEntry;
use App\Models\Barang;
use App\Models\RegisterAsset;
use App\Models\Room;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AssetController extends Controller
{
    public function __construct() {}


    function index(AssetDataTable $datatable)
    {
        $title = "Data Asset";
        $breadcrumbs = ["Asset Management", "Asset", "List"];

        return $datatable->render('pages.asset.index', compact('title', 'breadcrumbs'));
    }

    function create()
    {
        $title = "Tambah Asset";
        $breadcrumbs = ["Asset Management", "Asset", "Tambah"];
        $action = route("asset.store");
        $method = "POST";
        $asset = new Asset();

        return view("pages.asset.create", compact('title', 'breadcrumbs', 'action', 'method', 'asset'));
    }

    function store(Request $request)
    {
        $validated = $request->validate([
            'kategori' => "required",
            'kode_barang' => "required",
            'distributor' => "required",
            'asal_perolehan' => "required",
            "tanggal_barang_masuk" => "required",
            "merk" => "required",
            "tipe" => "required",
            "bahan" => "required",
            "no_akd" => "required",
            "satuan_barang" => "required",
            "ukuran" => "required",
            "harga_satuan" => "required",
            "jumlah_barang" => "required",
            "total_harga" => "required",
            "kondisi" => "required",
            "rekening_belanja" => "required",
            "tanggal_pembayaran" => "required",
            "no_bast_kontrak" => "required",
            "no_bast_pembayaran" => "required",
            "nama_barang" => "required",
            "nama_umum" => "required",
            "spesifikasi_umum" => "required",
        ]);

        try {
            DB::beginTransaction();

            $validated["kategori_id"] = $validated["kategori"];
            $validated["barang_id"] = $validated["kode_barang"];
            $validated["distributor_id"] = $validated["distributor"];
            $validated["asal_perolehan_id"] = $validated["asal_perolehan"];
            $validated["harga_satuan"] = str_replace(".", "", $validated["harga_satuan"]);
            $validated["total_harga"] = str_replace(".", "", $validated["total_harga"]);

            $asset = Asset::create($validated);

            if ($request->register_code) {
                foreach ($request->register_code as $key => $val) {
                    RegisterAsset::create([
                        "asset_id" => $asset->id,
                        "register_code" => $val,
                        "qr_code" => $request->qr_code[$key],
                        "tag_rfid" => $request->tag_rfid[$key],
                        "no_rangka" => $request->no_rangka[$key],
                        "no_sn" => $request->no_sn[$key],
                        "no_mesin" => $request->no_mesin[$key],
                        "no_polisi" => $request->no_polisi[$key],
                        "no_bpkb" => $request->no_bpkb[$key],
                    ]);
                }
            }

            DB::commit();
            return response()->json([
                "success" => true,
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "success" => false,
                "message" => $th->getMessage(),
            ], 500);
        }
    }

    function generate_register_code(Request $request)
    {
        try {
            $barang = Barang::find($request->kode_barang);
            $jumlahbarang = $request->jumlah_barang;
            $lastcode = RegisterAsset::whereDate("created_at", now("Asia/Jakarta"))->where("barang_id", $barang->id)->latest("register_code")->first();
            if ($lastcode) {
                $codeParts = explode('.', $lastcode->register_code);
                $lastSegment = end($codeParts);
                $start_register_code = str_pad(((int)$lastSegment + 1), 4, '0', STR_PAD_LEFT);
            } else {
                $start_register_code = "0001";
            }

            $registercodes = [];

            for ($i = 1; $i <= $jumlahbarang; $i++) {
                $registercode = str_pad($start_register_code++, 4, '0', STR_PAD_LEFT);
                $dynamic_registercode = $registercode;
                $dynamic_qrcode = $barang->kode_barang . "." . $registercode . "." . now("Asia/Jakarta")->format("Ymd");
                $registercodes[] = [
                    "register_code" => $dynamic_registercode,
                    "qr_code" => $dynamic_qrcode
                ];
            }

            return response()->json([
                "register_codes" => $registercodes
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function show(Asset $asset)
    {
        $asset->load([
            'item',
            'room',
            'assetEntry:id,brand,type,general_specifications,unit_price'
        ]);

        return response()->json([
            'data' => $asset
        ], 200);
    }

    public function showEntry(AssetEntry $assetEntry)
    {
        $assetEntry->load([]);

        return response()->json([
            'data' => $assetEntry
        ], 200);
    }



    function dropdown()
    {
        $data = Asset::select(
            'assets.id',
            'assets.asset_code',
            'assets.unit_price',
            'assets.uom_name',
            'assets.asset_name',
            'assets.qr_code',
            'assets.register_code',
            'assets.serial_number',
            'assets.category_type',
            'items.item_code',
            'items.item_name'
        )
            ->filterRoom()
            ->join('items', 'items.id', '=', 'assets.item_id')
            ->leftJoin('categories', 'categories.id', '=', 'items.category_id');

        if (request()->has('q')) {
            $search = request()->get('q');
            $data = $data->where(function ($query) use ($search) {
                $query->where('assets.qr_code', 'like', '%' . $search . '%')
                    ->orWhere('assets.asset_name', 'like', '%' . $search . '%')
                    ->orWhere('items.item_name', 'like', '%' . $search . '%');
            });
        }

        if (request()->has('category_sub_type')) {
            $data = $data->where('categories.category_sub_type', request()->get('category_sub_type'));
        }

        if (request()->has('itemId')) {
            $data = $data->where('assets.item_id', request()->get('itemId'));
        }

        if (request()->has('roomId')) {
            if (request()->get('roomId') != null) {
                $data = $data->where('assets.document_room_id', request()->get('roomId'));
            }
        }

        if (request()->has('menu') && request()->get('menu') === 'alocation') {
            $data = $data->whereNull('document_room_id')
                ->where('assets.category_type', 'EQUIPMENT');
        }

        if (request()->has('category')) {
            $data = $data->where('items.category_id', request()->get('category'));
        }

        if (request()->has('category_type')) {
            $data = $data->where('assets.category_type', request()->get('category_type'));
        }

        if (request()->has('pic')) {
            if (request()->get('pic') != null) {
                $data = $data->where('assets.document_room_id', request()->get('pic'));
            }
        }

        if (request()->has("selectedItems")) {
            $data = $data->whereNotIn("assets.id", request()->get("selectedItems"));
        }

        $asset = $data->paginate(25);

        return response()->json($asset);
    }
}
