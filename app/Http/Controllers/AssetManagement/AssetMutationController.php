<?php

namespace App\Http\Controllers\AssetManagement;

use App\Http\Controllers\Controller;
use App\Models\Asset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AssetMutationController extends Controller
{
    function index()
    {
        $title = "BAST Mutasi";
        $breadcrumbs = ["Manajemen Aset", "BAST Mutasi"];

        return view("asset-management.asset-mutation.index", compact("title", "breadcrumbs"));
    }

    public function updateRoom(Request $request)
    {
        $request->validate([
            'asset_id' => 'required|exists:assets,id',
            'room_parent' => 'required|exists:rooms,id',
            'room_specifik' => 'required|exists:room_subs,id'
        ]);

        try {
            DB::beginTransaction();

            $asset = Asset::findOrFail($request->asset_id);
            $asset->update([
                'document_room_id' => $request->room_parent,
                'document_sub_room_id' => $request->room_specifik
            ]);

            DB::commit();
            
            return response()->json([
                'message' => 'Ruangan spesifik berhasil diperbarui'
            ], 200);

        } catch (\Throwable $th) {
            DB::rollBack();
            
            return response()->json([
                'message' => 'Terjadi kesalahan saat memperbarui ruangan',
                'error' => $th->getMessage()
            ], 500);
        }
    }
}
