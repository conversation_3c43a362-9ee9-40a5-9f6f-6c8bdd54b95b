<?php

namespace App\Http\Controllers\AssetManagement;

use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use App\Exports\AssetCategoryExport;
use App\Http\Controllers\Controller;
use App\Models\Asset;
use App\Models\Room;
use Maatwebsite\Excel\Facades\Excel;
use Yajra\DataTables\Facades\DataTables;

class AssetCategoryController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Kategori Aset - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Kategori Aset";
        $breadcrumbs = ["Manajemen Aset", "Kategori Aset"];

        return view('asset-management.asset-category.index', compact('title', "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Kategori Aset - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $data = Category::leftJoin("items", "items.category_id", "=", "categories.id")
                ->leftJoin("assets", "assets.item_id", "=", "items.id")
                ->where("categories.category_type", "!=", "LOGISTIC")
                ->when($request->filter, function ($query) use ($request) {
                    if ($request->filter == "asset") {
                        $query->where("categories.category_type", "!=", "OTHER");
                    } else if ($request->filter == "non-asset") {
                        $query->where("categories.category_type", "OTHER");
                    }
                })
                ->select(
                    "categories.id",
                    "categories.category_code",
                    "categories.category_name",
                    "categories.category_type",
                    "categories.category_sub_type",
                    DB::raw("COUNT(assets.id) as total_asset")
                )
                ->groupBy("categories.id", "categories.category_code", "categories.category_type", "categories.category_name", "categories.category_sub_type");

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return '<a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-show" data-bs-toggle="modal" data-id="' . $row->id . '"><i class="fas fa-search"></i></a>';
                })
                ->rawColumns(["action"])
                ->make("true");
        }
    }

    function show(Category $category)
    {
        if (!hasPermissionInGuard('Kategori Aset - View')) {
            abort(403, "Unauthorized action.");
        }

        $data = Category::leftJoin("items", "items.category_id", "=", "categories.id")
            ->leftJoin("assets", "assets.item_id", "=", "items.id")
            ->select(
                "categories.id",
                "categories.category_code",
                "categories.category_name",
                "categories.category_type",
                "categories.category_sub_type",
                DB::raw("COUNT(assets.id) as total_asset")
            )
            ->groupBy("categories.id", "categories.category_name", "categories.category_code", "categories.category_type", "categories.category_sub_type")
            ->where("categories.id", $category->id)
            ->first();

        return response()->json([
            "data" => $data
        ], 200);
    }

    function asset_list(Request $request, Category $category)
    {
        if (!hasPermissionInGuard('Kategori Aset - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = Asset::with(["item"])
                ->select('assets.*')
                ->whereHas("item", function ($query) use ($category) {
                    $query->where("category_id", $category->id);
                })
                ->when($request->condition, function($query) use ($request) {
                    $query->where('asset_condition', $request->condition);
                });

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("actual_room", function ($row) {
                    $room = Room::find($row->document_room_id);
                    return $room ? $room->room_name : "-";
                })
                ->addColumn("action", function ($row) {
                    return '<a href="#modal-dialog" class="btn btn-sm btn-outline-primary btn-show" data-bs-toggle="modal" data-id="' . $row->id . '"><i class="fas fa-search"></i></a>';
                })
                ->orderColumn('qr_code', 'qr_code $1')
                ->orderColumn('register_code', 'register_code $1')
                ->orderColumn('asset_condition', 'asset_condition $1')
                ->filter(function ($query) use ($request) {
                    if ($request->search['value']) {
                        $searchValue = $request->search['value'];
                        $query->where(function ($q) use ($searchValue) {
                            $q->where('assets.qr_code', 'LIKE', "%{$searchValue}%")
                              ->orWhere('assets.register_code', 'LIKE', "%{$searchValue}%")
                              ->orWhere('assets.asset_condition', 'LIKE', "%{$searchValue}%")
                              ->orWhereHas('item', function ($subQuery) use ($searchValue) {
                                  $subQuery->where('item_code', 'LIKE', "%{$searchValue}%")
                                         ->orWhere('item_name', 'LIKE', "%{$searchValue}%");
                              });
                        });
                    }
                })
                ->rawColumns(["action"])
                ->make(true);
        }
    }

    public function exportExcel(Request $request, Category $category)
    {
        if (!hasPermissionInGuard('Kategori Aset - View')) {
            abort(403, "Unauthorized action.");
        }
        $categoryDataResponse = $this->show($category);

        if ($categoryDataResponse instanceof JsonResponse) {
            $categoryData = json_decode($categoryDataResponse->getContent(), true)['data'];
        } else {
            $categoryData = $categoryDataResponse;
        }

        $assets = Category::join('items', 'items.category_id', '=', 'categories.id')
            ->join('assets', 'assets.item_id', '=', 'items.id')
            ->select(
                'assets.id as asset_id',
                'assets.qr_code',
                'assets.register_code',
                'assets.serial_number',
                'items.item_name',
                'items.item_code',
                'categories.id as category_id',
                'categories.category_name'
            )
            ->where('categories.id', $category->id)
            ->get();

        if (empty($categoryData) || $assets->isEmpty()) {
            return response()->json(['message' => 'Data not found'], 404);
        }

        return Excel::download(new AssetCategoryExport($categoryData, $assets), 'assets_category_' . $category->category_code . '.xlsx');
    }
}
