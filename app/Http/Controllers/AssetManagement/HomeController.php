<?php

namespace App\Http\Controllers\AssetManagement;

use App\Http\Controllers\Controller;
use App\Models\Asset;
use App\Models\RequestDocument;
use App\Models\AssetLocationHistory;
use Carbon\Carbon;

class HomeController extends Controller
{
    function index()
    {
        $title = "Manajemen Aset";
        $breadcrumbs = ["Manajemen Aset"];

        // Get total equipments
        $totalEquipments = Asset::where('active', 1)
            ->where('category_type', 'EQUIPMENT')
            ->count();

        // Get total pending documents
        $totalPendingDocuments = RequestDocument::whereNull('document_id')
            ->count();

        // Get today's location changes
        $totalTodayLocationChanges = AssetLocationHistory::whereDate('created_at', Carbon::today())
            ->count();

        // Get latest 10 location changes
        $latestLocationChanges = AssetLocationHistory::with(['asset', 'room', 'prevRoom'])
            ->latest('scan_time')
            ->take(10)
            ->get();

        return view("asset-management.home.index", compact(
            "title",
            "breadcrumbs",
            "totalEquipments",
            "totalPendingDocuments", 
            "totalTodayLocationChanges",
            "latestLocationChanges"
        ));
    }
}
