<?php

namespace App\Http\Controllers\AssetManagement;

use Carbon\Carbon;
use App\Models\Asset;
use App\Models\Employee;
use Illuminate\Http\Request;
use App\Models\RequestDocument;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\AssetLocationHistory;
use Yajra\DataTables\Facades\DataTables;

class HelperBookController extends Controller
{

    function index()
    {
        if (!hasPermissionInGuard('Buku Bantu - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Buku Bantu";
        $breadcrumbs = ["Manajemen Aset", "Buku Bantu"];

        return view('asset-management.helper-book.index', compact('title', 'breadcrumbs'));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Buku Bantu - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $data = RequestDocument::whereNull('document_id')
                ->with(['requestDocumentAssets.asset', 'targetLocation'])
                ->orderBy('created_at', 'DESC');

            // Apply filters if provided
            if ($request->filled('document_type')) {
                $data->where('document_type', $request->document_type);
            }

            if ($request->filled('date_range')) {
                $dates = explode(' - ', $request->date_range);
                if (count($dates) == 2) {
                    $startDate = Carbon::createFromFormat('d/m/Y', trim($dates[0]))->startOfDay();
                    $endDate = Carbon::createFromFormat('d/m/Y', trim($dates[1]))->endOfDay();
                    $data->whereBetween('created_at', [$startDate, $endDate]);
                }
            }

            return DataTables::of($data)
                ->addIndexColumn()
                ->addColumn('action', function ($row) {
                    return '<a href="' . route('asset-management.helper-book.dokumen', $row->id) . '" target="_blank" class="btn btn-sm btn-outline-warning btn-view"><i class="fas fa-search"></i></a>';
                })
                ->rawColumns(['action'])
                ->make(true);
        }
    }

    function dokumen(RequestDocument $document)
    {
        if (!hasPermissionInGuard('Buku Bantu - Action')) {
            abort(403, "Unauthorized action.");
        }
        try {
            $template = '';
            $fileName = '';

            if ($document->document_type === 'PENEMPATAN') {
                $template = "bast.penempatan";
                $fileName = "template_dokumen_penempatan";
            } elseif ($document->document_type === 'MUTASI') {
                $template = "bast.mutasi";
                $fileName = "template_dokumen_mutasi";
            } elseif ($document->document_type === 'RUSAK') {
                $template = "bast.rusak";
                $fileName = "template_dokumen_rusak";
            }

            // $data = Asset::with(["item", "room"])
            //     ->whereIn("id", $document->requestDocumentAssets->pluck('asset_id'))
            //     ->get();
            $data = Asset::join("items", "assets.item_id", "=", "items.id")
                ->join("uoms", "assets.uom_id", "=", "uoms.id")
                ->join("asset_entries", "assets.asset_entry_id", "=", "asset_entries.id")
                ->leftJoin("rooms", "assets.document_room_id", "=", "rooms.id")
                ->whereIn("assets.id", $document->requestDocumentAssets->pluck('asset_id'))
                ->select(
                    "assets.id",
                    "assets.asset_code",
                    "assets.unit_price",
                    DB::raw("GROUP_CONCAT(assets.register_code) as register_codes"),
                    "items.item_name",
                    "items.item_code",
                    "rooms.room_code",
                    "rooms.room_name",
                    "uoms.uom_name",
                    DB::raw("COUNT(*) as total_quantity"),
                    "asset_entries.description",
                    "asset_entries.received_date",
                    "asset_entries.item_name as asset_entry_item_name"
                )
                ->groupBy(
                    "assets.unit_price", 
                    "items.item_name", 
                    "items.item_code", 
                    "rooms.room_code", 
                    "rooms.room_name", 
                    "uoms.uom_name",
                    "asset_entries.description",
                    "asset_entries.received_date",
                    "asset_entries.item_name"
                )
                ->get();

            $targetRoom = DB::table('rooms')
                ->where('id', $document->target_location)
                ->select('room_name')
                ->first();

            // Jika tidak ada data ruangan target, buat objek default
            if (!$targetRoom) {
                $targetRoom = (object) ['room_name' => '-'];
            }

            $initialRoom = DB::table('request_document_assets')
                ->join('assets', 'request_document_assets.asset_id', '=', 'assets.id')
                ->leftJoin('rooms', 'assets.document_room_id', '=', 'rooms.id')
                ->where('request_document_assets.request_document_id', $document->id)
                ->select('rooms.room_name')
                ->first();

            // Jika tidak ada data ruangan, buat objek default
            if (!$initialRoom) {
                $initialRoom = (object) ['room_name' => '-'];
            }

            $mainPerson = [
                'picItemStorage' => Employee::where('pic_type', 'PIC_PENYIMPANAN_BARANG')->first(),
                'teamLeader' => Employee::where('pic_type', 'KETUA_TEAM_ASET')->first(),
                'assetManager' => Employee::where('pic_type', 'PIC_PENGURUS_BARANG')->first(),
                'headPlanning' => Employee::where('pic_type', 'KEPALA_PERENCANAAN_PROGRAM_ASET')->first(),
            ];

            // Pastikan semua data yang diperlukan ada
            if (!$data || $data->isEmpty()) {
                return back()->with("error", "Tidak ada data asset yang ditemukan untuk dokumen ini.");
            }

            $pdf = Pdf::loadView($template, [
                "data" => $data,
                "requestDocument" => $document,
                "mainPerson" => $mainPerson,
                "targetRoom" => $targetRoom,
                "initialRoom" => $initialRoom,
            ])->setPaper('a4', 'portrait');

            return $pdf->stream($fileName . '.pdf');
        } catch (\Throwable $th) {
            return back()->with("error", $th->getMessage());
        }
    }
}
