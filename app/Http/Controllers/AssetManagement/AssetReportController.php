<?php

namespace App\Http\Controllers\AssetManagement;

use Carbon\Carbon;
use App\Models\Item;
use App\Models\Room;
use App\Models\Asset;
use App\Exports\KirExport;
use App\Models\AssetEntry;
use App\Exports\AspakExport;
use Illuminate\Http\Request;
use App\Exports\KirExportBarang;
use Illuminate\Support\Facades\DB;
use App\Exports\DaftarBarangExport;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\PenerimaanInventarisExport;
use App\Exports\PemeliharaanDetailReportExport;
use App\Models\DocumentAssetMedia;

class AssetReportController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('History Aset - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Laporan Aset";
        $breadcrumbs = ["Manajemen Aset", "Laporan Aset"];

        return view("asset-management.asset-report.index", compact("title", "breadcrumbs"));
    }

    function penerimaan_inventaris(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Penerimaan Inventaris - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            list($start, $end) = explode(" - ", request("periode"));
            $startDate = Carbon::parse($start)->format('Y-m-d');
            $endDate = Carbon::parse($end)->addDay(1)->format('Y-m-d');

            $params = [
                "start" => $startDate,
                "end" => $endDate,
                "jenis" => $request->jenis_inventaris ?? 'all'
            ];

            $query = AssetEntry::with(["assets", "item"])
                ->leftJoin('assets', 'asset_entries.id', '=', 'assets.asset_entry_id')
                ->whereBetween("asset_entries.payment_date", [$startDate, $endDate]);

            if ($request->jenis_inventaris && $request->jenis_inventaris !== 'all') {
                if ($request->jenis_inventaris === 'intrakomptabel') {
                    $query->where('asset_entries.unit_price', '>=', 500000);
                } else if ($request->jenis_inventaris === 'ekstrakomptabel') {
                    $query->where('asset_entries.unit_price', '<', 500000);
                }
            }

            $data = $query->groupBy("asset_entries.item_id", "asset_entries.payment_date", "asset_entries.received_date", "asset_entries.purchasing_account", "asset_entries.brand", "asset_entries.general_specifications", "asset_entries.quantity", "asset_entries.uom_name", "asset_entries.unit_price", "asset_entries.description", "assets.sub_activity_code", "assets.sub_activity_name")
                ->select("asset_entries.spending_account_code", "asset_entries.contract_form", "asset_entries.bast_contract_number", "asset_entries.item_id", "asset_entries.created_at", "asset_entries.payment_date", "asset_entries.received_date", "asset_entries.purchasing_account", "asset_entries.brand", "asset_entries.general_specifications", "asset_entries.quantity", "asset_entries.uom_name", "asset_entries.unit_price", "asset_entries.description", "assets.sub_activity_code", "assets.sub_activity_name")
                ->get();

            return view('components/asset-management/report-penerimaan-inventaris', ["params" => $params, "data" => $data]);
        }

        $title = "Laporan Penerimaan Inventaris";
        $breadcrumbs = ["Manajemen Aset", "Laporan Aset", "Penerimaan Inventaris"];
        $exportRoute = route("asset-management.export-report.penerimaan_inventaris");

        return view("asset-management.asset-report.penerimaan-inventaris", compact("title", "breadcrumbs", "exportRoute"));
    }

    public function export_penerimaan_inventaris(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Penerimaan Inventaris - View')) {
            abort(403, "Unauthorized action.");
        }
        $startDate = Carbon::parse($request->start_date)->format('Y-m-d');
        $endDate = Carbon::parse($request->end_date)->addDay(1)->format('Y-m-d');

        $params = [
            "start" => $startDate,
            "end" => $endDate,
            "jenis" => $request->jenis_inventaris ?? 'all'
        ];

        $query = AssetEntry::with(["assets", "item"])
            ->leftJoin('assets', 'asset_entries.id', '=', 'assets.asset_entry_id')
            ->whereBetween("asset_entries.payment_date", [$startDate, $endDate]);

        if ($request->jenis_inventaris && $request->jenis_inventaris !== 'all') {
            if ($request->jenis_inventaris === 'intrakomptabel') {
                $query->where('asset_entries.unit_price', '>=', 500000);
            } else if ($request->jenis_inventaris === 'ekstrakomptabel') {
                $query->where('asset_entries.unit_price', '<', 500000);
            }
        }

        $data = $query->groupBy("asset_entries.item_id", "asset_entries.payment_date", "asset_entries.received_date", "asset_entries.purchasing_account", "asset_entries.brand", "asset_entries.general_specifications", "asset_entries.quantity", "asset_entries.uom_name", "asset_entries.unit_price", "asset_entries.description", "assets.sub_activity_code", "assets.sub_activity_name")
            ->select("asset_entries.spending_account_code", "asset_entries.contract_form","asset_entries.item_id", "asset_entries.created_at", "asset_entries.payment_date", "asset_entries.received_date", "asset_entries.purchasing_account", "asset_entries.brand", "asset_entries.general_specifications", "asset_entries.quantity", "asset_entries.uom_name", "asset_entries.unit_price", "asset_entries.description", "assets.sub_activity_code", "assets.sub_activity_name")
            ->get();

        return Excel::download(new PenerimaanInventarisExport($data, $params), 'Penerimaan Inventaris.xls');
    }

    function aspak(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Aspak - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            list($start, $end) = explode(" - ", request("periode"));
            $startDate = Carbon::parse($start)->format('Y-m-d');
            $endDate = Carbon::parse($end)->addDay(1)->format('Y-m-d');

            $params = [
                "start" => $startDate,
                "end" => $endDate,
            ];

            $data = Asset::with(['assetEntry', 'item'])
                ->select('assets.*')
                ->where('assets.category_type', 'EQUIPMENT')
                ->addSelect([
                    'last_media_path' => DocumentAssetMedia::join("document_assets", "document_assets.id", "=", "document_asset_medias.document_asset_id")
                        ->join("documents", "documents.id", "=", "document_assets.document_id")
                        ->whereColumn("document_assets.asset_id", "assets.id")
                        ->whereIn("document_type", ["PENEMPATAN", "MUTASI", "RUSAK"])
                        ->orderBy('document_asset_medias.id', 'DESC')
                        ->limit(1)
                        ->select('document_asset_medias.media_path')
                ])
                ->whereHas('assetEntry', function ($query) use ($startDate, $endDate) {
                    $query->whereBetween('created_at', [$startDate, $endDate]);
                })
                ->get();

            return view('components/asset-management/report-aspak', ["params" => $params, "data" => $data]);
        }

        $title = "Laporan Aspak";
        $breadcrumbs = ["Manajemen Aset", "Laporan", "Aspak"];
        $exportRoute = route("asset-management.export-report.aspak");

        return view("asset-management.asset-report.index", compact("title", "breadcrumbs", "exportRoute"));
    }

    public function export_aspak(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Aspak - View')) {
            abort(403, "Unauthorized action.");
        }
        $startDate = Carbon::parse($request->start_date)->format('Y-m-d');
        $endDate = Carbon::parse($request->end_date)->addDay(1)->format('Y-m-d');

        $params = [
            "start" => $startDate,
            "end" => $endDate,
        ];

        $reportData = Asset::with(['assetEntry', 'item'])
            ->select('assets.*')
            ->where('assets.category_type', 'EQUIPMENT')
            ->addSelect([
                'last_media_path' => DocumentAssetMedia::join("document_assets", "document_assets.id", "=", "document_asset_medias.document_asset_id")
                    ->join("documents", "documents.id", "=", "document_assets.document_id")
                    ->whereColumn("document_assets.asset_id", "assets.id")
                    ->whereIn("document_type", ["PENEMPATAN", "MUTASI", "RUSAK"])
                    ->orderBy('document_asset_medias.id', 'DESC')
                    ->limit(1)
                    ->select('document_asset_medias.media_path')
            ])
            ->whereHas('assetEntry', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            })
            ->get();

        return Excel::download(new AspakExport($reportData, $params), 'aspak_report.xlsx');
    }

    function daftar_barang(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Daftar Barang - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            list($start, $end) = explode(" - ", request("periode"));
            $startDate = Carbon::parse($start)->format('Y-m-d');
            $endDate = Carbon::parse($end)->addDay(1)->format('Y-m-d');

            $params = [
                "start" => $startDate,
                "end" => $endDate,
            ];

            $data = Asset::with(["assetEntry", "item"])
                ->where("category_type", "EQUIPMENT")
                ->whereBetween("created_at", [$startDate, $endDate])
                ->get();

            return view('components/asset-management/report-daftar-barang', ["params" => $params, "data" => $data]);
        }

        $title = "Laporan Daftar Barang";
        $breadcrumbs = ["Manajemen Aset", "Laporan Aset", "Daftar Barang"];
        $exportRoute = route("asset-management.export-report.daftar_barang");

        return view("asset-management.asset-report.index", compact("title", "breadcrumbs", "exportRoute"));
    }

    public function export_daftar_barang(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Daftar Barang - View')) {
            abort(403, "Unauthorized action.");
        }
        $startDate = Carbon::parse($request->start_date)->format('Y-m-d');
        $endDate = Carbon::parse($request->end_date)->addDay(1)->format('Y-m-d');

        $params = [
            "start" => $startDate,
            "end" => $endDate,
        ];

        $reportData = Asset::with(["assetEntry", "item"])
            ->whereBetween("created_at", [$startDate, $endDate])
            ->get();

        return Excel::download(new DaftarBarangExport($reportData, $params), 'daftar barang.xls');
    }

    function kir(Request $request)
    {
        if (!hasPermissionInGuard('Laporan KIR - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $room = Room::find($request->room);
            $data = Asset::join("items", "assets.item_id", "=", "items.id")
                ->join("uoms", "assets.uom_id", "=", "uoms.id")
                ->join("asset_entries", "assets.asset_entry_id", "=", "asset_entries.id")
                ->leftJoin("rooms", "assets.document_room_id", "=", "rooms.id")
                ->select(
                    "assets.id",
                    "assets.asset_code",
                    "assets.unit_price",
                    DB::raw("GROUP_CONCAT(assets.register_code) as register_codes"),
                    DB::raw('GROUP_CONCAT(DISTINCT CASE WHEN assets.serial_number IS NOT NULL AND assets.serial_number != "" THEN assets.serial_number END SEPARATOR ", ") as serialNumbers'),
                    "items.item_name",
                    "items.item_code",
                    "rooms.room_code",
                    "rooms.room_name",
                    "uoms.uom_name",
                    DB::raw("COUNT(*) as total_quantity"),
                    "asset_entries.description",
                    "asset_entries.received_date",
                    "asset_entries.brand",
                    "asset_entries.size",
                    "asset_entries.material",
                    "asset_entries.condition",
                )
                ->where("assets.category_type", "EQUIPMENT")
                ->groupBy("assets.unit_price", "items.item_code", "items.item_code", "rooms.room_code", "rooms.room_name", "uoms.uom_name", "asset_entries.condition")
                ->filterRoom()
                ->get();

            return view('components/asset-management/report-kir', ["room" => $room, "data" => $data]);
        }

        $title = "Laporan KIR";
        $breadcrumbs = ["Manajemen Aset", "Laporan", "KIR"];
        $exportRoute = route("asset-management.export-report.kir");

        return view("asset-management.asset-report.kir", compact("title", "breadcrumbs", "exportRoute"));
    }

    function kir_barang(Request $request)
    {
        if (!hasPermissionInGuard('Laporan KIR Barang - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $room = Room::find($request->room);
            $data = DB::table('items')
                ->join('assets', 'items.id', '=', 'assets.item_id')
                ->leftJoin('asset_entries', 'assets.asset_entry_id', '=', 'asset_entries.id')
                ->when(request('room'), function ($query) {
                    $query->where('assets.document_room_id', request('room'));
                })
                ->select(
                    'asset_entries.*',
                    'items.*',
                    'assets.*',
                    DB::raw('GROUP_CONCAT(assets.register_code SEPARATOR ", ") as registerCodes'),
                    DB::raw('GROUP_CONCAT(DISTINCT CASE WHEN assets.serial_number IS NOT NULL AND assets.serial_number != "" THEN assets.serial_number END SEPARATOR ", ") as serialNumbers'), // Gabungkan serial_number, abaikan yang kosong
                    DB::raw('COUNT(assets.id) as asset_count'),
                    DB::raw('SUM(assets.unit_price) as total_price'),
                    DB::raw('GROUP_CONCAT(assets.unit_price SEPARATOR ", ") as unitPrices')
                )
                ->groupBy('items.id', 'assets.document_room_id')
                ->get();

            return view('components/asset-management/report-kir-barang', ["data" => $data, 'room' => $room]);
        }

        $title = "Laporan KIR Barang";
        $breadcrumbs = ["Manajemen Aset", "Laporan", "KIR Barang"];
        $exportRoute = route("asset-management.export-report.kir-barang");

        return view("asset-management.asset-report.kir-barang", compact("title", "breadcrumbs", "exportRoute"));
    }

    public function export_kir(Request $request)
    {
        if (!hasPermissionInGuard('Laporan KIR - View')) {
            abort(403, "Unauthorized action.");
        }

        $params = [];

        if ($request->room_id) {
            $room = Room::find($request->room_id);

            if ($room) {
                $params["room"] = $room;
            } else {
                return response()->json(['error' => 'Room not found.'], 404);
            }
        }

        $query = Asset::with(["assetEntry", "item"]);

        if ($request->room_id) {
            $query->where('document_room_id', $request->room_id);
        }

        $reportData = $query->get();

        return Excel::download(new KirExport($reportData, $params), 'kir.xlsx');
    }
    public function export_kir_barang(Request $request)
    {
        if (!hasPermissionInGuard('Laporan KIR - View')) {
            abort(403, "Unauthorized action.");
        }

        $params = [];

        if ($request->room_id) {
            $room = Room::find($request->room_id);

            if ($room) {
                $params["room"] = $room;
            } else {
                return response()->json(['error' => 'Room not found.'], 404);
            }
        }

        $reportData = DB::table('items')
            ->join('assets', 'items.id', '=', 'assets.item_id')
            ->leftJoin('asset_entries', 'assets.asset_entry_id', '=', 'asset_entries.id')
            ->when(request('room_id'), function ($query) {
                $query->where('assets.document_room_id', request('room_id'));
            })
            ->select(
                'asset_entries.*',
                'items.*',
                'assets.*',
                DB::raw('GROUP_CONCAT(assets.register_code SEPARATOR ", ") as registerCodes'),
                DB::raw('GROUP_CONCAT(DISTINCT CASE WHEN assets.serial_number IS NOT NULL AND assets.serial_number != "" THEN assets.serial_number END SEPARATOR ", ") as serialNumbers'),
                DB::raw('COUNT(assets.id) as asset_count'),
                DB::raw('SUM(assets.unit_price) as total_price'),
                DB::raw('GROUP_CONCAT(assets.unit_price SEPARATOR ", ") as unitPrices')
            )
            ->groupBy('items.id', 'assets.document_room_id')
            ->get();

        return Excel::download(new KirExportBarang($reportData, $params), 'kir.xlsx');
    }
}
