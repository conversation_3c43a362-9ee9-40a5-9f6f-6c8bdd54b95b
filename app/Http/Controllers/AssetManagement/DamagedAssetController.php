<?php

namespace App\Http\Controllers\AssetManagement;

use App\Models\Asset;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class DamagedAssetController extends Controller
{
  
    
    function index()
    {
        if (!hasPermissionInGuard('Aset Rusak - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Aset Rusak";
        $breadcrumbs = ["Manajemen Aset", "Aset Rusak"];

        return view('asset-management.damaged-asset.index', compact('title', 'breadcrumbs'));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Aset Rusak - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $data = Asset::join('document_assets', 'assets.id', '=', 'document_assets.asset_id')
                ->join('documents', 'documents.id', '=', 'document_assets.document_id')
                ->join('items', 'assets.item_id', '=', 'items.id')
                ->join('asset_entries', 'assets.asset_entry_id', '=', 'asset_entries.id')
                ->leftJoin('rooms', 'assets.document_room_id', '=', 'rooms.id')
                ->where('assets.active', 1)
                ->where('assets.category_type', 'EQUIPMENT')
                ->where('documents.document_type', 'RUSAK')
                ->when($request->asset, function ($query) use ($request) {
                    if ($request->asset) {
                        $query->where('assets.id', $request->asset);
                    }
                })
                ->select('assets.*', 'documents.document_path', 'rooms.room_code', 'rooms.room_name', 
                        'items.depreciation_year', 'asset_entries.received_date', 'items.item_code as asset_code');

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->editColumn("qr_code", function ($row) {
                    return QrCode::size(30)->generate($row->qr_code);
                })
                ->editColumn("register_code", function ($row) {
                    return $row->register_code . '<br>' . '<span class="text-danger">' . $row->serial_number . '</span>';
                })
                ->addColumn("action", function ($row) {
                    return '<a href="#modal-dialog" class="btn btn-sm btn-outline-primary btn-document" data-bs-toggle="modal" data-id="' . $row->id . '"><i class="fas fa-search"></i></a>';
                })
                ->rawColumns(['register_code', 'action'])
                ->make(true);
        }
    }

    function document(Asset $asset)
    {
        if (!hasPermissionInGuard('Aset Rusak - View')) {
            abort(403, "Unauthorized action.");
        }
        
        $assetDocument = Asset::join('document_assets', 'assets.id', '=', 'document_assets.asset_id')
            ->join('documents', 'documents.id', '=', 'document_assets.document_id')
            ->where('assets.id', $asset->id)
            ->where('documents.document_type', 'RUSAK')
            ->select('documents.document_path')
            ->first();

        if ($assetDocument && $assetDocument->document_path) {
            $documentUrl = asset('storage/' . $assetDocument->document_path);
            return response()->json(['document_url' => $documentUrl]);
        }

        return response()->json(['error' => 'Dokumen tidak ditemukan'], 404);
    }

    function dropdown(Request $request)
    {
        if ($request->ajax()) {
            $data = Asset::join('document_assets', 'assets.id', '=', 'document_assets.asset_id')
                ->join('documents', 'documents.id', '=', 'document_assets.document_id')
                ->leftJoin('rooms', 'assets.document_room_id', '=', 'rooms.id')
                ->where('assets.active', 1)
                ->where('assets.category_type', 'EQUIPMENT')
                ->where('documents.document_type', 'RUSAK')
                ->select('assets.*', 'documents.document_path', 'rooms.room_code', 'rooms.room_name')->get();

            return response()->json(['data' => $data]);
        }
    }
}
