<?php

namespace App\Http\Controllers\AssetManagement;

use App\Models\Asset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use SimpleSoftwareIO\QrCode\Facades\QrCode;


class AssetDeletionController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Penghapusan Aset - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Data Penghapusan Aset";
        $breadcrumbs = ["Manajemen Aset", "Data Penghapusan Aset"];

        // Get unique asset names for filter dropdown
        $assets = Asset::join('document_assets', 'assets.id', '=', 'document_assets.asset_id')
            ->join('documents', 'documents.id', '=', 'document_assets.document_id')
            ->join('items', 'assets.item_id', '=', 'items.id')
            ->where('assets.active', 0)
            ->where('assets.category_type', 'EQUIPMENT')
            ->where('documents.document_type', 'RUSAK')
            ->select('assets.asset_name')
            ->distinct()
            ->pluck('asset_name');

        return view('asset-management.asset-deletion.index', compact('title', 'breadcrumbs', 'assets'));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Penghapusan Aset - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $data = Asset::join('document_assets', 'assets.id', '=', 'document_assets.asset_id')
                ->join('documents', 'documents.id', '=', 'document_assets.document_id')
                ->join('items', 'assets.item_id', '=', 'items.id')
                ->join('asset_entries', 'assets.asset_entry_id', '=', 'asset_entries.id')
                ->leftJoin('rooms', 'assets.document_room_id', '=', 'rooms.id')
                ->where('assets.active', 0)
                ->where('assets.category_type', 'EQUIPMENT')
                ->where('documents.document_type', 'RUSAK')
                ->when($request->asset, function ($query) use ($request) {
                    if ($request->asset != "") {
                        $query->where("assets.asset_name", $request->asset);
                    }
                })
                ->select(
                    'assets.id',
                    'assets.qr_code',
                    'assets.asset_name',
                    'assets.register_code',
                    'assets.serial_number',
                    'assets.document_room_id',
                    'documents.document_path',
                    'rooms.room_code',
                    'rooms.room_name',
                    'items.item_code as asset_code'
                );

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->editColumn("qr_code", function ($row) {
                    return QrCode::size(30)->generate($row->qr_code);
                })
                ->editColumn("register_code", function ($row) {
                    return $row->register_code . '<br>' . '<span class="text-danger">' . $row->serial_number . '</span>';
                })
                ->addColumn("action", function ($row) {
                    return '<a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-document" data-bs-toggle="modal" data-id="' . $row->id . '"><i class="fas fa-file"></i></a>';
                })
                ->rawColumns(['register_code', 'qr_code', 'action'])
                ->make(true);
        }
    }

    function delete(Request $request)
    {
        if (!hasPermissionInGuard('Penghapusan Aset - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Hapus Asset";
        $breadcrumbs = ["Manajemen Aset", "Penghapusan Aset", "Hapus Aset"];

        return view('asset-management.asset-deletion.delete', compact('title', 'breadcrumbs'));
    }

    function store(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            DB::beginTransaction();

            foreach ($request->kode_barang as $key => $val) {
                $asset = Asset::find($val);

                if ($asset) {
                    $asset->deleted_at = now();
                    $asset->deleted_by = getAuthUserId();
                    $asset->updated_at = now();
                    $asset->active = 0;
                    $asset->save();
                }
            }

            DB::commit();
            return response()->json([
                "success" => true,
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "success" => false,
                "message" => $th->getMessage(),
            ], 500);
        }
    }
}
