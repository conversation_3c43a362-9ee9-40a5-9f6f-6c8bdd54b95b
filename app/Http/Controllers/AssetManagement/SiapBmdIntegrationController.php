<?php

namespace App\Http\Controllers\AssetManagement;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;

class SiapBmdIntegrationController extends Controller
{
    public function index(): View
    {
        if (! hasPermissionInGuard('Data Aset - View')) {
            abort(403, 'Unauthorized action.');
        }

        $title = 'SIAP BMD Integration';
        $breadcrumbs = ['Manajemen Aset', 'SIAP BMD Integration'];

        return view('asset-management.siap-bmd-integration.index', compact('title', 'breadcrumbs'));
    }

    public function list(Request $request): JsonResponse
    {
        if (! hasPermissionInGuard('Data Aset - View')) {
            abort(403, 'Unauthorized action.');
        }

        if ($request->ajax()) {
            $data = DB::table('assets')
                ->leftJoin('asset_entries', 'assets.asset_entry_id', '=', 'asset_entries.id')
                ->leftJoin('items', 'assets.item_id', '=', 'items.id')
                ->leftJoin('rooms', 'assets.document_room_id', '=', 'rooms.id')
                ->leftJoin('siap_bmd_programs as activity', 'asset_entries.siap_bmd_activity_id', '=', 'activity.id')
                ->leftJoin('siap_bmd_programs as sub_activity', 'asset_entries.siap_bmd_sub_activity_id', '=', 'sub_activity.id')
                ->select(
                    'assets.*',
                    'asset_entries.payment_date',
                    'asset_entries.received_date',
                    'asset_entries.source_supply',
                    'asset_entries.siap_bmd_asset_need_plan_code',
                    'asset_entries.siap_bmd_asset_user_name',
                    'items.item_name',
                    'items.item_code',
                    'rooms.room_name',
                    'activity.program_name as siap_bmd_activity_name',
                    'activity.program_code as siap_bmd_activity_code',
                    'sub_activity.program_name as siap_bmd_sub_activity_name',
                    'sub_activity.program_code as siap_bmd_sub_activity_code'
                )
                ->where('assets.category_type', 'EQUIPMENT')
                ->whereNotNull('asset_entries.siap_bmd_activity_id'); // Only show assets with SIAP BMD data

            return DataTables::of($data)
                ->addIndexColumn()
                ->editColumn('qr_code', function ($row) {
                    return '<div class="text-center align-middle">'.QrCode::size(50)->generate($row->qr_code).'</div>';
                })
                ->addColumn('formatted_created_at', function ($row) {
                    if ($row->created_at) {
                        // Set Indonesian locale for date formatting
                        Carbon::setLocale('id');
                        $date = Carbon::parse($row->created_at);
                        // Format date in Indonesian format (DD/MM/YYYY or "13 September 2025")
                        $formattedDate = $date->translatedFormat('d F Y');
                        // Format time in 24-hour format (HH:MM)
                        $formattedTime = $date->format('H:i');
                        // Return two-line format
                        return $formattedDate.'<br><small class="text-muted">'.$formattedTime.'</small>';
                    }
                    return '-';
                })
                ->addColumn('real_qr_code', function ($row) {
                    return $row->qr_code;
                })
                ->addColumn('action', function ($row) {
                    return '<a href="'.route('asset-management.asset-hospital.edit', $row->id).'" class="btn btn-sm btn-outline-warning btn-edit"><i class="fas fa-edit"></i></a>';
                })
                ->addColumn('siap_bmd_info', function ($row) {
                    $activity = $row->siap_bmd_activity_name ? $row->siap_bmd_activity_code . ' - ' . $row->siap_bmd_activity_name : '-';
                    $subActivity = $row->siap_bmd_sub_activity_name ? $row->siap_bmd_sub_activity_code . ' - ' . $row->siap_bmd_sub_activity_name : '-';
                    return $activity . '<br><small class="text-muted">' . $subActivity . '</small>';
                })
                ->addColumn('siap_bmd_plan_info', function ($row) {
                    $planCode = $row->siap_bmd_asset_need_plan_code ?: '-';
                    $userName = $row->siap_bmd_asset_user_name ?: '-';
                    return $planCode . '<br><small class="text-muted">User: ' . $userName . '</small>';
                })
                // Custom global search across specific columns
                ->filter(function ($query) use ($request) {
                    $search = $request->get('search')['value'] ?? null;
                    if ($search) {
                        $query->where(function ($q) use ($search) {
                            $q->where('assets.qr_code', 'like', "%{$search}%")
                                ->orWhere('items.item_name', 'like', "%{$search}%")
                                ->orWhere('assets.serial_number', 'like', "%{$search}%")
                                ->orWhere('activity.program_name', 'like', "%{$search}%")
                                ->orWhere('sub_activity.program_name', 'like', "%{$search}%")
                                ->orWhere('asset_entries.siap_bmd_asset_need_plan_code', 'like', "%{$search}%");
                        });
                    }
                })
                ->rawColumns(['action', 'qr_code', 'siap_bmd_info', 'siap_bmd_plan_info', 'formatted_created_at'])
                ->make(true);
        }
    }
}