<?php

namespace App\Http\Controllers\AssetManagement;

use App\Http\Controllers\Controller;
use App\Models\Asset;
use App\Models\Document;
use App\Models\DocumentAsset;
use App\Models\IncidentalRequest;
use App\Models\MaintenanceActivity;
use App\Models\MaintenanceSchedule;
use Illuminate\Http\Request;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Yajra\DataTables\Facades\DataTables;

class AssetHistoryController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('History Aset - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "History Aset";
        $breadcrumbs = ["Manajemen Aset", "History Aset"];

        return view('asset-management.asset-history.index', compact('title', 'breadcrumbs'));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('History Aset - View')) {
            abort(403, "Unauthorized action.");
        }
        if($request->ajax()) {
            $data = Asset::with(["assetEntry", "item"])
                ->where("category_type", "EQUIPMENT")
                ->when($request->asset, function ($query) use ($request) {
                    if ($request->asset != "") {
                        $query->where("assets.id", $request->asset);
                    }
                })
                ->filterById();

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->editColumn("qr_code", function ($row) {
                    return QrCode::size(30)->generate($row->qr_code);
                })
                ->editColumn("register_code", function ($row) {
                    return $row->register_code . '<br>' . '<span class="text-danger">'. $row->serial_number .'</span>';
                })
                ->addColumn("action", function ($row) {
                    return '<a href="#modal-dialog" class="btn btn-sm btn-outline-primary btn-history" data-bs-toggle="modal" data-id="'. $row->id .'"><i class="fas fa-eye"></i></a>';
                })
                ->rawColumns(['register_code', 'action'])
                ->make(true);
        }
    }

    function history(Asset $asset)
    {
        if (!hasPermissionInGuard('History Aset - View')) {
            abort(403, "Unauthorized action.");
        }
        
        $asset->load([
            "item",
            "room",
        ]);

        $qrCode = '<span>'. QrCode::size(120)->generate($asset->qr_code). '</span>';
        $documents = Document::join("document_assets", "documents.id", "=", "document_assets.document_id")
                            ->where("document_assets.asset_id", $asset->id)
                            ->get();

        $maintenanceSchedule = MaintenanceSchedule::join("maintenance_schedule_details", "maintenance_schedules.id", "=", "maintenance_schedule_details.maintenance_schedule_id")
                            ->join("rooms", "rooms.id", "=", "maintenance_schedule_details.room_id")
                            ->where("maintenance_schedule_details.asset_id", $asset->id)
                            ->get();

        $maintenanceActivity = MaintenanceActivity::join("maintenance_activity_details", "maintenance_activities.id", "=", "maintenance_activity_details.maintenance_activity_id")
                            ->join("rooms", "rooms.id", "=", "maintenance_activities.room_id")
                            ->where("maintenance_activity_details.asset_id", $asset->id)
                            ->get();

        $maintenanceIncidental = IncidentalRequest::with(["asset", "room"])
                            ->where("asset_id", $asset->id)
                            ->get();

        return view('asset-management.asset-history.history', compact('asset', 'qrCode', 'documents', 'maintenanceSchedule', 'maintenanceActivity', 'maintenanceIncidental'));
    }
}
