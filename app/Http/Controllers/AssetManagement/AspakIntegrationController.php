<?php

namespace App\Http\Controllers\AssetManagement;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class AspakIntegrationController extends Controller
{
    public function index()
    {
        if (! hasPermissionInGuard('Data Aset - View')) {
            abort(403, 'Unauthorized action.');
        }

        $title = 'ASPAK Integration';
        $breadcrumbs = ['Manajemen Aset', 'ASPAK Integration'];

        return view('asset-management.aspak-integration.index', compact('title', 'breadcrumbs'));
    }

    public function list(Request $request)
    {
        if (! hasPermissionInGuard('Data Aset - View')) {
            abort(403, 'Unauthorized action.');
        }

        if ($request->ajax()) {
            $data = DB::table('assets')
                ->leftJoin('asset_entries', 'assets.asset_entry_id', '=', 'asset_entries.id')
                ->leftJoin('items', 'assets.item_id', '=', 'items.id')
                ->leftJoin('rooms', 'assets.document_room_id', '=', 'rooms.id')
                ->leftJoin('aspak_items', 'assets.aspak_item_id', '=', 'aspak_items.id')
                ->leftJoin('aspak_service_rooms', 'assets.aspak_service_room_id', '=', 'aspak_service_rooms.id')
                ->select(
                    'assets.*',
                    'asset_entries.payment_date',
                    'asset_entries.received_date',
                    'asset_entries.source_supply',
                    'asset_entries.aspak_distributor',
                    'asset_entries.aspak_maintenance_pic',
                    'items.item_name',
                    'items.item_code',
                    'rooms.room_name',
                    'aspak_items.item_name as aspak_item_name',
                    'aspak_items.item_code as aspak_item_code',
                    'aspak_service_rooms.room_service_name as aspak_room_name',
                    'aspak_service_rooms.room_service_code as aspak_room_code'
                )
                ->where('assets.category_type', 'EQUIPMENT');

            return DataTables::of($data)
                ->addIndexColumn()
                ->editColumn('qr_code', function ($row) {
                    return '<div class="text-center align-middle">'.QrCode::size(50)->generate($row->qr_code).'</div>';
                })
                ->addColumn('formatted_created_at', function ($row) {
                    if ($row->created_at) {
                        // Set Indonesian locale for date formatting
                        Carbon::setLocale('id');
                        $date = Carbon::parse($row->created_at);
                        // Format date in Indonesian format (DD/MM/YYYY or "13 September 2025")
                        $formattedDate = $date->translatedFormat('d F Y');
                        // Format time in 24-hour format (HH:MM)
                        $formattedTime = $date->format('H:i');
                        // Return two-line format
                        return $formattedDate.'<br><small class="text-muted">'.$formattedTime.'</small>';
                    }
                    return '-';
                })
                ->addColumn('real_qr_code', function ($row) {
                    return $row->qr_code;
                })
                ->addColumn('action', function ($row) {
                    return '<a href="'.route('asset-management.asset-hospital.edit', $row->id).'" class="btn btn-sm btn-outline-warning btn-edit"><i class="fas fa-edit"></i></a>';
                })
                ->addColumn('aspak_info', function ($row) {
                    $aspakItem = $row->aspak_item_name ? $row->aspak_item_code . ' - ' . $row->aspak_item_name : '-';
                    $aspakRoom = $row->aspak_room_name ? $row->aspak_room_code . ' - ' . $row->aspak_room_name : '-';
                    return $aspakItem . '<br><small class="text-muted">' . $aspakRoom . '</small>';
                })
                // Custom global search across specific columns
                ->filter(function ($query) use ($request) {
                    $search = $request->get('search')['value'] ?? null;
                    if ($search) {
                        $query->where(function ($q) use ($search) {
                            $q->where('assets.qr_code', 'like', "%{$search}%")
                                ->orWhere('items.item_name', 'like', "%{$search}%")
                                ->orWhere('assets.serial_number', 'like', "%{$search}%")
                                ->orWhere('aspak_items.item_name', 'like', "%{$search}%")
                                ->orWhere('aspak_service_rooms.room_service_name', 'like', "%{$search}%");
                        });
                    }
                })
                ->filterColumn('item_name', function ($query, $keyword) {
                    $query->where('items.item_name', 'like', "%{$keyword}%");
                })
                ->filterColumn('aspak_item_name', function ($query, $keyword) {
                    $query->where('aspak_items.item_name', 'like', "%{$keyword}%");
                })
                ->filterColumn('aspak_room_name', function ($query, $keyword) {
                    $query->where('aspak_service_rooms.room_service_name', 'like', "%{$keyword}%");
                })
                ->filterColumn('real_qr_code', function ($query, $keyword) {
                    $query->where('assets.qr_code', 'like', "%{$keyword}%");
                })
                ->filterColumn('serial_number', function ($query, $keyword) {
                    $query->where('assets.serial_number', 'like', "%{$keyword}%");
                })
                ->rawColumns(['action', 'qr_code', 'aspak_info', 'formatted_created_at'])
                ->make(true);
        }
    }
}
