<?php

namespace App\Http\Controllers\AssetMaintenance;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ScheduleReportExport;
use App\Exports\IncidentalReportExport;
use App\Exports\PemeliharaanReportExport;
use App\Exports\PemeliharaanDetailReportExport;
use App\Enums\RepairRequestStatus;

class MaintenanceReportController extends Controller
{

    function index(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Pemeliharaan - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $params = [
                "start_date" => $request->start_date,
                "end_date" => $request->end_date,
                "maintenance_category" => $request->maintenance_category,
                "maintenance_type" => $request->maintenance_type,
            ];
            $report = MaintenanceReportController::dataReportActivity($params);
            return view('components/asset-maintenance/report-activity', ["params" => $params, "data" => $report]);
        }
        $title = "Laporan Pemeliharaan Aset";
        $breadcrumbs = ["Pemeliharaan Aset", "Laporan"];
        return view('asset-maintenance.report.index', compact('title', 'breadcrumbs'));
    }

    function detail(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Detail Pemeliharaan - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $params = [
                "start_date" => $request->start_date,
                "end_date" => $request->end_date,
                "maintenance_category" => $request->maintenance_category,
                "maintenance_type" => $request->maintenance_type,
                "filter" => $request->filter,
                "asset_id" => $request->asset_id,
            ];
            $report = MaintenanceReportController::dataReportDetailActivity($params);
            return view('components/asset-maintenance/report-detail-activity', ["params" => $params, "data" => $report]);
        }

        $title = "Detail Pemeliharaan Aset";
        $breadcrumbs = ["Pemeliharaan Aset", "Detail"];
        return view('asset-maintenance.report.detail', compact('title', 'breadcrumbs'));
    }

    function log_incidental(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Isidental - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $params = [
                "start_date" => $request->start_date,
                "end_date" => $request->end_date,
                "room_id" => $request->room_id,
            ];
            $report = MaintenanceReportController::dataReportIcindetal($params);
            $report = collect($report);
            $report = $report->map(function ($activity) {
                $activity->latest_status = RepairRequestStatus::fromShortName($activity->latest_activity_status);
                return $activity;
            });

            return view('components/asset-maintenance/report-log-incidental', ["params" => $params, "data" => $report]);
        }

        $title = "Laporan Perbaikan";
        $breadcrumbs = ["Pemeliharaan Aset", "Laporan"];
        return view('asset-maintenance.report.log_incidental', compact('title', 'breadcrumbs'));
    }

    function jadwal(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Pemeliharaan Jadwal - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $params = [
                "year" => $request->year,
                "maintenance_category" => $request->maintenance_category,
                "maintenance_type" => $request->maintenance_type,
            ];
            $report = MaintenanceReportController::dataReportSchedule($params);
            return view('components/asset-maintenance/report-schedule', ["params" => $params, "data" => $report]);
        }

        $title = "laporan Jadwal";
        $breadcrumbs = ["Pemeliharaan Aset", "Laporan"];
        return view('asset-maintenance.report.jadwal', compact('title', 'breadcrumbs'));
    }

    public function export_detail(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Detail Pemeliharaan - View')) {
            abort(403, "Unauthorized action.");
        }
        $params = [
            "start_date" => $request->start_date,
            "end_date" => $request->end_date,
            "maintenance_category" => $request->maintenance_category,
            "maintenance_type" => $request->maintenance_type,
            "filter" => $request->filter,
            "asset_id" => $request->asset_id,
        ];
        $reportData = $this->dataReportDetailActivity($params);

        return Excel::download(new PemeliharaanDetailReportExport($reportData, $params), 'incidental_report.xlsx');
    }

    public function export_pemeliharaan(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Pemeliharaan - View')) {
            abort(403, "Unauthorized action.");
        }
        $params = [
            "start_date" => $request->start_date,
            "end_date" => $request->end_date,
            "maintenance_category" => $request->maintenance_category,
            "maintenance_type" => $request->maintenance_type,
        ];

        $reportData = $this->dataReportActivity($params);

        return Excel::download(new PemeliharaanReportExport($reportData, $params), 'incidental_report.xlsx');
    }

    public function export_incidental(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Isidental - View')) {
            abort(403, "Unauthorized action.");
        }
        $params = [
            "start_date" => $request->start_date,
            "end_date" => $request->end_date,
            "room_id" => $request->room_id,
        ];

        $reportData = $this->dataReportIcindetal($params);

        return Excel::download(new IncidentalReportExport($reportData, $params), 'incidental_report.xlsx');
    }

    function dataReportActivity($params)
    {
        $query = "
            SELECT SUM(a.activity_cost) AS activity_cost,
                   GROUP_CONCAT(DISTINCT a.activity_name) AS activity_name,
                   GROUP_CONCAT(DISTINCT b.activity_date) AS activity_date,
                   GROUP_CONCAT(DISTINCT b.notes) AS notes,
                   GROUP_CONCAT(DISTINCT b.document_name) AS document_name,
                   d.item_code, d.item_name,
                   e.distributor_name
            FROM maintenance_activity_details a
            INNER JOIN maintenance_activities b ON b.id = a.maintenance_activity_id
            INNER JOIN assets c ON c.id = a.asset_id
            INNER JOIN items d ON d.id = c.item_id
            INNER JOIN distributors e ON e.id = b.distributor_id
            WHERE 1
              AND b.activity_date BETWEEN ? AND ?
        ";

        $bindings = [$params['start_date'], $params['end_date']];
        
        if ($params['maintenance_category'] !== 'all') {
            $query .= " AND b.activity_category = ?";
            $bindings[] = $params['maintenance_category'];
        }

        if (isset($params['maintenance_type']) && $params['maintenance_type'] !== '') {
            $query .= " AND b.activity_type = ?";
            $bindings[] = $params['maintenance_type'];
        }
        $query .= " GROUP BY d.item_code, d.item_name, e.distributor_name";
        return DB::select($query, $bindings);
    }

    function dataReportDetailActivity($params)
    {
        $baseQuery = "
        SELECT a.asset_id, a.activity_name, a.activity_cost,
               b.activity_category, b.activity_type, b.activity_code, b.activity_date, b.notes, b.document_name,
               c.register_code, c.qr_code, c.serial_number, c.asset_code, c.asset_name,
               d.item_code, d.item_name,
               e.distributor_name
        FROM maintenance_activity_details a
        INNER JOIN maintenance_activities b ON b.id = a.maintenance_activity_id
        INNER JOIN assets c ON c.id = a.asset_id
        INNER JOIN items d ON d.id = c.item_id
        INNER JOIN distributors e ON e.id = b.distributor_id
        WHERE 1
    ";

        $bindings = [];
        if ($params['filter'] === "qr") {
            if (isset($params['asset_id'])) {
                $query = $baseQuery . " AND a.asset_id = ?";
                $bindings[] = $params['asset_id'];
            } else {
                return [];
            }
        } else {
            $query = $baseQuery . "
                AND b.activity_category = ?
                AND b.activity_date BETWEEN ? AND ?
            ";
            $bindings[] = $params['maintenance_category'];
            $bindings[] = $params['start_date'];
            $bindings[] = $params['end_date'];

            if (isset($params['maintenance_type'])) {
                $query .= " AND b.activity_type = ?";
                $bindings[] = $params['maintenance_type'];
            }
        }

        return DB::select($query, $bindings);
    }

    function dataReportIcindetal($params)
    {
        $query = "
            SELECT a.*,
                   b.room_name,
                   c.qr_code ,c.register_code ,c.serial_number, c.asset_code, c.asset_name,
                   d.brand, d.type, d.general_specifications,
                   e.item_name, e.item_code
            FROM incidental_requests a
            INNER JOIN rooms b ON b.id = a.room_id
            INNER JOIN assets c ON c.id = a.asset_id
            INNER JOIN asset_entries d ON d.id = c.asset_entry_id
            INNER JOIN items e ON e.id = c.item_id
            WHERE 1
              AND a.request_date BETWEEN ? AND ?
        ";

        $bindings = [$params['start_date'], $params['end_date']];
        if (isset($params['room_id'])) {
            $query .= " AND a.room_id = ?";
            $bindings[] = $params['room_id'];
        }
        return DB::select($query, $bindings);
    }

    function dataReportSchedule($params)
    {
        $query = "
            SELECT a.*,
                   b.schedule_code, b.schedule_category, b.schedule_type, b.schedule_date, b.schedule_notes,
                   d.qr_code ,d.register_code ,d.serial_number, d.asset_code, d.asset_name,
                   e.brand, e.type, e.general_specifications,
                   f.item_name, f.item_code
            FROM maintenance_schedule_details a
            INNER JOIN maintenance_schedules b ON b.id = a.maintenance_schedule_id
            INNER JOIN assets d ON d.id = a.asset_id
            INNER JOIN asset_entries e ON e.id = d.asset_entry_id
            INNER JOIN items f ON f.id = d.item_id
            WHERE 1
        ";

        $bindings = [];
        if (isset($params['maintenance_category'])) {
            $query .= " AND b.schedule_category = ?";
            $bindings[] = $params['maintenance_category'];
        }
        if (isset($params['maintenance_type'])) {
            $query .= " AND b.schedule_type = ?";
            $bindings[] = $params['maintenance_type'];
        }
        if (isset($params['year'])) {
            $query .= " AND YEAR(b.schedule_date) = ?";
            $bindings[] = $params['year'];
        }
        return DB::select($query, $bindings);
    }

    public function export_schedule(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Pemeliharaan Jadwal - View')) {
            abort(403, "Unauthorized action.");
        }
        $params = [
            "year" => $request->year,
            "maintenance_category" => $request->maintenance_category,
            "maintenance_type" => $request->maintenance_type,
        ];
        $reportData = MaintenanceReportController::dataReportSchedule($params);
        return Excel::download(new ScheduleReportExport($reportData, $params), 'schedule_report.xlsx');
    }
}
