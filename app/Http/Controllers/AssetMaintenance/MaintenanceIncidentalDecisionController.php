<?php

namespace App\Http\Controllers\AssetMaintenance;

use App\Enums\RepairRequestStatus;
use App\Http\Controllers\Controller;
use App\Models\IncidentalRequest;
use App\Models\IncidentalRequestActivity;
use App\Models\IncidentalRequestList;
use App\Models\IncidentalRequestOfficer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class MaintenanceIncidentalDecisionController extends Controller
{

    function index()
    {
        if (!hasPermissionInGuard('Putusan Perbaikan - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Keputusan Perbaikan Isidental";
        $breadcrumbs = ["Pemeliharaan Aset", "Isidental", "Perbaikan"];
        return view('asset-maintenance.incidental-decision.index', compact('title', 'breadcrumbs'));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Putusan Perbaikan - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $query = ($request->room_id) ? IncidentalRequest::getIncidentalRequestQuery()->where("incidental_requests.room_id", $request->room_id) : IncidentalRequest::getIncidentalRequestQuery();
            
            // Filter by date range
            if ($request->has('filter_date_start') && $request->filter_date_start != '') {
                $query->where('incidental_requests.request_date', '>=', $request->filter_date_start);
            }
            
            if ($request->has('filter_date_end') && $request->filter_date_end != '') {
                $query->where('incidental_requests.request_date', '<=', $request->filter_date_end);
            }

            return DataTables::eloquent($query)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    $status = $row->latest_activity_status;

                    $disableAccept = true;
                    $disableAssigned = true;
                    $disableBhp = true;
                    $disableVendor = true;
                    $disableFinalDecision = true;
                    if ($status === "OPEN") {
                        $disableAccept = false;
                    }
                    if ($status === "ACCEPTED") {
                        $disableAssigned = false;
                    }
                    if ($status === "ASSIGNED") {
                        $disableBhp = false;
                        $disableVendor = false;
                        $disableFinalDecision = false;
                    }
                    if ($status === "ACCEPTED_BHP") {
                        $disableVendor = false;
                        $disableFinalDecision = false;
                    }
                    if ($status === "VENDOR_CALLED") {
                        $disableFinalDecision = false;
                    }

                    if ($status === "REQUEST_BHP") {
                        $disableBhp = false;
                    }
                    if ($status === "ASSIGN_FIXED") {
                        $disableFinalDecision = false;
                    }
                    if ($status === "ASSIGN_NOT_FIXED") {
                        $disableVendor = false;
                    }
                    if ($status === "VENDOR_FIXED" || $status === "VENDOR_NOT_FIXED") {
                        $disableFinalDecision = true;
                    }

                    $disableFinalDecision = false;
                    if ($status === "OPEN" || $status === "FIXED" || $status === "NOT_FIXED") {
                        $disableFinalDecision = true;
                    }

                    // OVERRIDE
                    $disableAccept = false;
                    $disableAssigned = false;
                    $disableBhp = false;
                    $disableVendor = false;
                    $disableFinalDecision = false;

                    $customButton = '
                        <div class="widget-list-action">
							<a href="#" data-bs-toggle="dropdown" class="text-body text-opacity-50" aria-expanded="false"><i class="fa fa-ellipsis-h fs-14px"></i></a>
							<div class="dropdown-menu dropdown-menu-end" style="">
								<a href="#modal-dialog" class="btn-detail dropdown-item" data-bs-toggle="modal" data-route="' . route("maintenance-asset.incidental.decision.show", $row->id) . '">Detail Request</a>
								<button ' . ($disableAccept ? "disabled" : "") . ' onclick="updateForm(`accept_request`,' . $row->id . ')" class="dropdown-item">Terima Request</button>
								<button ' . ($disableAssigned ? "disabled" : "") . ' onclick="updateForm(`assign_request`,' . $row->id . ')" class="dropdown-item">Penugasan</button>
								<button ' . ($disableBhp ? "disabled" : "") . ' onclick="updateForm(`add_bhp`,' . $row->id . ')" class="dropdown-item">Penyediaan BHP</button>
								<button ' . ($disableVendor ? "disabled" : "") . ' onclick="updateForm(`call_vendor`,' . $row->id . ')" class="dropdown-item">Pemanggilan Vendor</button>
								<div class="dropdown-divider"></div>
								<button ' . ($disableFinalDecision ? "disabled" : "") . ' onclick="updateForm(`final_decision`,' . $row->id . ')" class="dropdown-item">Keputusan Akhir</button>
							</div>
						</div>';
                    return $customButton;
                })
                ->addColumn("latest_status", function ($row) {
                    return RepairRequestStatus::fromShortName($row->latest_activity_status);
                })
                ->make(true);
        }
    }

    function data(IncidentalRequest $incidentalRequest)
    {
        if (!hasPermissionInGuard('Putusan Perbaikan - View')) {
            abort(403, "Unauthorized action.");
        }

        $incidentalRequestDetail = IncidentalRequest::getIncidentalRequestQuery()->where("incidental_requests.id", $incidentalRequest->id)->first();
        $incidentalRequestDetail->latest_status = RepairRequestStatus::fromShortName($incidentalRequestDetail->latest_activity_status);
        $incidentalRequestActivity = IncidentalRequestActivity::where(["incidental_request_id" => $incidentalRequest->id])->get();
        $incidentalRequestActivity = $incidentalRequestActivity->map(function ($activity) {
            $activity->latest_status = RepairRequestStatus::fromShortName($activity->activity_status);
            return $activity;
        });
        return response()->json([
            "incidentalRequestDetail" => $incidentalRequestDetail,
            "incidentalRequestActivity" => $incidentalRequestActivity,
        ], 200);
    }

    function show(IncidentalRequest $incidentalRequest)
    {
        if (!hasPermissionInGuard('Putusan Perbaikan - View')) {
            abort(403, "Unauthorized action.");
        }

        $incidentalRequestDetail = IncidentalRequest::getIncidentalRequestQuery()->where("incidental_requests.id", $incidentalRequest->id)->first();
        $incidentalRequestDetail->latest_status = RepairRequestStatus::fromShortName($incidentalRequestDetail->latest_activity_status);
        $incidentalRequestActivity = IncidentalRequestActivity::where(["incidental_request_id" => $incidentalRequest->id])->get();
        $incidentalRequestActivity = $incidentalRequestActivity->map(function ($activity) {
            $activity->latest_status = RepairRequestStatus::fromShortName($activity->activity_status);
            return $activity;
        });
        $incidentalRequestList = IncidentalRequestList::where(["incidental_request_id" => $incidentalRequest->id])->get()->toArray();
        $incidentalRequestOfficer = IncidentalRequestOfficer::where(["incidental_request_id" => $incidentalRequest->id])->get()->toArray();

        return view('components/asset-maintenance/detail-incidental', [
            "incidentalRequestDetail" => $incidentalRequestDetail,
            "incidentalRequestActivity" => $incidentalRequestActivity,
            "incidentalRequestList" => $incidentalRequestList,
            "incidentalRequestOfficer" => $incidentalRequestOfficer,
        ]);
    }

    function update(Request $request, IncidentalRequest $incidentalRequest)
    {
        if (!hasPermissionInGuard('Putusan Perbaikan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $incidentalRequest->update(["type_update" => $request->type_update]);
        $typeUpdate = $request->type_update;
        $dateTime = date('Y-m-d H:i:s');
        $userId = getAuthUserId();
        $userName = getAuthUserName();

        if ($typeUpdate === "final_decision") {
            $decisionNotice = $request->decision_notice;
            $finalDecision = $request->final_decision_select;

            if ($incidentalRequest->latest_activity_status === $finalDecision) {
                return response()->json(["message" => "error, kondisi sudah berhasil diubah sebelumnya"], 500);
            }

            try {
                DB::beginTransaction();
                $incidentalRequest->update([
                    "latest_activity_time" => $dateTime,
                    "latest_activity_status" => $finalDecision
                ]);
                IncidentalRequestActivity::create([
                    "incidental_request_id" => $incidentalRequest->id,
                    "activity_status" => $finalDecision,
                    "activity_name" => "Pengambilan keputusan terkait perbaikan asset",
                    "activity_notes" => $decisionNotice,
                    "activity_time" => $dateTime,
                    "activity_issue" => null,
                    "activity_document_path" => null,
                    "created_at" => $dateTime,
                    "created_by" => $userId,
                    "created_by_name" => $userName
                ]);

                DB::commit();
                return response()->json(["message" => "Data berhasil disimpan"], 200);
            } catch (\Throwable $th) {
                DB::rollBack();
                return response()->json(["message" => $th->getMessage()], 500);
            }
        }

        if ($typeUpdate === "call_vendor") {
            if ($incidentalRequest->latest_activity_status === "VENDOR_CALLED") {
                return response()->json(["message" => "error, kondisi sudah berhasil diubah sebelumnya"], 500);
            }

            $vendorNotice = $request->vendor_notice;
            try {
                DB::beginTransaction();
                $incidentalRequest->update([
                    "latest_activity_time" => $dateTime,
                    "latest_activity_status" => "VENDOR_CALLED"
                ]);
                IncidentalRequestActivity::create([
                    "incidental_request_id" => $incidentalRequest->id,
                    "activity_status" => "VENDOR_CALLED",
                    "activity_name" => "Pemanggilan Vendor",
                    "activity_notes" => $vendorNotice,
                    "activity_time" => $dateTime,
                    "activity_issue" => null,
                    "activity_document_path" => null,
                    "created_at" => $dateTime,
                    "created_by" => $userId,
                    "created_by_name" => $userName
                ]);

                DB::commit();
                return response()->json(["message" => "Data berhasil disimpan"], 200);
            } catch (\Throwable $th) {
                DB::rollBack();
                return response()->json(["message" => $th->getMessage()], 500);
            }
        }

        if ($typeUpdate === "add_bhp") {
            $bhpNotice = $request->bhp_notice;
            $dataBhp = $request->data_bhp;

            if ($incidentalRequest->latest_activity_status === "ACCEPTED_BHP") {
                return response()->json(["message" => "error, kondisi sudah berhasil diubah sebelumnya"], 500);
            }

            if (count($dataBhp) < 1) {
                return response()->json(["message" => "petugas dibutuhkan"], 500);
            }
            if (!isset($dataBhp[0]['name'])) {
                return response()->json(["message" => "petugas dibutuhkan"], 500);
            }

            try {
                DB::beginTransaction();
                $incidentalRequest->update([
                    "latest_activity_time" => $dateTime,
                    "latest_activity_status" => "ACCEPTED_BHP"
                ]);
                $incidentalRequestActivity = IncidentalRequestActivity::create([
                    "incidental_request_id" => $incidentalRequest->id,
                    "activity_status" => "ACCEPTED_BHP",
                    "activity_name" => "BHP disediakan",
                    "activity_notes" => $bhpNotice,
                    "activity_time" => $dateTime,
                    "activity_issue" => null,
                    "activity_document_path" => null,
                    "created_at" => $dateTime,
                    "created_by" => $userId,
                    "created_by_name" => $userName
                ]);

                foreach ($dataBhp as $b) {
                    IncidentalRequestList::create([
                        "incidental_request_id" => $incidentalRequest->id,
                        "incidental_request_activity_id" => $incidentalRequestActivity->id,
                        "type" => "ACCEPTED_BHP",
                        "name" => $b['name'],
                        "price" => $b['price'],
                        "qty" => $b['qty'],
                    ]);
                }

                DB::commit();
                return response()->json(["message" => "Data berhasil disimpan"], 200);
            } catch (\Throwable $th) {
                DB::rollBack();
                return response()->json(["message" => $th->getMessage()], 500);
            }
        }

        if ($typeUpdate === "assign_request") {
            $assignNotice = $request->assign_notice;
            $dataOfficer = $request->data_officer;

            if ($incidentalRequest->latest_activity_status === "ASSIGNED") {
                return response()->json(["message" => "error, kondisi sudah berhasil diubah sebelumnya"], 500);
            }

            if (count($dataOfficer) < 1) {
                return response()->json(["message" => "petugas dibutuhkan"], 500);
            }
            if (!isset($dataOfficer[0]['id'])) {
                return response()->json(["message" => "petugas dibutuhkan"], 500);
            }

            try {
                DB::beginTransaction();
                $incidentalRequest->update([
                    "latest_activity_time" => $dateTime,
                    "latest_activity_status" => "ASSIGNED"
                ]);
                $incidentalRequestActivity = IncidentalRequestActivity::create([
                    "incidental_request_id" => $incidentalRequest->id,
                    "activity_status" => "ASSIGNED",
                    "activity_name" => "Penugasan petugas",
                    "activity_notes" => $assignNotice,
                    "activity_time" => $dateTime,
                    "activity_issue" => null,
                    "activity_document_path" => null,
                    "created_at" => $dateTime,
                    "created_by" => $userId,
                    "created_by_name" => $userName
                ]);

                foreach ($dataOfficer as $o) {
                    IncidentalRequestOfficer::create([
                        "incidental_request_id" => $incidentalRequest->id,
                        "incidental_request_activity_id" => $incidentalRequestActivity->id,
                        "employee_id" => $o['id'],
                        "employee_name" => $o['name'],
                    ]);
                }

                DB::commit();
                return response()->json(["message" => "Data berhasil disimpan"], 200);
            } catch (\Throwable $th) {
                DB::rollBack();
                return response()->json(["message" => $th->getMessage()], 500);
            }
        }

        if ($typeUpdate === "accept_request") {
            if ($incidentalRequest->latest_activity_status === "ACCEPTED") {
                return response()->json(["message" => "error, kondisi sudah berhasil diubah sebelumnya"], 500);
            }

            try {
                DB::beginTransaction();
                $incidentalRequest->update([
                    "latest_activity_time" => $dateTime,
                    "latest_activity_status" => "ACCEPTED"
                ]);
                IncidentalRequestActivity::create([
                    "incidental_request_id" => $incidentalRequest->id,
                    "activity_status" => "ACCEPTED",
                    "activity_name" => "Request Isidental Diterima",
                    "activity_notes" => null,
                    "activity_time" => $dateTime,
                    "activity_issue" => null,
                    "activity_document_path" => null,
                    "created_at" => $dateTime,
                    "created_by" => $userId,
                    "created_by_name" => $userName
                ]);
                DB::commit();
                return response()->json(["message" => "Data berhasil disimpan"], 200);
            } catch (\Throwable $th) {
                DB::rollBack();
                return response()->json(["message" => $th->getMessage()], 500);
            }
        }
    }
}
