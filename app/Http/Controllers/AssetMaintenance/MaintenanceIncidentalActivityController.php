<?php

namespace App\Http\Controllers\AssetMaintenance;

use App\Enums\RepairRequestStatus;
use App\Http\Controllers\Controller;
use App\Models\IncidentalRequest;
use App\Models\IncidentalRequestActivity;
use App\Models\IncidentalRequestList;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class MaintenanceIncidentalActivityController extends Controller
{
   
    function index()
    {
        if (!hasPermissionInGuard('Aktivitas Perbaikan - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Aktivitas Perbaikan Isidental";
        $breadcrumbs = ["Pemeliharaan Aset", "Isidental", "Perbaikan"];
        return view('asset-maintenance.incidental-activity.index', compact('title', 'breadcrumbs'));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Aktivitas Perbaikan - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $query = ($request->room_id) ? IncidentalRequest::getIncidentalRequestQuery()->where("incidental_requests.room_id", $request->room_id) : IncidentalRequest::getIncidentalRequestQuery();
            
            // Filter by date range
            if ($request->has('filter_date_start') && $request->filter_date_start != '') {
                $query->where('incidental_requests.request_date', '>=', $request->filter_date_start);
            }
            
            if ($request->has('filter_date_end') && $request->filter_date_end != '') {
                $query->where('incidental_requests.request_date', '<=', $request->filter_date_end);
            }

            return DataTables::eloquent($query)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    $status = $row->latest_activity_status;
                    $disableInspectionRequest = true;
                    $disableInspectionResult = true;
                    $disableRequestBhp = true;
                    $disableVendorResult = true;

                    // IPFRS
                    if ($status === "ASSIGNED") {
                        $disableInspectionRequest = false;
                    }
                    if ($status === "ACCEPTED_BHP") {
                        $disableInspectionResult = false;
                    }
                    if ($status === "VENDOR_CALLED") {
                        $disableVendorResult = false;
                    }

                    // PETUGAS
                    if ($status === "INSPECTION") {
                        $disableRequestBhp = false;
                        $disableInspectionResult = false;
                    }
                    if ($status === "REQUEST_BHP") {
                        $disableInspectionResult = false;
                    }
                    if ($status === "ASSIGN_FIXED" || $status === "ASSIGN_NOT_FIXED") {
                    }

                    // OVERRIDE
                    $disableInspectionRequest = false;
                    $disableInspectionResult = false;
                    $disableRequestBhp = false;
                    $disableVendorResult = false;

                    $customButton = '
                        <div class="widget-list-action">
							<a href="#" data-bs-toggle="dropdown" class="text-body text-opacity-50" aria-expanded="false"><i class="fa fa-ellipsis-h fs-14px"></i></a>
							<div class="dropdown-menu dropdown-menu-end" style="">
								<a href="#modal-dialog" class="btn-detail dropdown-item" data-bs-toggle="modal" data-route="' . route("maintenance-asset.incidental.decision.show", $row->id) . '">Detail Request</a>
								<button ' . ($disableInspectionRequest ? "disabled" : "") . ' onclick="updateForm(`inspection_request`,' . $row->id . ')" class="dropdown-item">Lakukan Pengecekan</button>
								<button ' . ($disableInspectionResult ? "disabled" : "") . ' onclick="updateForm(`inspection_result`,' . $row->id . ')" class="dropdown-item">Hasil Pengecekan</button>
								<button ' . ($disableRequestBhp ? "disabled" : "") . ' onclick="updateForm(`request_bhp`,' . $row->id . ')" class="dropdown-item">Request BHP</button>
								<button ' . ($disableVendorResult ? "disabled" : "") . ' onclick="updateForm(`vendor_result`,' . $row->id . ')" class="dropdown-item">Report Hasil Vendor</button>
							</div>
						</div>';
                    return $customButton;
                })
                ->addColumn("latest_status", function ($row) {
                    return RepairRequestStatus::fromShortName($row->latest_activity_status);
                })
                ->make(true);
        }
    }

    function update(Request $request, IncidentalRequest $incidentalRequest)
    {
        if (!hasPermissionInGuard('Aktivitas Perbaikan - Action')) {
            abort(403, "Unauthorized action.");
        }
        $incidentalRequest->update(["type_update" => $request->type_update]);
        $typeUpdate = $request->type_update;
        $dateTime = date('Y-m-d H:i:s');

        $userId = getAuthUserId();
        $userName = getAuthUserName();

        if ($typeUpdate === "vendor_result") {
            if ($incidentalRequest->latest_activity_status === "VENDOR_FIXED" || $incidentalRequest->latest_activity_status === "VENDOR_NOT_FIXED") {
                return response()->json(["message" => "error, kondisi sudah berhasil diubah sebelumnya"], 500);
            }

            $vendorNotice = $request->vendor_result_notice;
            $vendorResultSelect = $request->final_vendor_result_select;
            $activityName = ($vendorResultSelect === "VENDOR_FIXED") ? "Berhasil diperbaiki vendor" : "Tidak dapat diperbaiki vendor";

            try {
                DB::beginTransaction();
                $incidentalRequest->update([
                    "latest_activity_time" => $dateTime,
                    "latest_activity_status" => $vendorResultSelect
                ]);
                IncidentalRequestActivity::create([
                    "incidental_request_id" => $incidentalRequest->id,
                    "activity_status" => $vendorResultSelect,
                    "activity_name" => $activityName,
                    "activity_notes" => $vendorNotice,
                    "activity_time" => $dateTime,
                    "activity_issue" => null,
                    "activity_document_path" => null,
                    "created_at" => $dateTime,
                    "created_by" => $userId,
                    "created_by_name" => $userName
                ]);

                DB::commit();
                return response()->json(["message" => "Data berhasil disimpan"], 200);
            } catch (\Throwable $th) {
                DB::rollBack();
                return response()->json(["message" => $th->getMessage()], 500);
            }
        }

        if ($typeUpdate === "request_bhp") {
            if ($incidentalRequest->latest_activity_status === "REQUEST_BHP") {
                return response()->json(["message" => "error, kondisi sudah berhasil diubah sebelumnya"], 500);
            }

            $requestBhpNotice = $request->request_bhp_notice;
            try {
                DB::beginTransaction();
                $incidentalRequest->update([
                    "latest_activity_time" => $dateTime,
                    "latest_activity_status" => "REQUEST_BHP"
                ]);
                IncidentalRequestActivity::create([
                    "incidental_request_id" => $incidentalRequest->id,
                    "activity_status" => "REQUEST_BHP",
                    "activity_name" => "Petugas request BHP",
                    "activity_notes" => $requestBhpNotice,
                    "activity_time" => $dateTime,
                    "activity_issue" => null,
                    "activity_document_path" => null,
                    "created_at" => $dateTime,
                    "created_by" => $userId,
                    "created_by_name" => $userName
                ]);

                DB::commit();
                return response()->json(["message" => "Data berhasil disimpan"], 200);
            } catch (\Throwable $th) {
                DB::rollBack();
                return response()->json(["message" => $th->getMessage()], 500);
            }
        }

        if ($typeUpdate === "inspection_result") {
            if ($incidentalRequest->latest_activity_status === "ASSIGN_FIXED" || $incidentalRequest->latest_activity_status === "ASSIGN_NOT_FIXED") {
                return response()->json(["message" => "error, kondisi sudah berhasil diubah sebelumnya"], 500);
            }

            $bhpNotice = $request->inspection_result_notice;
            $dataBhp = $request->data_inspection_result;
            $inspectionResultSelect = $request->inspection_result_select;
            $activityName = ($inspectionResultSelect === "ASSIGN_FIXED") ? "Berhasil diperbaiki petugas" : "Tidak dapat diperbaiki petugas";

            try {
                DB::beginTransaction();
                $incidentalRequest->update([
                    "latest_activity_time" => $dateTime,
                    "latest_activity_status" => $inspectionResultSelect
                ]);
                $incidentalRequestActivity = IncidentalRequestActivity::create([
                    "incidental_request_id" => $incidentalRequest->id,
                    "activity_status" => $inspectionResultSelect,
                    "activity_name" => $activityName,
                    "activity_notes" => $bhpNotice,
                    "activity_time" => $dateTime,
                    "activity_issue" => null,
                    "activity_document_path" => null,
                    "created_at" => $dateTime,
                    "created_by" => $userId,
                    "created_by_name" => $userName
                ]);

                if ($inspectionResultSelect === "ASSIGN_FIXED") {
                    foreach ($dataBhp as $b) {
                        IncidentalRequestList::create([
                            "incidental_request_id" => $incidentalRequest->id,
                            "incidental_request_activity_id" => $incidentalRequestActivity->id,
                            "type" => "ASSIGN_FIXED",
                            "name" => $b['name'],
                            "price" => $b['price'],
                            "qty" => $b['qty'],
                        ]);
                    }
                }

                DB::commit();
                return response()->json(["message" => "Data berhasil disimpan"], 200);
            } catch (\Throwable $th) {
                DB::rollBack();
                return response()->json(["message" => $th->getMessage()], 500);
            }
        }

        if ($typeUpdate === "inspection_request") {
            if ($incidentalRequest->latest_activity_status === "INSPECTION") {
                return response()->json(["message" => "error, kondisi sudah berhasil diubah sebelumnya"], 500);
            }

            try {
                DB::beginTransaction();
                $incidentalRequest->update([
                    "latest_activity_time" => $dateTime,
                    "latest_activity_status" => "INSPECTION"
                ]);
                IncidentalRequestActivity::create([
                    "incidental_request_id" => $incidentalRequest->id,
                    "activity_status" => "INSPECTION",
                    "activity_name" => "Request diterima petugas",
                    "activity_notes" => null,
                    "activity_time" => $dateTime,
                    "activity_issue" => null,
                    "activity_document_path" => null,
                    "created_at" => $dateTime,
                    "created_by" => $userId,
                    "created_by_name" => $userName
                ]);
                DB::commit();
                return response()->json(["message" => "Data berhasil disimpan"], 200);
            } catch (\Throwable $th) {
                DB::rollBack();
                return response()->json(["message" => $th->getMessage()], 500);
            }
        }
    }
}
