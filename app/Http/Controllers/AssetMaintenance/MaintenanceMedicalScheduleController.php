<?php

namespace App\Http\Controllers\AssetMaintenance;

use App\Http\Controllers\Controller;
use App\Models\Asset;
use App\Models\MaintenanceSchedule;
use App\Models\MaintenanceScheduleDetail;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class MaintenanceMedicalScheduleController extends Controller
{
    protected $category;

    public function __construct()
    {
        $this->category = "ALKES";
    }

    public function index()
    {
        if (!hasPermissionInGuard('Jadwal Alkes - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Jadwal Pemeliharaan Alkes";
        $breadcrumbs = ["Pemeliharaan Aset", "Alkes", "Jadwal"];

        return view("asset-maintenance.medical-schedule.index", compact("title", "breadcrumbs"));
    }

    public function create()
    {
        if (!hasPermissionInGuard('Jadwal Alkes - Action')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Jadwal Pemeliharaan Alkes";
        $breadcrumbs = ["Pemeliharaan Aset", "<PERSON>kes", "Jadwal"];

        return view("asset-maintenance.medical-schedule.create", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Jadwal Alkes - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $query = MaintenanceSchedule::getMaintenanceScheduleQuery($this->category);

            // Filter by kode barang (item_id)
            if ($request->has('filter_kode_barang') && $request->filter_kode_barang !== '' && $request->filter_kode_barang !== null) {
                $query->where('maintenance_schedules.item_id', $request->filter_kode_barang);
            }

            // Filter by tanggal mulai
            if ($request->has('filter_tanggal_mulai') && $request->filter_tanggal_mulai !== '' && $request->filter_tanggal_mulai !== null) {
                $query->where('maintenance_schedules.schedule_date', '>=', $request->filter_tanggal_mulai);
            }

            // Filter by tanggal akhir
            if ($request->has('filter_tanggal_akhir') && $request->filter_tanggal_akhir !== '' && $request->filter_tanggal_akhir !== null) {
                $query->where('maintenance_schedules.schedule_date', '<=', $request->filter_tanggal_akhir);
            }

            return DataTables::eloquent($query)
                ->addIndexColumn()
                ->filter(function ($query) {
                    if (request()->has('search') && request('search')['value']) {
                        $searchValue = request('search')['value'];
                        $query->where(function ($q) use ($searchValue) {
                            $q->where('maintenance_schedules.schedule_code', 'like', '%' . $searchValue . '%')
                                ->orWhere('maintenance_schedules.schedule_notes', 'like', '%' . $searchValue . '%')
                                ->orWhere('maintenance_schedules.schedule_type', 'like', '%' . $searchValue . '%')
                                ->orWhere('items.item_name', 'like', '%' . $searchValue . '%');
                        });
                    }
                })
                ->addColumn("action", function ($row) {
                    return '<a href="#modal-dialog" class="btn btn-sm btn-outline-primary btn-detail" data-bs-toggle="modal" data-route="' . route("maintenance-asset.alkes.schedule.show", $row->id) . '"><i class="fas fa-search"></i> Lihat Detail</a> 
                        <button class="btn btn-sm btn-outline-danger btn-delete" data-route="' . route("maintenance-asset.alkes.schedule.destroy", $row->id) . '"><i class="fas fa-trash"></i> Hapus Data </button>';
                })
                ->filterColumn('room_name', function ($query, $keyword) {
                    $query->where('rooms.room_name', 'like', "%{$keyword}%");
                })
                ->rawColumns(["action", "schedule_type"])
                ->make(true);
        }
    }



    function store(Request $request)
    {
        if (!hasPermissionInGuard('Jadwal Alkes - Action')) {
            abort(403, "Unauthorized action.");
        }
        if (!$request->schedule_type) {
            return response()->json(['message' => 'error, Tipe Pemelihraan dibutuhkan'], 400);
        }
        if (!$request->item_id) {
            return response()->json(['message' => 'error, Barang dibutuhkan'], 400);
        }
        if (!$request->asset_id) {
            return response()->json(['message' => 'error, data asset dibutuhkan'], 400);
        }

        $assets = Asset::with(["item", "assetEntry"])->whereIn("id", $request->asset_id)->get()->keyBy('id');
        $userId = getAuthUserId();
        $userName = getAuthUserName();

        try {
            DB::beginTransaction();

            $scheduleId = MaintenanceSchedule::create([
                "schedule_code" => "SCH-" . date('ymd') . "." . random_int(10000000, 99999999),
                "schedule_category" => $this->category,
                "schedule_type" => $request->schedule_type,
                "schedule_date" => $request->schedule_date,
                "schedule_notes" => $request->notes,
                "schedule_quantity" => count($request->asset_id),
                "item_id" => $request->item_id,
                "created_by" => $userId,
                "created_by_name" => $userName,
                "created_at" => date('Y-m-d H:i:s')
            ]);

            foreach ($request->asset_id as $key => $assetId) {
                MaintenanceScheduleDetail::create([
                    "maintenance_schedule_id" => $scheduleId->id,
                    "asset_id" => $assets[$assetId]->id,
                    "notes" => $request->asset_notes[$key] ?? "",
                    "room_id" => $assets[$assetId]->document_room_id
                ]);
            }

            DB::commit();

            return response()->json(["message" => "Data berhasil disimpan"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(['message' => 'error, proses penyimpanan data error'], 400);
        }
    }

    function destroy(MaintenanceSchedule $maintenanceSchedule)
    {
        if (!hasPermissionInGuard('Jadwal Alkes - Action')) {
            abort(403, "Unauthorized action.");
        }
        try {
            DB::beginTransaction();
            MaintenanceScheduleDetail::where('maintenance_schedule_id', $maintenanceSchedule->id)->delete();
            $maintenanceSchedule->delete();
            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus"
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function show(MaintenanceSchedule $maintenanceSchedule)
    {
        if (!hasPermissionInGuard('Jadwal Alkes - View')) {
            abort(403, "Unauthorized action.");
        }
        $maintenanceScheduleDetail = MaintenanceScheduleDetail::select(
            'maintenance_schedule_details.*',
            'assets.qr_code',
            'assets.register_code',
            'assets.serial_number',
            'assets.asset_code',
            'assets.asset_name',
            'items.item_code',
            'items.item_name',
            'rooms.room_code',
            'rooms.room_name'
        )
            ->join("assets", "assets.id", "=", "maintenance_schedule_details.asset_id")
            ->join("items", "items.id", "=", "assets.item_id")
            ->leftJoin('rooms', 'assets.document_room_id', '=', 'rooms.id')
            ->where(["maintenance_schedule_id" => $maintenanceSchedule->id])->get();

        return view('components/asset-maintenance/detail-schedule', [
            "maintenanceSchedule" => $maintenanceSchedule,
            "maintenanceScheduleDetail" => $maintenanceScheduleDetail
        ]);
    }
}
