<?php

namespace App\Http\Controllers\AssetMaintenance;

use App\Models\Asset;
use App\Models\Distributor;
use App\Models\MaintenanceActivity;
use App\Models\MaintenanceActivityDetail;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class MaintenanceNonMedicalActivityController extends Controller
{
    protected $category;

    public function __construct()
    {
        $this->category = "NON_ALKES";
    }

    public function index()
    {
        if (!hasPermissionInGuard('Jadwal Non Alkes - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Kegiatan Pemeliharaan Non Alkes";
        $breadcrumbs = ["Pemeliharaan Aset", "Non Alkes", "Kegiatan"];

        return view("asset-maintenance.nonmedical-activity.index", compact("title", "breadcrumbs"));
    }

    public function create()
    {
        if (!hasPermissionInGuard('Jadwal Non Alkes - Action')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Pemeliharaan Non Alkes";
        $breadcrumbs = ["Pemeliharaan Aset", "Non Alkes", "Kegiatan"];

        return view("asset-maintenance.nonmedical-activity.create", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Jadwal Non Alkes - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $query = MaintenanceActivity::getMaintenanceActivityQuery($this->category);

            // Filter by distributor
            if ($request->has('filter_distributor') && $request->filter_distributor !== '' && $request->filter_distributor !== null) {
                $query->where('maintenance_activities.distributor_id', $request->filter_distributor);
            }

            // Filter by tanggal mulai
            if ($request->has('filter_tanggal_mulai') && $request->filter_tanggal_mulai !== '' && $request->filter_tanggal_mulai !== null) {
                $query->where('maintenance_activities.activity_date', '>=', $request->filter_tanggal_mulai);
            }

            // Filter by tanggal akhir
            if ($request->has('filter_tanggal_akhir') && $request->filter_tanggal_akhir !== '' && $request->filter_tanggal_akhir !== null) {
                $query->where('maintenance_activities.activity_date', '<=', $request->filter_tanggal_akhir);
            }

            return DataTables::eloquent($query)
                ->addIndexColumn()
                ->filter(function ($query) {
                    if (request()->has('search') && request('search')['value']) {
                        $searchValue = request('search')['value'];
                        $query->where(function ($q) use ($searchValue) {
                            $q->where('maintenance_activities.activity_code', 'like', '%' . $searchValue . '%')
                                ->orWhere('maintenance_activities.notes', 'like', '%' . $searchValue . '%')
                                ->orWhere('maintenance_activities.activity_type', 'like', '%' . $searchValue . '%')
                                ->orWhere('distributors.distributor_name', 'like', '%' . $searchValue . '%');
                        });
                    }
                })
                ->addColumn("action", function ($row) {
                    return '<a href="#modal-dialog" class="btn btn-sm btn-outline-primary btn-detail" data-bs-toggle="modal" data-route="' . route("maintenance-asset.non-alkes.activity.show", $row->id) . '"><i class="fas fa-search"></i> Lihat Detail</a> 
                        <button class="btn btn-sm btn-outline-danger btn-delete" data-route="' . route("maintenance-asset.non-alkes.activity.destroy", $row->id) . '"><i class="fas fa-trash"></i> Hapus Data </button>';
                })
                ->rawColumns(["action", "activity_type"])
                ->make(true);
        }
    }

    public function store(Request $request)
    {
        if (!hasPermissionInGuard('Jadwal Non Alkes - Action')) {
            abort(403, "Unauthorized action.");
        }
        if (!$request->maintenance_type) {
            return response()->json(['message' => 'error, Tipe Pemelihraan dibutuhkan'], 400);
        }
        if (!$request->distributor) {
            return response()->json(['message' => 'error, data Distributor dibutuhkan'], 400);
        }
        if (!$request->document_name) {
            return response()->json(['message' => 'error, Nama Bukti Pemeliharaan dibutuhkan'], 400);
        }
        if (!$request->asset_id) {
            return response()->json(['message' => 'error, data form minimal memiliki 1 data asset'], 400);
        }
        $assets = Asset::with(["item", "assetEntry"])->whereIn("id", $request->asset_id)->get()->keyBy('id');

        $totalMaintenanceCost = 0;
        foreach ($request->asset_id as $key => $assetId) {
            $totalMaintenanceCost += str_replace(',', '', $request->maintenance_cost[$key]);
        }

        try {
            DB::beginTransaction();

            $file_docs = $request->file("document");
            $fileDocsUrl = null;
            if ($file_docs) {
                $folder = "public/maintenance_assets/activity";
                $fileDocsUrl = $file_docs->storeAs($folder, now()->format("YmdHis") . "_kegiatan_pemeliharaan" . "." . $file_docs->extension());
            }

            $userId = getAuthUserId();
            $userName = getAuthUserName();

            $activityId = MaintenanceActivity::create([
                "activity_category" => $this->category,
                "distributor_id" => $request->distributor,
                "activity_type" => $request->maintenance_type,
                "activity_code" => "ACT-" . date('ymd') . "." . random_int(10000000, 99999999),
                "activity_date" => $request->activity_date,
                "total_activity_cost" => $totalMaintenanceCost,
                "notes" => $request->notes,
                "document_name" => $request->document_name,
                "document_path" => $fileDocsUrl,
                "created_by" => $userId,
                "created_by_name" => $userName,
                "created_at" => date('Y-m-d H:i:s')
            ]);

            foreach ($request->asset_id as $key => $assetId) {
                MaintenanceActivityDetail::create([
                    "maintenance_activity_id" => $activityId->id,
                    "asset_id" => $assets[$assetId]->id,
                    "activity_name" => $request->asset_notes[$key] ?? "",
                    "activity_cost" => str_replace(',', '', $request->maintenance_cost[$key])
                ]);
            }

            DB::commit();

            return response()->json(["message" => "Data berhasil disimpan"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(['message' => 'error, proses penyimpanan data error'], 400);
        }
    }

    function destroy(MaintenanceActivity $maintenanceActivity)
    {
        if (!hasPermissionInGuard('Jadwal Non Alkes - Action')) {
            abort(403, "Unauthorized action.");
        }
        try {
            DB::beginTransaction();
            MaintenanceActivityDetail::where('maintenance_activity_id', $maintenanceActivity->id)->delete();
            $maintenanceActivity->delete();
            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus"
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function show(MaintenanceActivity $maintenanceActivity)
    {
        if (!hasPermissionInGuard('Jadwal Non Alkes - View')) {
            abort(403, "Unauthorized action.");
        }
        $maintenanceActivityDetail = MaintenanceActivityDetail::select(
            'maintenance_activity_details.*',
            'assets.qr_code',
            'assets.register_code',
            'assets.serial_number',
            'assets.asset_code',
            'assets.asset_name',
            'items.item_code',
            'items.item_name'
        )
            ->join("assets", "assets.id", "=", "maintenance_activity_details.asset_id")
            ->join("items", "items.id", "=", "assets.item_id")
            ->where(["maintenance_activity_id" => $maintenanceActivity->id])->get();
        $distributor = Distributor::where(["id" => $maintenanceActivity->distributor_id])->first();

        return view('components/asset-maintenance/detail-activity', [
            "maintenanceActivity" => $maintenanceActivity,
            "maintenanceActivityDetail" => $maintenanceActivityDetail,
            "distributor" => $distributor,
        ]);
    }
}
