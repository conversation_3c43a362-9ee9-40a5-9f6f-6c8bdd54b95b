<?php

namespace App\Http\Controllers\AssetMaintenance;

use App\Models\Asset;
use App\Models\IncidentalRequest;
use App\Models\IncidentalRequestActivity;
use App\Http\Controllers\Controller;
use App\Models\IncidentalRequestList;
use App\Models\IncidentalRequestOfficer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use App\Enums\RepairRequestStatus;

class MaintenanceIncidentalRequestController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Request Perbaikan - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Request Perbaikan Isidental";
        $breadcrumbs = ["Pemeliharaan Aset", "Isidental", "Request"];
        return view('asset-maintenance.incidental-request.index', compact('title', 'breadcrumbs'));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Request Perbaikan - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $query = ($request->room_id) ? IncidentalRequest::getIncidentalRequestQuery()->where("incidental_requests.room_id", $request->room_id) : IncidentalRequest::getIncidentalRequestQuery();
            
            // Filter by date range
            if ($request->has('filter_date_start') && $request->filter_date_start != '') {
                $query->where('incidental_requests.request_date', '>=', $request->filter_date_start);
            }
            
            if ($request->has('filter_date_end') && $request->filter_date_end != '') {
                $query->where('incidental_requests.request_date', '<=', $request->filter_date_end);
            }
            
            return DataTables::eloquent($query)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    $detailButton = '<a href="#modal-dialog" class="btn btn-sm btn-outline-primary btn-detail" data-bs-toggle="modal" data-route="' . route("maintenance-asset.incidental.request.show", $row->id) . '"><i class="fas fa-search"></i></a>';
                    $deleteButton = '<button type="button" class="btn btn-sm btn-outline-danger btn-delete" data-route="' . route("maintenance-asset.incidental.request.destroy", $row->id) . '"><i class="fas fa-trash"></i></button>';
                    return ($row->latest_activity_status !== "OPEN") ? $detailButton : $detailButton . ' ' . $deleteButton;
                })
                ->addColumn("latest_status", function ($row) {
                    return RepairRequestStatus::fromShortName($row->latest_activity_status);
                })
                ->make(true);
        }
    }

    function show(IncidentalRequest $incidentalRequest)
    {
        if (!hasPermissionInGuard('Request Perbaikan - View')) {
            abort(403, "Unauthorized action.");
        }
        $incidentalRequestDetail = IncidentalRequest::getIncidentalRequestQuery()->where("incidental_requests.id", $incidentalRequest->id)->first();
        $incidentalRequestDetail->latest_status = RepairRequestStatus::fromShortName($incidentalRequestDetail->latest_activity_status);
        $incidentalRequestActivity = IncidentalRequestActivity::where(["incidental_request_id" => $incidentalRequest->id])->get();
        $incidentalRequestActivity = $incidentalRequestActivity->map(function ($activity) {
            $activity->latest_status = RepairRequestStatus::fromShortName($activity->activity_status);
            return $activity;
        });
        $incidentalRequestList = IncidentalRequestList::where(["incidental_request_id" => $incidentalRequest->id])->get()->toArray();
        $incidentalRequestOfficer = IncidentalRequestOfficer::where(["incidental_request_id" => $incidentalRequest->id])->get()->toArray();

        return view('components/asset-maintenance/detail-incidental', [
            "incidentalRequestDetail" => $incidentalRequestDetail,
            "incidentalRequestActivity" => $incidentalRequestActivity,
            "incidentalRequestList" => $incidentalRequestList,
            "incidentalRequestOfficer" => $incidentalRequestOfficer,
        ]);
    }

    function destroy(IncidentalRequest $incidentalRequest)
    {
        if (!hasPermissionInGuard('Request Perbaikan - Action')) {
            abort(403, "Unauthorized action.");
        }
        try {
            DB::beginTransaction();
            IncidentalRequestActivity::where('incidental_request_id', $incidentalRequest->id)->delete();
            $incidentalRequest->delete();
            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus"
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function create()
    {
        if (!hasPermissionInGuard('Request Perbaikan - Action')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Form Perbaikan Isidental";
        $breadcrumbs = ["Form Perbaikan Isidental", "Form Perbaikan Isidental"];
        return view('asset-maintenance.incidental-request.create', compact('title', 'breadcrumbs'));
    }

    public function store(Request $request)
    {
        if (!hasPermissionInGuard('Request Perbaikan - Action')) {
            abort(403, "Unauthorized action.");
        }
        if (!$request->target_ruangan) {
            return response()->json(['message' => 'error, Ruangan dibutuhkan'], 400);
        }
        if (!$request->jenis_kerusakan) {
            return response()->json(['message' => 'error, Jenis Kerusakan dibutuhkan'], 400);
        }
        if (!$request->asset_id) {
            return response()->json(['message' => 'error, data asset dibutuhkan'], 400);
        }
        $assets = Asset::with(["item", "assetEntry"])->whereIn("id", $request->asset_id)->get()->keyBy('id');

        $item_pic_id = null;
        $item_pic_name = null;
        $item_pic_identification_number = null;
        $item_pic_grade = null;
        $item_pic_position = null;
        $query = DB::table('items')
            ->leftJoin('categories', 'items.category_id', '=', 'categories.id')
            ->leftJoin('employees', 'categories.pic_category', '=', 'employees.id')
            ->where('items.id', $assets[$request->asset_id[0]]->item_id)
            ->select('employees.id', 'employees.employee_name', 'employees.employee_identification_number', 'employees.employee_grade', 'employees.employee_position')
            ->first();
        if ($query) {
            $item_pic_id = $query->id;
            $item_pic_name = $query->employee_name;
            $item_pic_identification_number = $query->employee_identification_number;
            $item_pic_grade = $query->employee_grade;
            $item_pic_position = $query->employee_position;
        }

        $dateTime = date('Y-m-d H:i:s');
        $userId = getAuthUserId();
        $userName = getAuthUserName();

        try {
            DB::beginTransaction();

            $file_docs = $request->file("document");
            $fileDocsUrl = null;
            if ($file_docs) {
                $folder = "maintenance_assets/incidental/request";
                $fileDocsUrl = $file_docs->storeAs($folder, now()->format("YmdHis") . "_request_isidental" . "." . $file_docs->extension());
            }

            $assetId = $request->asset_id[0];
            $requestData = IncidentalRequest::create([
                "request_code" => "IRQ-" . date('ymd') . "." . random_int(10000000, 99999999),
                "asset_id" => $assets[$assetId]->id,
                "room_id" => $assets[$assetId]->document_room_id,
                "request_date" => date("Y-m-d"),
                "request_issue" => $request->jenis_kerusakan,
                "latest_activity_time" => $dateTime,
                "latest_activity_status" => "OPEN",
                "item_pic_id" => $item_pic_id,
                "item_pic_name" => $item_pic_name,
                "item_pic_identification_number" => $item_pic_identification_number,
                "item_pic_grade" => $item_pic_grade,
                "item_pic_position" => $item_pic_position,
                "created_at" => $dateTime,
                "created_by" => $userId,
                "created_by_name" => $userName
            ]);

            IncidentalRequestActivity::create([
                "incidental_request_id" => $requestData->id,
                "activity_status" => "OPEN",
                "activity_name" => "Request Isidental Dibuat",
                "activity_notes" => null,
                "activity_time" => $dateTime,
                "activity_issue" => $request->jenis_kerusakan,
                "activity_document_path" => $fileDocsUrl,
                "created_at" => $dateTime,
                "created_by" => $userId,
                "created_by_name" => $userName
            ]);

            DB::commit();

            return response()->json(["message" => "Data berhasil disimpan"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(['message' => 'error, proses penyimpanan data error'], 400);
        }
    }
}
