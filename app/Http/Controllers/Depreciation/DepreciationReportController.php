<?php

namespace App\Http\Controllers\Depreciation;

use App\Http\Controllers\Controller;
use App\Models\Room;
use App\Models\Asset;
use Illuminate\Http\Request;

class DepreciationReportController extends Controller
{
    function index()
    {
        $title = "Laporan Penyusutan Aset";
        $breadcrumbs = ["Laporan Penyusutan Aset"];
        $exportRoute = route('depreciation.report.export'); // Sesuaikan dengan route export Anda

        return view('depreciation.report.index', compact('title', 'breadcrumbs', 'exportRoute'));
    }

    function getData(Request $request)
    {
        $room = null;
        if ($request->room) {
            $room = Room::find($request->room);
        }

        $data = Asset::with(["assetEntry", "item"])
                ->join('items', 'assets.item_id', '=', 'items.id')
                ->select('assets.*', 'items.depreciation_year')
                ->where("assets.category_type", "EQUIPMENT")
                ->when($request->room, function($query) use ($request) {
                    return $query->where('assets.document_room_id', $request->room);
                })
                ->when($request->year, function($query) use ($request) {
                    return $query->whereHas('assetEntry', function($query) use ($request) {
                        $query->whereYear('received_date', $request->year);
                    });
                })
                ->when($request->item_id, function($query) use ($request) {
                    return $query->where('assets.item_id', $request->item_id);
                })
                ->get();

        return view('components.depreciation.report-data', compact('data', 'room'));
    }

    function export(Request $request)
    {
        // Logika export
        // Sesuaikan dengan kebutuhan export Anda
    }
}
