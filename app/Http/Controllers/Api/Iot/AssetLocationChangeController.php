<?php

namespace App\Http\Controllers\Api\Iot;

use App\Http\Controllers\Controller;
use App\Models\Asset;
use App\Models\AssetLocationHistory;
use App\Models\Room;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AssetLocationChangeController extends Controller
{
    function location_change(Request $request) : JsonResponse
    {
        $requestApiKey = $request->header("Api-Key");
        $apiKey = env("API_KEY");

        if ($requestApiKey != $apiKey) {
            return response()->json([
                "status" => "error",
                "message" => "Unauthorized"
            ], 401);
        }

        if (!$request->rfid || !$request->device_id) {
            return response()->json([
                "status" => "error",
                "message" => "rfid_code or device_id are required"
            ], 400);
        }

        try {
            DB::beginTransaction();

            $assets = Asset::whereIn("rfid_tag", $request->rfid)->get();
            $room = Room::firstWhere("device_id", $request->device_id);

            if (!$room) {
                return response()->json([
                    "status" => "error",
                    "message" => "Room not found"
                ], 404);
            }

            foreach ($assets as $asset) {
                if($room->id != $asset->actual_room_id) {
                    AssetLocationHistory::create([
                        "asset_id" => $asset->id,
                        "room_id" => $room->id,
                        "previous_room_id" => $asset->actual_room_id ?? $asset->document_room_id,
                        "scan_time" => now("Asia/Jakarta")
                    ]);

                    $asset->update([
                        "actual_room_id" => $room->id
                    ]);
                }
            }

            DB::commit();
            return response()->json([
                "status" => "success",
                "message" => "Data successfuly updated"
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "status" => "error",
                "message" => $th->getMessage()
            ], 500);
        }
    }
}
