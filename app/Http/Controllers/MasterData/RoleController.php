<?php

namespace App\Http\Controllers\MasterData;

use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\Permission;
use App\Models\Role;

class RoleController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Role - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Data Role";
        $breadcrumbs = ["Master Data", "Role"];
        $permissions = Permission::all();

        return view("master-data.role.index", compact("title", "breadcrumbs", "permissions"));
    }

    public function list(Request $request)
    {
        if (!hasPermissionInGuard('Role - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = Role::where("guard_name", "employee")->orderBy("id", "DESC");

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return '<a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-edit" data-bs-toggle="modal" data-route="' . route("master-data.role.show", $row->id) . '"><i class="fas fa-edit"></i></a>';
                })
                ->rawColumns(["action"])
                ->make(true);
        }
    }

    function show(Role $role)
    {
        return response()->json([
            "data" => $role,
            "permissons" => $role->permissions
        ], 200);
    }

    function store(Request $request)
    {
        if (!hasPermissionInGuard('Role - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "kode_role" => "required|unique:roles,role_code",
            "nama_role" => "required|unique:roles,name",
            "deskripsi" => "nullable",
            "permissions" => "nullable|array",
        ]);

        try {
            DB::beginTransaction();

            $role = Role::create([
                "role_code" => $request->kode_role,
                "name" => $request->nama_role,
                "role_description" => $request->deskripsi,
                "guard_name" => "employee",
            ]);

            if ($request->permissions) {
                $role->syncPermissions(
                    Permission::where('guard_name', 'employee')->whereIn('name', $request->permissions)->get()
                );
            }

            DB::commit();

            return response()->json([
                "message" => "Data berhasil disimpan"
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function update(Request $request, Role $role)
    {
        if (!hasPermissionInGuard('Role - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "kode_role" => "required",
            "nama_role" => "required",
            "deskripsi" => "nullable",
        ]);

        try {
            DB::beginTransaction();

            $role->update([
                "role_code" => $request->kode_role,
                "name" => $request->nama_role,
                "description" => $request->deskripsi,
            ]);
            $role->syncPermissions(
                Permission::where('guard_name', 'employee')->whereIn('name', $request->permissions)->get()
            );

            DB::commit();

            return response()->json(["message" => "Data berhasil disimpan"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(["message" => $th->getMessage()], 500);
        }
    }

    public function dropdown()
    {
        $roles = Role::filterGuard()->get();
        return response()->json(['data' => $roles]);
    }

    function dropdown_permission()
    {
        $permissions = Permission::filterGuard()
            ->search()
            ->get();
        return response()->json(['data' => $permissions]);
    }
}