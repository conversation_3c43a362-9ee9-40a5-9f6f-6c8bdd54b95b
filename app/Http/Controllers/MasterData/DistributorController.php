<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Imports\MasterDistributorImport;
use App\Models\Distributor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Validators\ValidationException;
use Yajra\DataTables\Facades\DataTables;
use App\Exports\DistributorTemplateExport;

class DistributorController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Data Distributor - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Data Distributor";
        $breadcrumbs = ["Master Data", "Distributor"];

        return view("master-data.distributor.index", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Data Distributor - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = Distributor::orderBy("distributor_name", "ASC");

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return view('components.master.distributor.action-buttons', compact('row'))->render();
                })
                ->rawColumns(["action"])
                ->make("true");
        }
    }

    function store(Request $request)
    {
        if (!hasPermissionInGuard('Data Distributor - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "nama_distributor" => "required|string",
            "alamat" => "nullable|string",
            "telepon" => "nullable|string",
            "email" => "nullable|string",
        ]);

        try {
            DB::beginTransaction();

            Distributor::create([
                "distributor_name" => $request->nama_distributor,
                "distributor_address" => $request->alamat,
                "distributor_phone" => $request->telepon,
                "distributor_email" => $request->email,
                "created_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan"
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function import(Request $request)
    {
        if (!hasPermissionInGuard('Data Distributor - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            'excel_file' => 'required|mimes:xlsx,xls',
        ]);

        $file = $request->file('excel_file');

        try {
            $import = new MasterDistributorImport();
            Excel::import($import, $file);

            return response()->json([
                'success' => true,
                'message' => 'Data distributor berhasil diimport'
            ]);
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            $errors = [];

            foreach ($failures as $failure) {
                $errors[] = [
                    'row' => $failure->row(),
                    'attribute' => $failure->attribute(),
                    'errors' => $failure->errors(),
                    'values' => $failure->values()
                ];
            }

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan validasi saat import data distributor. Tidak ada data yang diimport.',
                'errors' => $errors,
                'error_count' => count($errors)
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    // Download template Excel untuk import
    function downloadTemplate()
    {
        if (!hasPermissionInGuard('Data Distributor - View')) {
            abort(403, "Unauthorized action.");
        }

        try {
            $fileName = 'template_import_distributor_' . date('Y-m-d_H-i-s') . '.xlsx';

            Log::info('Distributor template download', [
                'file_name' => $fileName,
                'user_id' => getAuthUserId()
            ]);

            return Excel::download(new DistributorTemplateExport(), $fileName);

        } catch (\Throwable $th) {
            Log::error('Distributor template download failed', [
                'error' => $th->getMessage(),
                'user_id' => getAuthUserId()
            ]);

            return response()->json([
                'message' => 'Gagal mendownload template: ' . $th->getMessage()
            ], 500);
        }
    }

    function show(Distributor $distributor)
    {
        return response()->json([
            "data" => $distributor
        ], 200);
    }

    function update(Request $request, Distributor $distributor)
    {
        if (!hasPermissionInGuard('Data Distributor - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "nama_distributor" => "required|string",
            "alamat" => "nullable|string",
            "telepon" => "nullable|string",
            "email" => "nullable|string",
        ]);

        try {
            DB::beginTransaction();

            $distributor->update([
                "distributor_name" => $request->nama_distributor,
                "distributor_address" => $request->alamat,
                "distributor_phone" => $request->telepon,
                "distributor_email" => $request->email,
                "updated_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan"
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function destroy(Distributor $distributor)
    {
        if (!hasPermissionInGuard('Data Distributor - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            $distributor->delete();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus"
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function dropdown()
    {
        $distributors = Distributor::search()
            ->paginate(25);

        return response()->json($distributors);
    }
}
