<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Models\Room;
use App\Models\Employee;
use App\Models\Distributor;
use App\Models\Uom;

class HomeController extends Controller
{
    function index()
    {
        $title = "Master Data";
        $breadcrumbs = ["Master Data"];

        // Get total counts
        $totalRooms = Room::count();
        $totalEmployees = Employee::count();
        $totalDistributors = Distributor::count();
        $totalUoms = Uom::count();

        // Get latest data
        $latestRooms = Room::with('pic')->latest()->take(5)->get();
        $latestEmployees = Employee::latest()->take(5)->get();

        return view("master-data.home.index", compact(
            "title", 
            "breadcrumbs",
            "totalRooms",
            "totalEmployees", 
            "totalDistributors",
            "totalUoms",
            "latestRooms",
            "latestEmployees"
        ));
    }
}
