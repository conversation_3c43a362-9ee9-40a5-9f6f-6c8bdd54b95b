<?php

namespace App\Http\Controllers\MasterData;

use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Models\Employee;
use App\Models\Room;
use App\Exports\EmployeeTemplateExport;
use App\Imports\MasterEmployeeImport;
use Maatwebsite\Excel\Facades\Excel;

class EmployeeController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Data Karyawan - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Data Pegawai";
        $breadcrumbs = ["Master Data", "Pegawai"];

        return view("master-data.employee.index", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Data Karyawan - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = DB::table('employees')
                ->leftJoin('roles', 'employees.role_id', '=', 'roles.id')
                ->select('employees.*', 'roles.name as role_name')
                ->when($request->filter_category, function ($query, $filter) {
                    $query->where('employees.category', $filter);
                })
                ->orderBy('employee_name', 'ASC');

            return DataTables::query($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return '
                        <a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-edit" data-bs-toggle="modal" data-route="' . route("master-data.employee.show", $row->id) . '"><i class="fas fa-edit"></i></a> <button type="button" class="btn btn-sm btn-outline-danger btn-delete" data-route="' . route("master-data.employee.destroy", $row->id) . '"><i class="fas fa-trash"></i></button>
                    ';
                })
                ->rawColumns(["action", "active"])
                ->make(true);
        }
    }


    function store(Request $request)
    {
        if (!hasPermissionInGuard('Data Karyawan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "kode_karyawan" => "required|string|unique:employees,employee_identification_number",
            "nama_karyawan" => "required|string",
            "golongan_karyawan" => "required|string",
            "jabatan_karyawan" => "required|string",
            "tipe_karyawan" => "required|string",
            "role_code" => "required|string|exists:roles,role_code",
        ]);

        try {
            DB::beginTransaction();

            Employee::create([
                "employee_identification_number" => $request->kode_karyawan,
                "employee_name" => $request->nama_karyawan,
                "employee_grade" => $request->golongan_karyawan,
                "employee_position" => $request->jabatan_karyawan,
                "employee_position" => $request->jabatan_karyawan,
                "pic_type" => $request->tipe_karyawan,
                "category" => $request->category,
                "role_id" => Role::where('role_code', $request->role_code)->first()?->id,
                "active" => 1, // Semua karyawan otomatis aktif
                "created_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json(["message" => "Data berhasil disimpan"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(["message" => $th->getMessage()], 500);
        }
    }

    function show(Employee $employee)
    {
        return response()->json(["data" => $employee], 200);
    }

    function update(Request $request, Employee $employee)
    {
        if (!hasPermissionInGuard('Data Karyawan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "kode_karyawan" => "required|string",
            "nama_karyawan" => "required|string",
            "golongan_karyawan" => "required|string",
            "jabatan_karyawan" => "required|string",
            "tipe_karyawan" => "required|string",
        ]);

        try {
            DB::beginTransaction();

            $employee->update([
                "employee_name" => $request->nama_karyawan,
                "employee_identification_number" => $request->kode_karyawan,
                "employee_grade" => $request->golongan_karyawan,
                "employee_position" => $request->jabatan_karyawan,
                "category" => $request->category,
                "role_id" => Role::where('role_code', $request->role_code)->first()?->id,
                "pic_type" => $request->tipe_karyawan,
                "active" => 1, // Semua karyawan otomatis aktif
                // "updated_by" => getAuthUserId()
            ]);

            $role = Role::where('role_code', $request->role_code)->first();
            if ($role) {
                $employee->syncRoles($role->name);
            }

            DB::commit();
            return response()->json(["message" => "Data berhasil disimpan"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(["message" => $th->getMessage()], 500);
        }
    }

    function destroy(Employee $employee)
    {
        if (!hasPermissionInGuard('Data Karyawan - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            $employee->delete();

            DB::commit();
            return response()->json(["message" => "Data berhasil dihapus"], 200);
        } catch (\Throwable $th) {
            return response()->json(["message" => $th->getMessage()], 500);
        }
    }

    // response only employee
    function dropdown()
    {
        $data = Employee::select("*");
        if (request()->has('q')) {
            $search = request()->get('q');
            $data = $data->where(function ($query) use ($search) {
                $query->where('employee_name', 'like', '%' . $search . '%')
                    ->orWhere('employee_identification_number', 'like', '%' . $search . '%');
            });
        }
        if (request()->has('category')) {
            $data = $data->where('category', request()->get('category'));
        }

        if (request()->has('template')) {
            if (request("template") == "penempatan") {
                $employee = Employee::search()->where("pic_type", "PIC_PENGURUS_BARANG")->paginate(25);
                return response()->json($employee);
            }

            if (request("template") == "rusak") {
                $employee = Employee::search()->where("pic_type", "PIC_BARANG_RUSAK")->paginate(25);
                return response()->json($employee);
            }
        }

        if (request()->has('pic_room')) {
            $employee = Employee::select('employees.*', 'rooms.room_name', 'rooms.room_code', 'rooms.id as room_id')
                ->join('rooms', 'employees.id', '=', 'rooms.pic_room')
                ->whereNotNull('rooms.pic_room');
            if (request()->has('q')) {
                $search = request()->get('q');
                $employee = $employee->where(function ($query) use ($search) {
                    $query->where('employees.employee_name', 'like', '%' . $search . '%')
                        ->orWhere('employees.employee_identification_number', 'like', '%' . $search . '%')
                        ->orWhere('rooms.room_name', 'like', '%' . $search . '%')
                        ->orWhere('rooms.room_code', 'like', '%' . $search . '%');
                });
            }
            $employee = $employee->paginate(25);
            return response()->json($employee);
        }

        $employee = $data->paginate(25);
        return response()->json($employee);
    }

    // response room with pic employee
    function dropdownHasRoom()
    {
        //template BAST penempatan
        if (request("template") == "penempatan") {
            $employee = Employee::search()->where("pic_type", "PIC_PENGURUS_BARANG")->paginate(25);
            return response()->json($employee);
        }
        if (request("template") == "mutasi" || request("template") == "rusak") {
            $employee = Employee::whereHas('rooms', function ($query) {
                $query->where('pic_room', '=', DB::raw('employees.id'));
            })->search()->paginate(25);
            return response()->json($employee);
        }

        if (request("type") == "bast") {
            $employeeId = auth()->guard('employee')->user()->id ?? null;
            $userId = Auth::id() ?? null;
            if ($userId) {
                $categories = Room::with("pic")->whereNotNull("pic_room")->search()->paginate(25);
                return response()->json($categories);
            } else {
                $employeeRole = auth()->guard('employee')->user()->role_id;
                $listRoomId = Room::where('pic_room', $employeeId)->pluck('id');
                if ($listRoomId->isNotEmpty() && $employeeRole === 1) {
                    $categories = Room::with("pic")->whereNotNull("pic_room")->search()->whereIn('id', $listRoomId)->paginate(25);
                    return response()->json($categories);
                } else {
                    $categories = Room::with("pic")->whereNotNull("pic_room")->search()->paginate(25);
                    return response()->json($categories);
                }
            }
        }

        $categories = Room::with("pic")->whereNotNull("pic_room")->search()->paginate(25);
        return response()->json($categories);
    }

    function findByType(Request $request)
    {
        $where = "";
        if ($request->type == "penempatan") {
            $where = "PIC_PENGURUS_BARANG";
        } else if ($request->type == "rusak") {
            $where = "PIC_PENGURUS_BARANG";
        }

        $employee = Employee::with("rooms")->where("pic_type", $where)->first();
        return response()->json($employee);
    }

    public function import(Request $request)
    {
        if (!hasPermissionInGuard('Data Karyawan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            'excel_file' => 'required|mimes:xlsx,xls',
        ]);

        $file = $request->file('excel_file');

        try {
            $import = new MasterEmployeeImport();
            Excel::import($import, $file);

            return response()->json([
                'success' => true,
                'message' => 'Data karyawan berhasil diimport'
            ]);
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            $errors = [];

            foreach ($failures as $failure) {
                $errors[] = [
                    'row' => $failure->row(),
                    'attribute' => $failure->attribute(),
                    'errors' => $failure->errors(),
                    'values' => $failure->values()
                ];
            }

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan validasi saat import data karyawan. Tidak ada data yang diimport.',
                'errors' => $errors,
                'error_count' => count($errors)
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    // Download template Excel untuk import
    function downloadTemplate()
    {
        if (!hasPermissionInGuard('Data Karyawan - View')) {
            abort(403, "Unauthorized action.");
        }

        try {
            $fileName = 'template_import_karyawan_' . date('Y-m-d_H-i-s') . '.xlsx';

            Log::info('Employee template download', [
                'file_name' => $fileName,
                'user_id' => getAuthUserId()
            ]);

            return Excel::download(new EmployeeTemplateExport(), $fileName);
        } catch (\Throwable $th) {
            Log::error('Employee template download failed', [
                'error' => $th->getMessage(),
                'user_id' => getAuthUserId()
            ]);

            return response()->json([
                'message' => 'Gagal mendownload template: ' . $th->getMessage()
            ], 500);
        }
    }
}
