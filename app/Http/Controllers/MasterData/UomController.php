<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Models\Uom;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class UomController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Data UOM - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Data UOM";
        $breadcrumbs = ["Master Data", "UOM"];

        return view("master-data.uom.index", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Data UOM - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $query = Uom::query()
                ->when($request->status, function ($query) use ($request) {
                    if ($request->status === 'active') {
                        return $query->where('active', 1);
                    } elseif ($request->status === 'inactive') {
                        return $query->where('active', 0);
                    }
                    return $query;
                })
                ->orderBy('uom_name', 'ASC');

            return DataTables::eloquent($query)
                ->addIndexColumn()
                ->addColumn('active', function ($row) {
                    return $row->active ? 
                        '<span class="badge bg-success">Aktif</span>' : 
                        '<span class="badge bg-danger">Tidak Aktif</span>';
                })
                ->addColumn('action', function ($row) {
                    return view('components.master.uom.action-buttons', compact('row'))->render();
                })
                ->rawColumns(['active', 'action'])
                ->make(true);
        }
    }

    function show(Uom $uom)
    {
        if (!hasPermissionInGuard('Data UOM - View')) {
            abort(403, "Unauthorized action.");
        }

        return response()->json(["data" => $uom]);
    }

    function store(Request $request)
    {
        if (!hasPermissionInGuard('Data UOM - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            'uom_name' => 'required'
        ]);

        try {
            Uom::create([
                'uom_name' => $request->uom_name,
                'active' => 1,
                'created_by' => getAuthUserId(),
                'created_by_name' => getAuthUserName()
            ]);

            return response()->json(['message' => 'Data berhasil disimpan']);
        } catch (\Throwable $th) {
            return response()->json(['message' => 'error, ' . $th->getMessage()], 400);
        }
    }

    function update(Request $request, Uom $uom)
    {
        if (!hasPermissionInGuard('Data UOM - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            $uom->update([
                'active' => $request->active,
                'updated_by' => getAuthUserId(),
                'updated_by_name' => getAuthUserName()
            ]);

            return response()->json(['message' => 'Data berhasil diupdate']);
        } catch (\Throwable $th) {
            return response()->json(['message' => 'error, ' . $th->getMessage()], 400);
        }
    }

    function destroy(Uom $uom)
    {
        if (!hasPermissionInGuard('Data UOM - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            $uom->delete();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function dropdown()
    {
        $uoms = Uom::search()
            ->where('active', 1)
            ->paginate(25);

        return response()->json($uoms);
    }
}
