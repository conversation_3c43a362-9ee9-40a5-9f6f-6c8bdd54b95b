<?php

namespace App\Http\Controllers\MasterData;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class MaintenanceCategoryController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Data Kategori Pemeliharaan - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Data Kategori Pemeliharaan";
        $breadcrumbs = ["Master Data", "Kategori Pemeliharaan"];

        return view("master-data.maintenance-category.index", compact("title", "breadcrumbs"));
    }


    public function list(Request $request)
    {
        if (!hasPermissionInGuard('Data Kategori Pemeliharaan - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = DB::table('maintenance_categories')
                ->select([
                    'id',
                    'maintenance_category_name',
                    'active',
                ])
                ->orderBy("maintenance_category_name", "ASC");

            return DataTables::of($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return view('components.master.maintenance-category.action-buttons', compact('row'))->render();
                })
                ->addColumn('active', function ($row) {
                    return $row->active ? 'Aktif' : 'Tidak Aktif';
                })
                ->rawColumns(["action", "active"])
                ->make(true);
        }
    }


    function store(Request $request)
    {
        if (!hasPermissionInGuard('Data Kategori Pemeliharaan - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();
            DB::table('maintenance_categories')
                ->insert([
                    "maintenance_category_name" => $request->maintenance_category_name,
                    "active" => $request->active ?? 0,
                    "created_by" => getAuthUserId()
                ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function show($id)
    {
        $maintenanceCategory = DB::table('maintenance_categories')->where('id', $id)->first();

        return response()->json([
            "data" => $maintenanceCategory
        ], 200);
    }

    public function update(Request $request, $id)
    {
        if (!hasPermissionInGuard('Data Kategori Pemeliharaan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $maintenanceCategory = DB::table('maintenance_categories')->where('id', $id)->first();

        if (!$maintenanceCategory) {
            return response()->json([
                "message" => "Maintenance category not found."
            ], 404);
        }

        try {
            DB::beginTransaction();

            DB::table('maintenance_categories')
                ->where('id', $id)
                ->update([
                    "maintenance_category_name" => $request->maintenance_category_name,
                    "updated_by" => getAuthUserId(),
                    "active" => $request->active,
                    "updated_at" => now()
                ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        if (!hasPermissionInGuard('Data Kategori Pemeliharaan - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            $exists = DB::table('maintenance_categories')->where('id', $id)->exists();
            if (!$exists) {
                return response()->json([
                    "message" => "Maintenance category not found."
                ], 404);
            }

            DB::table('maintenance_categories')->where('id', $id)->delete();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function dropdown()
    {
        $categories = DB::table('maintenance_categories')
            ->where('active', 1)
            ->select('id', 'maintenance_category_name as text')
            ->get();

        return response()->json($categories);
    }

}
