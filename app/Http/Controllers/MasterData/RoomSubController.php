<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Models\Room;
use App\Models\RoomSub;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class RoomSubController extends Controller
{
    public function index(Room $room)
    {
        if (!hasPermissionInGuard('Data Ruangan - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Ruang Spesifik";
        $breadcrumbs = ["Master Data", "Ruangan", "Ruang Spesifik"];

        // Menyimpan data ruangan induk untuk ditampilkan di halaman
        $parentRoom = $room;

        return view("master-data.room-sub.index", compact("title", "breadcrumbs", "parentRoom"));
    }

    public function list(Request $request, Room $room)
    {
        if (!hasPermissionInGuard('Data Ruangan - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = $room->roomSubs()->select('room_subs.*');

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) use ($room) {
                    return view('components.master.room-sub.action-buttons', compact('row', 'room'))->render();
                })
                ->rawColumns(["action"])
                ->make("true");
        }
    }

    public function store(Request $request, Room $room)
    {
        if (!hasPermissionInGuard('Data Ruangan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            'sub_room_code' => 'required|string|max:255|unique:room_subs,sub_room_code',
            'sub_room_name' => 'required|string|max:255',
            'active' => 'required|boolean',
        ]);

        try {
            DB::beginTransaction();

            RoomSub::create([
                'room_id' => $room->id,
                'sub_room_code' => $request->sub_room_code,
                'sub_room_name' => $request->sub_room_name,
                'active' => $request->active,
            ]);

            DB::commit();
            return response()->json([
                "message" => "Ruang Spesifik berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => "Terjadi kesalahan saat menyimpan data",
                "error" => $th->getMessage()
            ], 500);
        }
    }

    public function show(Room $room, RoomSub $roomSub)
    {
        if (!hasPermissionInGuard('Data Ruangan - View')) {
            abort(403, "Unauthorized action.");
        }

        return response()->json([
            'data' => $roomSub
        ]);
    }

    public function update(Request $request, Room $room, RoomSub $roomSub)
    {
        if (!hasPermissionInGuard('Data Ruangan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            'sub_room_code' => 'required|string|max:255|unique:room_subs,sub_room_code,' . $roomSub->id,
            'sub_room_name' => 'required|string|max:255',
            'active' => 'required|boolean',
        ]);

        try {
            DB::beginTransaction();

            $roomSub->update([
                'sub_room_code' => $request->sub_room_code,
                'sub_room_name' => $request->sub_room_name,
                'active' => $request->active,
            ]);

            DB::commit();
            return response()->json([
                "message" => "Ruang Spesifik berhasil diperbarui",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => "Terjadi kesalahan saat memperbarui data",
                "error" => $th->getMessage()
            ], 500);
        }
    }

    public function destroy(Room $room, RoomSub $roomSub)
    {
        if (!hasPermissionInGuard('Data Ruangan - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            $roomSub->delete();

            DB::commit();
            return response()->json([
                "message" => "Ruang Spesifik berhasil dihapus",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => "Terjadi kesalahan saat menghapus data",
                "error" => $th->getMessage()
            ], 500);
        }
    }

    /**
     * Dropdown untuk mengambil data sub-room berdasarkan room_id
     */
    public function dropdown($room_id)
    {
        try {
            $subRooms = RoomSub::where('room_id', $room_id)
                ->where('active', true)
                ->search()
                ->paginate(25);
            
            return response()->json($subRooms);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => "Terjadi kesalahan saat mengambil data sub-room",
                "error" => $th->getMessage()
            ], 500);
        }
    }
}
