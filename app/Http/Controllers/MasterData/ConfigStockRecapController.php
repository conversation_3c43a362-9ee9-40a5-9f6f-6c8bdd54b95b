<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Models\ConfigStockRecap;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class ConfigStockRecapController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Config Rekapitulasi - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Stock Recap Data";
        $breadcrumbs = ["Master Data", "Stock Recap"];

        return view("master-data.config-stock-recap.index", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Config Rekapitulasi - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
        $data = ConfigStockRecap::orderBy("name", "ASC");

        return DataTables::eloquent($data)
            ->addIndexColumn()
            ->addColumn("action", function ($row) {
                return '<a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-edit" data-bs-toggle="modal" data-route="' . route('master-data.config-stock-recap.show', $row->id) . '"><i class="fas fa-edit"></i></a> <button type="button" class="btn btn-sm btn-outline-danger btn-delete" data-route="' . route("master-data.config-stock-recap.destroy", $row->id) . '"><i class="fas fa-trash"></i></button>';
            })
            ->rawColumns(["action"])
            ->make("true");
        }
    }

    function store(Request $request)
    {
        if (!hasPermissionInGuard('Config Rekapitulasi - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "tipe" => "required|in:LOGISTIC,EXTERNAL",
            "code" => "required|string|max:128",
            "name" => "required|string|max:128",
        ]);

        try {
            DB::beginTransaction();

            ConfigStockRecap::create([
                "type" => $request->tipe,
                "code" => $request->code,
                "name" => $request->name,
                "active" => 1,
                "created_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data successfully saved.",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function show(ConfigStockRecap $configStockRecap)
    {
        return response()->json([
            "data" => $configStockRecap
        ], 200);
    }

    function update(Request $request, ConfigStockRecap $configStockRecap)
    {
        if (!hasPermissionInGuard('Config Rekapitulasi - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "tipe" => "required|in:LOGISTIC,EXTERNAL",
            "code" => "required|string|max:128",
            "name" => "required|string|max:128",
        ]);

        try {
            DB::beginTransaction();

            $configStockRecap->update([
                "type" => $request->tipe,
                "code" => $request->code,
                "name" => $request->name,
                "updated_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data successfully updated.",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function destroy(ConfigStockRecap $configStockRecap)
    {
        if (!hasPermissionInGuard('Config Rekapitulasi - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            $configStockRecap->delete();

            DB::commit();
            return response()->json([
                "message" => "Data successfully deleted.",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function dropdown()
    {
        $categories = ConfigStockRecap::select('id', 'code', 'name')
            ->orderBy("code", "ASC")
            ->get();

        return response()->json($categories);
    }
}
