<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Models\Room;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Program;

class RoomProgramController extends Controller
{
    public function index(Room $room)
    {
        if (!hasPermissionInGuard('Data Ruangan - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Program Ruangan: " . $room->room_name;
        $breadcrumbs = ["Master Data", "Ruangan", "Program"];

        return view("master-data.room-program.index", compact("title", "breadcrumbs", "room"));
    }

    public function list(Request $request, Room $room)
    {
        if (!hasPermissionInGuard('Data Ruangan - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = DB::table('room_has_programs')
                ->join('programs', 'room_has_programs.program_id', '=', 'programs.id')
                ->where('room_has_programs.room_id', $room->id)
                ->select('programs.*');

            return DataTables::query($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) use ($room) {
                    return view('components.master.room-program.action-buttons', compact('row', 'room'))->render();
                })
                ->rawColumns(["action"])
                ->make(true);
        }
    }

    public function store(Request $request, Room $room)
    {
        if (!hasPermissionInGuard('Data Ruangan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            'program_ids' => 'required|array',
            'program_ids.*' => [
                'exists:programs,id',
                function ($attribute, $value, $fail) {
                    $program = Program::find($value);
                    if ($program && $program->program_type !== 'OUTPUT') {
                        $fail('Hanya program dengan tipe OUTPUT yang diperbolehkan.');
                    }
                }
            ]
        ]);

        try {
            DB::beginTransaction();

            // Get existing program IDs for this room
            $existingProgramIds = DB::table('room_has_programs')
                ->where('room_id', $room->id)
                ->pluck('program_id')
                ->toArray();

            // Filter out program IDs that already exist
            $newProgramIds = array_diff($request->program_ids, $existingProgramIds);

            // Only insert new programs that don't exist yet
            if (!empty($newProgramIds)) {
                $data = array_map(function($programId) use ($room) {
                    return [
                        'room_id' => $room->id,
                        'program_id' => $programId
                    ];
                }, $newProgramIds);

                DB::table('room_has_programs')->insert($data);
            }

            DB::commit();
            return response()->json([
                "message" => "Program ruangan berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function destroy(Room $room, $programId)
    {
        if (!hasPermissionInGuard('Data Ruangan - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            DB::table('room_has_programs')
                ->where('room_id', $room->id)
                ->where('program_id', $programId)
                ->delete();

            DB::commit();
            return response()->json([
                "message" => "Program ruangan berhasil dihapus"
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }
}