<?php

namespace App\Http\Controllers\MasterData;

use App\Models\Category;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Imports\MasterCategoryImport;
use App\Exports\CategoryTemplateExport;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Validators\ValidationException;
use Yajra\DataTables\Facades\DataTables;

class CategoryController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Data Kategori Barang - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Data Kategori Barang";
        $breadcrumbs = ["Master Data", "Kategori"];

        return view("master-data.category.index", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Data Kategori Barang - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = Category::select("categories.*", "employees.employee_name")
                ->leftJoin("employees", "categories.pic_category", "=", "employees.id")
                ->when($request->filter_category_type, function ($query) use ($request) {
                    $query->where("categories.category_type", $request->filter_category_type);
                })
                ->when($request->filter_sub_category, function ($query) use ($request) {
                    $query->where("categories.category_sub_type", $request->filter_sub_category);
                })
                ->orderBy("category_name", "ASC");

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return view("components.master.category.action-buttons", compact("row"))->render();
                })
                ->rawColumns(["action"])
                ->make("true");
        }
    }

    function store(Request $request)
    {
        if (!hasPermissionInGuard('Data Kategori Barang - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "kode_kategori" => "required|string|unique:categories,category_code",
            "nama_kategori" => "required|string",
            "tipe_kategori" => "required|string",
            "sub_tipe_kategori" => "required|string",
            "kategori_pic" => "nullable|string",
        ]);

        try {
            DB::beginTransaction();

            Category::create([
                "category_code" => $request->kode_kategori,
                "category_name" => $request->nama_kategori,
                "category_type" => $request->tipe_kategori,
                "category_sub_type" => $request->sub_tipe_kategori,
                "pic_category" => $request->kategori_pic,
                "created_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function import(Request $request)
    {
        if (!hasPermissionInGuard('Data Kategori Barang - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            'excel_file' => 'required|mimes:xlsx,xls',
        ]);

        $file = $request->file('excel_file');

        try {
            $import = new MasterCategoryImport();
            Excel::import($import, $file);

            return response()->json([
                'success' => true,
                'message' => 'Data kategori berhasil diimport'
            ]);
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            $errors = [];

            foreach ($failures as $failure) {
                $errors[] = [
                    'row' => $failure->row(),
                    'attribute' => $failure->attribute(),
                    'errors' => $failure->errors(),
                    'values' => $failure->values()
                ];
            }

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan validasi saat import data kategori. Tidak ada data yang diimport.',
                'errors' => $errors,
                'error_count' => count($errors)
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show(Category $category)
    {
        $dataEmployee = optional(Employee::find($category->pic_category));
        $category->employee_name = $dataEmployee->employee_name ?? "";
        $category->employee_identification_number = $dataEmployee->employee_identification_number ?? "";

        return response()->json([
            "data" => $category
        ], 200);
    }

    function update(Request $request, Category $category)
    {
        if (!hasPermissionInGuard('Data Kategori Barang - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "kode_kategori" => "required|string|unique:categories,category_code," . $category->id,
            "nama_kategori" => "required|string",
            "tipe_kategori" => "required|string",
            "sub_tipe_kategori" => "required|string",
            "kategori_pic" => "nullable|string",
        ]);

        try {
            DB::beginTransaction();

            $category->update([
                "category_code" => $request->kode_kategori,
                "category_name" => $request->nama_kategori,
                "category_type" => $request->tipe_kategori,
                "category_sub_type" => $request->sub_tipe_kategori,
                "pic_category" => $request->kategori_pic,
                "updated_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function destroy(Category $category)
    {
        if (!hasPermissionInGuard('Data Kategori Barang - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            $category->delete();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus"
            ], 200);
        } catch (\Throwable $th) {
            if (strpos($th->getMessage(), 'a foreign key') !== false) {
                return response()->json([
                    "message" => "Data tidak dapat dihapus karena sudah terkait dengan data lain"
                ], 500);
            }

            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function dropdown()
    {
        $categories = Category::search()
            ->paginate(25);

        return response()->json($categories);
    }



    // Download template Excel untuk import
    function downloadTemplate()
    {
        if (!hasPermissionInGuard('Data Kategori Barang - View')) {
            abort(403, "Unauthorized action.");
        }

        try {
            $fileName = 'template_import_kategori_' . date('Y-m-d_H-i-s') . '.xlsx';

            Log::info('Category template download', [
                'file_name' => $fileName,
                'user_id' => getAuthUserId()
            ]);

            return Excel::download(new CategoryTemplateExport(), $fileName);

        } catch (\Throwable $th) {
            Log::error('Category template download failed', [
                'error' => $th->getMessage(),
                'user_id' => getAuthUserId()
            ]);

            return response()->json([
                'message' => 'Gagal mendownload template: ' . $th->getMessage()
            ], 500);
        }
    }
}
