<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Models\Item;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Imports\MasterItemImport;
use App\Exports\MasterItemTemplateExport;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Validators\ValidationException;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class ItemController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Data Barang - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Data Barang";
        $breadcrumbs = ["Master Data", "Barang"];

        return view("master-data.item.index", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Data Barang - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = Item::with('category')
                ->select('items.*')
                ->leftJoin('categories', 'categories.id', '=', 'items.category_id')
                ->orderBy("items.item_name", "ASC");

            return DataTables::of($data)
                ->addIndexColumn()
                ->filter(function ($query) use ($request) {
                    if ($request->search['value']) {
                        $searchValue = strtolower($request->search['value']);
                        $query->where(function($q) use ($searchValue) {
                            $q->where('items.item_code', 'LIKE', "%{$searchValue}%")
                              ->orWhere('categories.category_code', 'LIKE', "%{$searchValue}%")
                              ->orWhere('categories.category_name', 'LIKE', "%{$searchValue}%")
                              ->orWhere('items.item_name', 'LIKE', "%{$searchValue}%")
                              ->orWhere('items.depreciation_year', 'LIKE', "%{$searchValue}%");
                        });
                    }
                })
                ->addColumn("action", function ($row) {
                    return view('components.master.item.action-buttons', compact('row'))->render();
                })
                ->rawColumns(["action"])
                ->make(true);
        }
    }

    function store(Request $request)
    {
        if (!hasPermissionInGuard('Data Barang - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "category_id" => "required",
            "kode_barang" => "required",
            "nama_barang" => "required",
            "lama_penyusutan" => "required",
        ]);

        try {
            DB::beginTransaction();

            Item::create([
                "category_id" => $request->category_id,
                "item_code" => $request->kode_barang,
                "item_name" => $request->nama_barang,
                "depreciation_year" => $request->lama_penyusutan,
                "created_by" => getAuthUserId()
            ]);

            DB::commit();

            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage(),
            ], 500);
        }
    }

    function show(Item $item)
    {
        return response()->json([
            "data" => $item->load('category')
        ], 200);
    }

    function update(Request $request, Item $item)
    {
        if (!hasPermissionInGuard('Data Barang - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "category_id" => "required",
            "kode_barang" => "required",
            "nama_barang" => "required",
            "lama_penyusutan" => "required",
        ]);

        try {
            DB::beginTransaction();

            $item->update([
                "category_id" => $request->category_id,
                "item_code" => $request->kode_barang,
                "item_name" => $request->nama_barang,
                "depreciation_year" => $request->lama_penyusutan,
                "updated_by" => getAuthUserId()
            ]);

            DB::commit();

            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage(),
            ], 500);
        }
    }

    function destroy(Item $item)
    {
        if (!hasPermissionInGuard('Data Barang - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            $item->delete();

            DB::commit();

            return response()->json([
                "message" => "Data berhasil dihapus",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage(),
            ], 500);
        }
    }

    function dropdown()
    {
        $data = Item::select('items.*')
            ->leftJoin('categories', 'categories.id', '=', 'items.category_id');

        // Jika join_asset = true, join dengan tabel assets
        if (request()->has('join_asset') && request('join_asset')) {
            $data->join('assets', function($join) {
                $join->on('assets.item_id', '=', 'items.id')
                     ->where('assets.category_type', '=', 'EQUIPMENT');
            })
            ->distinct(); // Menghindari duplikasi item
        }

        if (request()->has('q')) {
            $search = request()->get('q');
            $data = $data->where(function ($query) use ($search) {
                $query->where('items.item_code', 'like', '%' . $search . '%')
                    ->orWhere('items.item_name', 'like', '%' . $search . '%');
            });
        }

        if (request()->has('category_sub_type')) {
            $data = $data->where('categories.category_sub_type', request()->get('category_sub_type'));
        }

        if (request()->has('category_type')) {
            $data = $data->where('categories.category_type', request()->get('category_type'));
        }

        $item = $data->paginate(25);
        return response()->json($item);
    }

    public function import(Request $request)
    {
        if (!hasPermissionInGuard('Data Barang - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            'excel_file' => 'required|mimes:xlsx,xls',
        ]);

        $file = $request->file('excel_file');

        try {
            $import = new MasterItemImport();
            Excel::import($import, $file);

            return response()->json([
                'success' => true,
                'message' => 'Data barang berhasil diimport'
            ]);
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            $errors = [];

            foreach ($failures as $failure) {
                $errors[] = [
                    'row' => $failure->row(),
                    'attribute' => $failure->attribute(),
                    'errors' => $failure->errors(),
                    'values' => $failure->values()
                ];
            }

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan validasi saat import data barang. Tidak ada data yang diimport.',
                'errors' => $errors,
                'error_count' => count($errors)
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    // Download template Excel untuk import
    function downloadTemplate()
    {
        if (!hasPermissionInGuard('Data Barang - View')) {
            abort(403, "Unauthorized action.");
        }

        try {
            $fileName = 'template_import_barang_' . date('Y-m-d_H-i-s') . '.xlsx';

            Log::info('Item template download', [
                'file_name' => $fileName,
                'user_id' => getAuthUserId()
            ]);

            return Excel::download(new MasterItemTemplateExport(), $fileName);

        } catch (\Throwable $th) {
            Log::error('Item template download failed', [
                'error' => $th->getMessage(),
                'user_id' => getAuthUserId()
            ]);

            return response()->json([
                'message' => 'Gagal mendownload template: ' . $th->getMessage()
            ], 500);
        }
    }
}
