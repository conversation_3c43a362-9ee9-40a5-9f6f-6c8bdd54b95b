<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Models\RoomCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class RoomCategoryController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Data Kategori Ruangan - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Data Kategori Ruangan";
        $breadcrumbs = ["Master Data", "Kategori Ruangan"];

        return view("master-data.room-category.index", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Data Kategori Ruangan - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = RoomCategory::select('room_categories.*')
                ->where('active', 1)
                ->orderBy("room_category_code", "ASC");

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return view('components.master.room-category.action-buttons', compact('row'))->render();
                })
                ->rawColumns(["action"])
                ->make("true");
        }
    }

    function store(Request $request)
    {
        if (!hasPermissionInGuard('Data Kategori Ruangan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "room_category_code" => "required|string",
            "room_category_name" => "required|string",
        ]);

        try {
            DB::beginTransaction();

            RoomCategory::create([
                "room_category_code" => $request->room_category_code,
                "room_category_name" => $request->room_category_name,
                "active" => 1,
                "created_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function show(RoomCategory $roomCategory)
    {
        return response()->json([
            "data" => $roomCategory
        ], 200);
    }

    function update(Request $request, RoomCategory $roomCategory)
    {
        if (!hasPermissionInGuard('Data Kategori Ruangan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "room_category_code" => "required|string",
            "room_category_name" => "required|string",
        ]);

        try {
            DB::beginTransaction();

            $roomCategory->update([
                "room_category_code" => $request->room_category_code,
                "room_category_name" => $request->room_category_name,
                "updated_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function destroy(RoomCategory $roomCategory)
    {

        if (!hasPermissionInGuard('Data Kategori Ruangan - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            $roomCategory->delete();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function dropdown()
    {
        $categories = RoomCategory::where('active', 1)
            ->select('id', 'room_category_code', 'room_category_name')
            ->orderBy("room_category_code", "ASC")
            ->get();

        return response()->json($categories);
    }
}
