<?php

namespace App\Http\Controllers\MasterData;

use App\Models\Officer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class OfficerController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Data Petugas - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Data Petugas";
        $breadcrumbs = ["Master Data", "Petugas"];

        return view("master-data.officer.index", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Data Petugas - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = Officer::orderBy("officer_name", "ASC");

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return '<a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-edit" data-bs-toggle="modal" data-route="' . route("master-data.officer.show", $row->id) . '"><i class="fas fa-edit"></i></a> <button type="button" class="btn btn-sm btn-outline-danger btn-delete" data-route="' . route("master-data.officer.destroy", $row->id) . '"><i class="fas fa-trash"></i></button>';
                })
                ->rawColumns(["action"])
                ->make("true");
        }
    }

    function store(Request $request)
    {
        if (!hasPermissionInGuard('Data Petugas - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "kode_petugas" => "required|string|unique:officers,officer_identification_number",
            "nama_petugas" => "required|string",
        ]);

        try {
            DB::beginTransaction();

            Officer::create([
                "officer_identification_number" => $request->kode_petugas,
                "officer_name" => $request->nama_petugas,
                "created_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            Log::info($th);
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function show(Officer $officer)
    {
        return response()->json([
            "data" => $officer
        ], 200);
    }

    function update(Request $request, Officer $officer)
    {
        if (!hasPermissionInGuard('Data Petugas - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "kode_petugas" => "required|string",
            "nama_petugas" => "required|string",
        ]);

        try {
            DB::beginTransaction();

            $officer->update([
                "officer_identification_number" => $request->kode_petugas,
                "officer_name" => $request->nama_petugas,
                "updated_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function destroy(Officer $officer)
    {
        if (!hasPermissionInGuard('Data Petugas - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            $officer->delete();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus"
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function dropdown()
    {
        $categories = Officer::search()
            ->paginate(25);

        return response()->json($categories);
    }

}
