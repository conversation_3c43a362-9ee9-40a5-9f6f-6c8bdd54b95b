<?php

namespace App\Http\Controllers\MasterData;

use App\Exports\SiapBmdProgramTemplateExport;
use App\Http\Controllers\Controller;
use App\Imports\SiapBmdProgramImport;
use App\Models\SiapBmdProgram;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Validators\ValidationException;

class SiapBmdProgramController extends Controller
{
    public function index()
    {
        $title = "Program SIAP BMD";
        $breadcrumbs = ["Master Data", "SIAP BMD", "Program"];

        return view('master-data.siap-bmd.program.index', compact('title', 'breadcrumbs'));
    }

    public function list(Request $request)
    {
        if (!$request->ajax()) {
            return response()->json([
                "data" => 'Not implemented'
            ], 200);
        }

        try {
            $queryBuilder = SiapBmdProgram::query()->select('siap_bmd_programs.*');
            $getPrograms = $queryBuilder->get();

            // Add rendered action buttons to each row
            $getPrograms = $getPrograms->map(function ($row) {
                $row->action_buttons = view('components.master.siap-bmd-program.action-buttons', compact('row'))->render();
                return $row;
            });
        } catch (\Throwable $th) {
            return response()->json([
                "data" => [],
                "message" => $th->getMessage()
            ], 500);
        }

        return response()->json([
            "data" => $getPrograms
        ]);
    }

    public function parentGroups(Request $request)
    {
        try {
            $query = SiapBmdProgram::branches()->select('id', 'program_name', 'program_code');

            if ($request->has('q')) {
                $search = $request->q;
                $query->where(function ($q) use ($search) {
                    $q->where('program_name', 'like', '%' . $search . '%')
                        ->orWhere('program_code', 'like', '%' . $search . '%');
                });
            }

            $groups = $query->paginate(10);

            // Format data untuk Select2
            $formattedGroups = $groups->map(function ($item) {
                return [
                    'id' => $item->id,
                    'text' => $item->program_code . ' - ' . $item->program_name
                ];
            });

            return response()->json([
                'data' => $formattedGroups,
                'total' => $groups->total()
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                "data" => [],
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function show(SiapBmdProgram $siapBmdProgram)
    {
        try {
            // Load parent data if exists
            $siapBmdProgram->load('parent');

            // Format data for response
            $data = [
                'id' => $siapBmdProgram->id,
                'program_name' => $siapBmdProgram->program_name,
                'program_code' => $siapBmdProgram->program_code,
                'type' => $siapBmdProgram->type,
                'tree' => $siapBmdProgram->tree,
                'parent_id' => $siapBmdProgram->parent_id,
                'parent_name' => $siapBmdProgram->parent ?
                    $siapBmdProgram->parent->program_code . ' - ' . $siapBmdProgram->parent->program_name : null
            ];

            return response()->json([
                "data" => $data
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => "Terjadi kesalahan saat mengambil data",
                "error" => $th->getMessage()
            ], 500);
        }
    }

    public function edit(SiapBmdProgram $siapBmdProgram)
    {
        try {
            // Load parent data if exists
            $siapBmdProgram->load('parent');

            // Format data for response
            $data = [
                'id' => $siapBmdProgram->id,
                'program_name' => $siapBmdProgram->program_name,
                'program_code' => $siapBmdProgram->program_code,
                'type' => $siapBmdProgram->type,
                'tree' => $siapBmdProgram->tree,
                'parent_id' => $siapBmdProgram->parent_id,
                'parent_name' => $siapBmdProgram->parent ?
                    $siapBmdProgram->parent->program_code . ' - ' . $siapBmdProgram->parent->program_name : null
            ];

            return response()->json([
                "data" => $data
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => "Terjadi kesalahan saat mengambil data",
                "error" => $th->getMessage()
            ], 500);
        }
    }

    public function store(Request $request)
    {
        $request->validate([
            'program_name' => 'required|string|max:255',
            'program_code' => 'required|string|max:50|unique:siap_bmd_programs,program_code',
            'type' => 'required|in:PROGRAM,ACTIVITY,SUB_ACTIVITY',
            'tree' => 'required|in:BRANCH,LEAF',
            'parent_id' => 'nullable|exists:siap_bmd_programs,id',
        ], [
            'program_name.required' => 'Nama wajib diisi',
            'program_code.required' => 'Kode wajib diisi',
            'program_code.unique' => 'Kode sudah digunakan, silakan gunakan kode lain',
            'type.required' => 'Type wajib diisi',
            'type.in' => 'Type harus PROGRAM, ACTIVITY, atau SUB_ACTIVITY',
            'tree.required' => 'Tree wajib diisi',
            'tree.in' => 'Tree harus BRANCH atau LEAF',
            'parent_id.exists' => 'Parent tidak valid',
        ]);

        // Additional validation for LEAF type
        if ($request->tree === 'LEAF' && !$request->parent_id) {
            return response()->json([
                'message' => 'Parent wajib diisi untuk tipe LEAF',
                'errors' => [
                    'parent_id' => ['Parent wajib diisi untuk tipe LEAF']
                ]
            ], 422);
        }


        try {
            $data = $request->only([
                'program_name',
                'program_code',
                'type',
                'tree',
                'parent_id'
            ]);

            // Add user information
            $data['created_by'] = \Illuminate\Support\Facades\Auth::id();
            $data['created_by_name'] = \Illuminate\Support\Facades\Auth::user()->name;

            $program = new SiapBmdProgram();
            $program->fill($data);
            $program->save();

            return response()->json([
                "data" => $program,
                "message" => "Data berhasil disimpan"
            ], 201);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => "Terjadi kesalahan saat menyimpan data",
                "error" => $th->getMessage()
            ], 500);
        }
    }

    public function update(Request $request, SiapBmdProgram $siapBmdProgram)
    {
        $request->validate([
            'program_name' => 'required|string|max:255',
            'program_code' => 'required|string|max:50|unique:siap_bmd_programs,program_code,' . $siapBmdProgram->id,
            'type' => 'required|in:PROGRAM,ACTIVITY,SUB_ACTIVITY',
            'tree' => 'required|in:BRANCH,LEAF',
            'parent_id' => 'nullable|exists:siap_bmd_programs,id',
        ], [
            'program_name.required' => 'Nama wajib diisi',
            'program_code.required' => 'Kode wajib diisi',
            'program_code.unique' => 'Kode sudah digunakan, silakan gunakan kode lain',
            'type.required' => 'Type wajib diisi',
            'type.in' => 'Type harus PROGRAM, ACTIVITY, atau SUB_ACTIVITY',
            'tree.required' => 'Tree wajib diisi',
            'tree.in' => 'Tree harus BRANCH atau LEAF',
            'parent_id.exists' => 'Parent tidak valid',
        ]);

        // Additional validation for LEAF type
        if ($request->tree === 'LEAF' && !$request->parent_id) {
            return response()->json([
                'message' => 'Parent wajib diisi untuk tipe LEAF',
                'errors' => [
                    'parent_id' => ['Parent wajib diisi untuk tipe LEAF']
                ]
            ], 422);
        }


        // Check if changing from BRANCH to LEAF and has children
        if ($siapBmdProgram->tree === 'BRANCH' &&
            $request->tree === 'LEAF' &&
            $siapBmdProgram->children()->count() > 0) {
            return response()->json([
                'message' => 'Tidak dapat mengubah BRANCH yang memiliki sub-program menjadi LEAF',
                'errors' => [
                    'tree' => ['Tidak dapat mengubah BRANCH yang memiliki sub-program menjadi LEAF']
                ]
            ], 422);
        }

        // Prevent setting parent to self or child
        if ($request->parent_id) {
            if ($request->parent_id == $siapBmdProgram->id) {
                return response()->json([
                    'message' => 'Tidak dapat menjadikan diri sendiri sebagai parent',
                    'errors' => [
                        'parent_id' => ['Tidak dapat menjadikan diri sendiri sebagai parent']
                    ]
                ], 422);
            }

            // Check if the selected parent is a descendant of current program
            $isDescendant = $this->isDescendant($siapBmdProgram->id, $request->parent_id);
            if ($isDescendant) {
                return response()->json([
                    'message' => 'Tidak dapat menjadikan sub-program sebagai parent',
                    'errors' => [
                        'parent_id' => ['Tidak dapat menjadikan sub-program sebagai parent']
                    ]
                ], 422);
            }
        }

        try {
            $data = $request->only([
                'program_name',
                'program_code',
                'type',
                'tree',
                'parent_id'
            ]);

            // Add user information
            $data['updated_by'] = \Illuminate\Support\Facades\Auth::id();
            $data['updated_by_name'] = \Illuminate\Support\Facades\Auth::user()->name;

            $siapBmdProgram->update($data);

            return response()->json([
                "data" => $siapBmdProgram,
                "message" => "Data berhasil diperbarui"
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => "Terjadi kesalahan saat memperbarui data",
                "error" => $th->getMessage()
            ], 500);
        }
    }

    /**
     * Check if a program is a descendant of another program
     *
     * @param int $parentId
     * @param int $childId
     * @return bool
     */
    private function isDescendant($parentId, $childId)
    {
        $child = SiapBmdProgram::with('parent')->find($childId);

        while ($child && $child->parent_id) {
            if ($child->parent_id == $parentId) {
                return true;
            }
            $child = $child->parent;
        }

        return false;
    }

    public function destroy(SiapBmdProgram $siapBmdProgram)
    {
        try {
            // Check if this is a BRANCH and has children
            if ($siapBmdProgram->tree === 'BRANCH' && $siapBmdProgram->children()->count() > 0) {
                return response()->json([
                    'message' => 'Tidak dapat menghapus BRANCH yang memiliki sub-program. Hapus terlebih dahulu semua sub-program di dalamnya.',
                    'error' => 'BRANCH_HAS_CHILDREN'
                ], 422);
            }

            // Store information for success message
            $programName = $siapBmdProgram->program_name;
            $programCode = $siapBmdProgram->program_code;

            // Soft delete the record (uses SoftDeletes)
            $siapBmdProgram->delete();

            return response()->json([
                'message' => "Data '{$programCode} - {$programName}' berhasil dihapus",
                'data' => [
                    'deleted_id' => $siapBmdProgram->id,
                    'deleted_name' => $programName,
                    'deleted_code' => $programCode
                ]
            ], 200);

        } catch (\Throwable $th) {
            return response()->json([
                'message' => 'Terjadi kesalahan saat menghapus data',
                'error' => $th->getMessage()
            ], 500);
        }
    }

    /**
     * Import SIAP BMD programs from Excel file
     */
    public function import(Request $request)
    {
        try {
            // Validate the uploaded file with stricter validation
            $validator = Validator::make($request->all(), [
                'excel_file' => [
                    'required',
                    'file',
                    'mimes:xlsx,xls',
                    'mimetypes:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel',
                    'max:5120' // 5MB max
                ]
            ], [
                'excel_file.required' => 'File Excel wajib dipilih.',
                'excel_file.file' => 'File yang dipilih tidak valid.',
                'excel_file.mimes' => 'File harus berformat Excel (.xlsx atau .xls).',
                'excel_file.mimetypes' => 'File harus berformat Excel yang valid.',
                'excel_file.max' => 'Ukuran file maksimal 5MB.'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validasi gagal. Periksa file yang diupload.',
                    'errors' => $validator->errors()
                ], 422);
            }

            $file = $request->file('excel_file');

            // Additional file validation
            if (!$this->isValidExcelFile($file)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File yang diupload bukan file Excel yang valid. Pastikan file dalam format .xlsx atau .xls yang benar.'
                ], 422);
            }

            DB::beginTransaction();

            try {
                $import = new SiapBmdProgramImport();
                Excel::import($import, $file);

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Data SIAP BMD Program berhasil diimport.',
                    'imported_count' => $import->getSuccessCount(),
                    'errors' => $import->getErrors()
                ]);
            } catch (ValidationException $e) {
                DB::rollBack();

                $failures = $e->failures();
                $errors = [];
                $errorCount = 0;

                foreach ($failures as $failure) {
                    $errorCount++;
                    $errors[] = [
                        'row' => $failure->row(),
                        'attribute' => $failure->attribute(),
                        'errors' => $failure->errors(),
                        'values' => $failure->values()
                    ];
                }

                return response()->json([
                    'success' => false,
                    'message' => 'Terdapat kesalahan validasi pada file Excel.',
                    'errors' => $errors,
                    'error_count' => $errorCount
                ], 422);

            } catch (\Maatwebsite\Excel\Exceptions\NoTypeDetectedException $e) {
                DB::rollBack();
                Log::error('Excel type detection failed: ' . $e->getMessage());

                return response()->json([
                    'success' => false,
                    'message' => 'File yang diupload tidak dapat dikenali sebagai file Excel. Pastikan file memiliki format .xlsx atau .xls yang benar dan tidak rusak.'
                ], 422);

            } catch (\PhpOffice\PhpSpreadsheet\Exception $e) {
                DB::rollBack();
                Log::error('PhpSpreadsheet error: ' . $e->getMessage());

                return response()->json([
                    'success' => false,
                    'message' => 'File Excel tidak dapat dibaca. Pastikan file tidak rusak dan memiliki format yang benar.'
                ], 422);

            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Error importing SIAP BMD programs: ' . $e->getMessage());

                // Check if it's an OLE file error
                if (strpos($e->getMessage(), 'OLE file') !== false || strpos($e->getMessage(), 'not recognised') !== false) {
                    return response()->json([
                        'success' => false,
                        'message' => 'File yang diupload tidak dapat dikenali sebagai file Excel yang valid. Silakan gunakan file Excel (.xlsx atau .xls) yang dibuat dengan Microsoft Excel atau aplikasi spreadsheet lainnya.'
                    ], 422);
                }

                Log::error('Error importing SIAP BMD programs: ' . $e->getMessage(), [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan saat mengimport data. Silakan periksa format file Excel dan coba lagi.'
                ], 500);
            }
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan tak terduga: ' . $th->getMessage()
            ], 500);
        }
    }

    /**
     * Validate if the uploaded file is a valid Excel file
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @return bool
     */
    private function isValidExcelFile($file)
    {
        try {
            // Check file extension
            $extension = strtolower($file->getClientOriginalExtension());
            if (!in_array($extension, ['xlsx', 'xls'])) {
                return false;
            }

            // Check MIME type
            $mimeType = $file->getMimeType();
            $validMimeTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
                'application/vnd.ms-excel', // .xls
                'application/excel',
                'application/x-excel',
                'application/x-msexcel'
            ];

            if (!in_array($mimeType, $validMimeTypes)) {
                return false;
            }

            // Try to read the file header to validate it's a real Excel file
            $filePath = $file->getRealPath();
            $handle = fopen($filePath, 'rb');

            if (!$handle) {
                return false;
            }

            $header = fread($handle, 8);
            fclose($handle);

            // Check for Excel file signatures
            if ($extension === 'xlsx') {
                // XLSX files start with PK (ZIP signature)
                return substr($header, 0, 2) === 'PK';
            } elseif ($extension === 'xls') {
                // XLS files have OLE signature
                return substr($header, 0, 8) === "\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1" ||
                    substr($header, 0, 4) === "\x09\x08\x06\x00" ||
                    substr($header, 0, 4) === "\x09\x08\x08\x00";
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Error validating Excel file: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Download Excel template for SIAP BMD program import
     */
    public function downloadTemplate()
    {
        try {
            return Excel::download(
                new SiapBmdProgramTemplateExport,
                'template_siap_bmd_programs.xlsx',
                \Maatwebsite\Excel\Excel::XLSX
            );
        } catch (\Exception $e) {
            Log::error('Error downloading SIAP BMD program template: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mendownload template: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Dropdown list for ACTIVITY (Kegiatan)
     * Returns paginated results suitable for Select2
     */
    public function dropdownActivities(Request $request)
    {
        try {
            $perPage = (int) ($request->get('per_page') ?: 10);
            $page = (int) ($request->get('page') ?: 1);

            $query = SiapBmdProgram::query()
                ->activities()
                ->select(['id', 'program_code', 'program_name'])
                ->orderBy('program_code');

            if ($request->filled('q')) {
                $search = $request->get('q');
                $query->where(function ($q) use ($search) {
                    $q->where('program_name', 'like', "%{$search}%")
                        ->orWhere('program_code', 'like', "%{$search}%");
                });
            }

            $paginator = $query->paginate($perPage, ['*'], 'page', $page);

            $items = $paginator->getCollection()->map(function ($item) {
                return [
                    'id' => $item->id,
                    'text' => $item->program_code . ' - ' . $item->program_name,
                    'program_code' => $item->program_code,
                    'program_name' => $item->program_name,
                ];
            })->values();

            return response()->json([
                'data' => $items,
                'current_page' => $paginator->currentPage(),
                'last_page' => $paginator->lastPage(),
                'total' => $paginator->total(),
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'data' => [],
                'message' => $th->getMessage(),
            ], 500);
        }
    }

    /**
     * Dropdown list for SUB_ACTIVITY (Sub Kegiatan) filtered by activity_id
     * Returns paginated results suitable for Select2
     */
    public function dropdownSubActivities(Request $request)
    {
        try {
            $activityId = $request->get('activity_id');
            if (!$activityId) {
                return response()->json([
                    'data' => [],
                    'current_page' => 1,
                    'last_page' => 1,
                    'total' => 0,
                ]);
            }

            $perPage = (int) ($request->get('per_page') ?: 10);
            $page = (int) ($request->get('page') ?: 1);

            $query = SiapBmdProgram::query()
                ->subActivities()
                ->where('parent_id', $activityId)
                ->select(['id', 'program_code', 'program_name'])
                ->orderBy('program_code');

            if ($request->filled('q')) {
                $search = $request->get('q');
                $query->where(function ($q) use ($search) {
                    $q->where('program_name', 'like', "%{$search}%")
                        ->orWhere('program_code', 'like', "%{$search}%");
                });
            }

            $paginator = $query->paginate($perPage, ['*'], 'page', $page);

            $items = $paginator->getCollection()->map(function ($item) {
                return [
                    'id' => $item->id,
                    'text' => $item->program_code . ' - ' . $item->program_name,
                    'program_code' => $item->program_code,
                    'program_name' => $item->program_name,
                ];
            })->values();

            return response()->json([
                'data' => $items,
                'current_page' => $paginator->currentPage(),
                'last_page' => $paginator->lastPage(),
                'total' => $paginator->total(),
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'data' => [],
                'message' => $th->getMessage(),
            ], 500);
        }
    }
}
