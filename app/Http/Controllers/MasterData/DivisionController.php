<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use App\Models\Division;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class DivisionController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Data Bidang - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Data Bidang";
        $breadcrumbs = ["Master Data", "Bidang"];

        return view("master-data.division.index", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Data Bidang - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = Division::query()
                ->leftJoin('rooms', 'divisions.room_id', '=', 'rooms.id')
                ->select('divisions.*', 'rooms.room_name')
                ->orderBy("division_name", "ASC");

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("status", function($row) {
                    return $row->active ? 'Aktif' : 'Tidak Aktif';
                })
                ->addColumn("action", function ($row) {
                    return view('components.master.division.action-buttons', compact('row'))->render();
                })
                ->rawColumns(["action"])
                ->make(true);
        }
    }

    function store(Request $request)
    {
        if (!hasPermissionInGuard('Data Bidang - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "nama_bidang" => "required|string|max:128"
        ]);

        try {
            DB::beginTransaction();

            Division::create([
                "division_name" => $request->nama_bidang,
                "division_description" => $request->deskripsi_bidang ?? '',
                "room_id" => $request->room_id,
                "active" => $request->status ?? true,
                "created_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function show(Division $division)
    {
        // Join dengan rooms untuk mendapatkan data room
        $division = Division::leftJoin('rooms', 'divisions.room_id', '=', 'rooms.id')
            ->select('divisions.*', 'rooms.room_name', 'rooms.room_code')
            ->where('divisions.id', $division->id)
            ->first();

        return response()->json([
            "data" => $division
        ], 200);
    }

    function update(Request $request, Division $division)
    {
        if (!hasPermissionInGuard('Data Bidang - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "nama_bidang" => "required|string|max:128"
        ]);

        try {
            DB::beginTransaction();

            $division->update([
                "division_name" => $request->nama_bidang,
                "division_description" => $request->deskripsi_bidang,
                "room_id" => $request->room_id,
                "active" => $request->status,
                "updated_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function destroy(Division $division)
    {
        if (!hasPermissionInGuard('Data Bidang - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            $division->delete();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus"
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            if (strpos($th->getMessage(), 'a foreign key') !== false) {
                return response()->json([
                    "message" => "Data tidak dapat dihapus karena sudah terkait dengan data lain"
                ], 500);
            }

            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function dropdown()
    {
        if (!hasPermissionInGuard('Data Bidang - View')) {
            abort(403, "Unauthorized action.");
        }

        $divisions = Division::where('active', true)
            ->select('id', 'division_name')
            ->orderBy('division_name', 'ASC')
            ->get();

        return response()->json([
            'data' => $divisions
        ], 200);
    }
}