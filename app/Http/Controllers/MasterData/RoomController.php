<?php

namespace App\Http\Controllers\MasterData;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\Employee;
use App\Models\Room;
use App\Models\Division;
use App\Exports\RoomTemplateExport;
use App\Imports\RoomImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Validators\ValidationException;
use Yajra\DataTables\Facades\DataTables;

class RoomController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Data Ruangan - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Data Ruangan";
        $breadcrumbs = ["Master Data", "Ruangan"];

        return view("master-data.room.index", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Data Ruangan - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = Room::leftJoin('employees', 'rooms.pic_room', '=', 'employees.id')
                ->leftJoin('room_categories', 'rooms.room_category', '=', 'room_categories.id')
                ->leftJoin('divisions', 'rooms.division_id', '=', 'divisions.id')
                ->select(
                    'rooms.*',
                    'employees.employee_name AS room_pic_name',
                    'divisions.division_name',
                    DB::raw('CONCAT(room_categories.room_category_code, " ", room_categories.room_category_name) AS room_category_name')
                )
                ->when($request->room_category_id, function ($query) use ($request) {
                    return $query->where('rooms.room_category', $request->room_category_id);
                })
                ->orderBy("room_name", "ASC");

            return DataTables::eloquent($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return view('components.master.room.action-buttons', compact('row'))->render();
                })
                ->rawColumns(["action"])
                ->make("true");
        }
    }

    function store(Request $request)
    {
        if (!hasPermissionInGuard('Data Ruangan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "kode_ruangan" => "required|string",
            "nama_ruangan" => "required|string",
            "nama_gedung" => "required|string",
            "penanggung_jawab" => "required|string",
            "kategori_ruangan" => "required",
            "device_id" => "nullable|string",
            "division_id" => "required|exists:divisions,id",
        ]);

        try {
            DB::beginTransaction();

            Room::create([
                "room_code" => $request->kode_ruangan,
                "room_name" => $request->nama_ruangan,
                "building_name" => $request->nama_gedung,
                "device_id" => $request->device_id,
                "pic_room" => $request->penanggung_jawab,
                "room_category" => $request->kategori_ruangan,
                "division_id" => $request->division_id,
                "created_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function show(Room $room)
    {

        $dataEmployee = optional(Employee::find($room->pic_room));
        $room->employee_identification_number = $dataEmployee->employee_identification_number ?? "";
        $room->employee_name = $dataEmployee->employee_name ?? "";
        return response()->json([
            "data" => $room
        ], 200);
    }

    function update(Request $request, Room $room)
    {
        if (!hasPermissionInGuard('Data Ruangan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "kode_ruangan" => "required|string",
            "nama_ruangan" => "required|string",
            "nama_gedung" => "required|string",
            "penanggung_jawab" => "nullable|string",
            "kategori_ruangan" => "required",
            "division_id" => "required|exists:divisions,id",
        ]);

        try {
            DB::beginTransaction();

            $room->update([
                "room_code" => $request->kode_ruangan,
                "room_name" => $request->nama_ruangan,
                "building_name" => $request->nama_gedung,
                "device_id" => $request->device_id,
                "pic_room" => $request->penanggung_jawab,
                "room_category" => $request->kategori_ruangan,
                "division_id" => $request->division_id,
                "updated_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function destroy(Room $room)
    {
        if (!hasPermissionInGuard('Data Ruangan - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            $room->delete();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function dropdown()
    {
        if (request()->has('template')) {
            if (request()->template == "rusak") {
                $rooms = Room::search()->where("room_type", "ASET_RUSAK")->paginate(25);
                return response()->json($rooms);
            }
        }

        $rooms = Room::search()->paginate(25);
        return response()->json($rooms);
    }

    function dropdown_with_access()
    {
        $employeeId = auth()->guard('employee')->user()->id ?? null;
        $userId = Auth::id() ?? null;

        if ($userId) {
            $rooms = Room::search()->paginate(25);
            return response()->json($rooms);
        } else {
            $employeeRole = auth()->guard('employee')->user()->role_id;
            $listRoomId = Room::where('pic_room', $employeeId)->pluck('id');
            if ($listRoomId->isNotEmpty() && $employeeRole === 1) {
                $rooms = Room::search()->whereIn('id', $listRoomId)->paginate(25);
                return response()->json($rooms);
            } else {
                $rooms = Room::search()->paginate(25);
                return response()->json($rooms);
            }
        }
    }

    function dropdown_with_access_with_trigger()
    {
        $employeeId = auth()->guard('employee')->user()->id ?? null;
        $userId = Auth::id() ?? null;

        if ($userId) {
            $rooms = Room::search()->paginate(25);
            return response()->json([
                'data' => $rooms,
                'auto_select' => false
            ]);
        } else {
            $employeeRole = auth()->guard('employee')->user()->role_id;
            $listRoomId = Room::where('pic_room', $employeeId)->pluck('id');

            if ($listRoomId->isNotEmpty() && $employeeRole === 1) {
                $rooms = Room::search()->whereIn('id', $listRoomId)->paginate(25);
                return response()->json([
                    'data' => $rooms,
                    'auto_select' => true,
                    'default_room' => $rooms->first()
                ]);
            } else {
                $rooms = Room::search()->paginate(25);
                return response()->json([
                    'data' => $rooms,
                    'auto_select' => false
                ]);
            }
        }
    }

    function dropdown_with_access_planapprove()
    {
        $employeeId = auth()->guard('employee')->user()->id ?? null;
        $userId = Auth::id() ?? null;

        if ($userId) {
            $rooms = Room::search()->paginate(25);
            return response()->json([
                'data' => $rooms,
                'auto_select' => false
            ]);
        } else {
            $listDivisionIds = Division::select('divisions.id')
                ->join('rooms', 'rooms.division_id', '=', 'divisions.id')
                ->where('rooms.pic_room', $employeeId)
                ->pluck('divisions.id');

            $listRoomId = Room::whereIn('division_id', $listDivisionIds)->pluck('id');

            $rooms = Room::search()->whereIn('id', $listRoomId)->paginate(25);
            return response()->json([
                'data' => $rooms,
                'auto_select' => true,
                'default_room' => $rooms->first()
            ]);
        }
    }

    function downloadTemplate()
    {
        if (!hasPermissionInGuard('Data Ruangan - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            Log::info('Room template download initiated by user: ' . getAuthUserId());

            return Excel::download(new RoomTemplateExport, 'template-import-ruangan.xlsx', \Maatwebsite\Excel\Excel::XLSX);
        } catch (\Throwable $th) {
            Log::error('Room template download failed: ' . $th->getMessage());
            return response()->json([
                "message" => "Gagal mengunduh template: " . $th->getMessage()
            ], 500);
        }
    }

    function import(Request $request)
    {
        if (!hasPermissionInGuard('Data Ruangan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            'excel_file' => 'required|mimes:xlsx,xls'
        ]);

        try {
            Log::info('Room import initiated by user: ' . getAuthUserId());

            Excel::import(new RoomImport, $request->file('excel_file'), null, \Maatwebsite\Excel\Excel::XLSX);

            Log::info('Room import completed successfully by user: ' . getAuthUserId());

            return response()->json([
                'message' => 'Data ruangan berhasil diimport'
            ], 200);
        } catch (ValidationException $e) {
            Log::error('Room import validation failed: ' . json_encode($e->failures()));

            $failures = [];
            foreach ($e->failures() as $failure) {
                $failures[] = [
                    'row' => $failure->row(),
                    'attribute' => $failure->attribute(),
                    'errors' => $failure->errors(),
                    'values' => $failure->values()
                ];
            }

            return response()->json([
                'message' => 'Terdapat kesalahan pada data yang diimport',
                'errors' => $failures
            ], 422);
        } catch (\Throwable $th) {
            Log::error('Room import failed: ' . $th->getMessage());

            return response()->json([
                'message' => 'Gagal mengimport data: ' . $th->getMessage()
            ], 500);
        }
    }
}
