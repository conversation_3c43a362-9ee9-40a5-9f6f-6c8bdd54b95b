<?php

namespace App\Http\Controllers\MasterData;

use App\Models\Program;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class ProgramController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Data Program - View')) {
            abort(403, "Unauthorized action.");
        }

        $title = "Data Program";
        $breadcrumbs = ["Master Data", "Program"];

        return view("master-data.program.index", compact("title", "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Data Program - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = Program::orderBy("id", "ASC")
                ->select(['id', 'program_category', 'program_code', 'program_name', 'program_type', 'asset_type', 'active', 'top_parent_id']);

            if ($request->has('filter_category') && $request->filter_category != '') {
                $data->where('program_category', $request->filter_category);
            }

            return DataTables::of($data)
                ->addIndexColumn()
                ->addColumn("action", function ($row) {
                    return '
                        <a href="#modal-dialog" class="btn btn-sm btn-outline-warning btn-edit" data-bs-toggle="modal" data-route="' . route("master-data.program.show", $row->id) . '"><i class="fas fa-edit"></i></a> <button type="button" class="btn btn-sm btn-outline-danger btn-delete" data-route="' . route("master-data.program.destroy", $row->id) . '"><i class="fas fa-trash"></i></button>
                    ';
                })
                ->rawColumns(["action", "program_name", "program_code"])
                ->make("true");
        }
    }

    function store(Request $request)
    {
        if (!hasPermissionInGuard('Data Program - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "kategori_program" => "required|string",
            "kode_program" => "required|string",
            "nama_program" => "required|string",
            "tipe_program" => "required|string",
            "asset_type" => "required|string",
            "top_parent" => "required|string",
            "parent_id" => "required|string",
        ]);

        try {
            DB::beginTransaction();

            Program::create([
                "program_category" => $request->kategori_program,
                "program_code" => $request->kode_program,
                "program_name" => $request->nama_program,
                "program_type" => $request->tipe_program,
                "asset_type" => $request->asset_type,
                "top_parent_id" => $request->top_parent,
                "parent_id" => $request->parent_id,
                "created_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            Log::info($th);
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function show(Program $program)
    {
        return response()->json([
            "data" => $program
        ], 200);
    }

    function update(Request $request, Program $program)
    {
        if (!hasPermissionInGuard('Data Program - Action')) {
            abort(403, "Unauthorized action.");
        }

        $request->validate([
            "nama_program" => "required|string",
            "asset_type" => "required|string",
            "active" => "required|in:0,1",
        ]);

        try {
            DB::beginTransaction();

            $program->update([
                "program_name" => $request->nama_program,
                "asset_type" => $request->asset_type,
                "active" => $request->active,
                "updated_by" => getAuthUserId()
            ]);

            DB::commit();
            return response()->json([
                "message" => "Data berhasil disimpan",
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function destroy(Program $program)
    {
        if (!hasPermissionInGuard('Data Program - Action')) {
            abort(403, "Unauthorized action.");
        }

        try {
            DB::beginTransaction();

            $program->delete();

            DB::commit();
            return response()->json([
                "message" => "Data berhasil dihapus"
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => $th->getMessage()
            ], 500);
        }
    }

    function dropdown()
    {
        $categories = Program::search()
            ->paginate(25);

        return response()->json($categories);
    }
    function dropdown_top_parent(Request $request)
    {
        $kategori = $request->input('kategori');

        $topParents = Program::where('program_category', $kategori)
            ->where('top_parent_id', 0)
            ->paginate(25);
        Log::info($topParents);


        return response()->json($topParents);
    }

    public function dropdown_parent_id(Request $request)
    {
        $kategori = $request->input('kategori');

        $parentIds = Program::where('program_category', $kategori)
            ->where('program_type', 'GROUP')
            ->paginate(25);
        Log::info($parentIds);

        return response()->json($parentIds);
    }

    public function dropdown_output(Request $request)
    {
        $programs = Program::where('program_type', 'OUTPUT')
            ->when($request->has('q'), function($query) use ($request) {
                $query->where(function($q) use ($request) {
                    $q->where('program_name', 'like', '%'.$request->q.'%')
                      ->orWhere('program_code', 'like', '%'.$request->q.'%');
                });
            })
            ->paginate(25);

        return response()->json($programs);
    }
}
