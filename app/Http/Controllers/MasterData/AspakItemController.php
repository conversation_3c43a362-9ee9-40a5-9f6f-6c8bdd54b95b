<?php

namespace App\Http\Controllers\MasterData;

use App\Exports\AspakItemTemplateExport;
use App\Http\Controllers\Controller;
use App\Imports\AspakItemImport;
use App\Models\AspakItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Validators\ValidationException;

class AspakItemController extends Controller
{
    public function index()
    {
        $title = "Barang ASPAK";
        $breadcrumbs = ["Master Data", "Aspak", "Barang"];

        return view('master-data.aspak.item.index', compact('title', 'breadcrumbs'));
    }

    public function list(Request $request)
    {
        if (!$request->ajax()) {
            return response()->json([
                "data" => 'Not implemented'
            ], 200);
        }

        try {
            $queryBuilder = AspakItem::select('aspak_items.*');
            $getItems = $queryBuilder->get();

            // Add rendered action buttons to each row
            $getItems = $getItems->map(function ($row) {
                $row->action_buttons = view('components.master.aspak-item.action-buttons', compact('row'))->render();
                return $row;
            });
        } catch (\Throwable $th) {
            return response()->json([
                "data" => [],
                "message" => $th->getMessage()
            ], 500);
        }

        return response()->json([
            "data" => $getItems
        ]);
    }

    public function parentGroups(Request $request)
    {
        try {
            $query = AspakItem::branches()->select('id', 'item_name', 'item_code');

            if ($request->has('q')) {
                $search = $request->q;
                $query->where(function ($q) use ($search) {
                    $q->where('item_name', 'like', '%' . $search . '%')
                        ->orWhere('item_code', 'like', '%' . $search . '%');
                });
            }

            $groups = $query->paginate(10);

            // Format data untuk Select2
            $formattedGroups = $groups->map(function ($item) {
                return [
                    'id' => $item->id,
                    'text' => $item->item_code . ' - ' . $item->item_name
                ];
            });

            return response()->json([
                'data' => $formattedGroups,
                'total' => $groups->total()
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                "data" => [],
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function dropdownLeaves(Request $request)
    {
        try {
            $query = AspakItem::leaves()->select('id', 'item_name', 'item_code');

            if ($request->has('q')) {
                $search = $request->q;
                $query->where(function ($q) use ($search) {
                    $q->where('item_name', 'like', '%' . $search . '%')
                        ->orWhere('item_code', 'like', '%' . $search . '%');
                });
            }

            $leaves = $query->paginate(10);

            // Format data untuk Select2
            $formattedLeaves = $leaves->map(function ($item) {
                return [
                    'id' => $item->id,
                    'aspak_tool_name' => $item->item_code . ' - ' . $item->item_name
                ];
            });

            return response()->json([
                'data' => $formattedLeaves,
                'total' => $leaves->total()
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                "data" => [],
                "message" => $th->getMessage()
            ], 500);
        }
    }

    public function edit(AspakItem $aspakItem)
    {
        try {
            // Load parent data if exists
            $aspakItem->load('parent');

            // Format data for response
            $data = [
                'id' => $aspakItem->id,
                'item_name' => $aspakItem->item_name,
                'item_code' => $aspakItem->item_code,
                'item_synonym' => $aspakItem->item_synonym,
                'tree' => $aspakItem->tree,
                'parent_id' => $aspakItem->parent_id,
                'parent_name' => $aspakItem->parent ?
                    $aspakItem->parent->item_code . ' - ' . $aspakItem->parent->item_name : null
            ];

            return response()->json([
                "data" => $data
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => "Terjadi kesalahan saat mengambil data",
                "error" => $th->getMessage()
            ], 500);
        }
    }

    public function destroy(AspakItem $aspakItem)
    {
        try {
            // Check if this is a BRANCH and has children
            if ($aspakItem->tree === 'BRANCH' && $aspakItem->children()->count() > 0) {
                return response()->json([
                    'message' => 'Tidak dapat menghapus BRANCH yang memiliki sub-item. Hapus terlebih dahulu semua sub-item di dalamnya.',
                    'error' => 'BRANCH_HAS_CHILDREN'
                ], 422);
            }

            // Store information for success message
            $itemName = $aspakItem->item_name;
            $itemCode = $aspakItem->item_code;

            // Hard delete the record
            $aspakItem->delete();

            return response()->json([
                'message' => "Data '{$itemCode} - {$itemName}' berhasil dihapus",
                'data' => [
                    'deleted_id' => $aspakItem->id,
                    'deleted_name' => $itemName,
                    'deleted_code' => $itemCode
                ]
            ], 200);

        } catch (\Throwable $th) {
            return response()->json([
                'message' => 'Terjadi kesalahan saat menghapus data',
                'error' => $th->getMessage()
            ], 500);
        }
    }

    public function store(Request $request)
    {
        $request->validate([
            'item_name' => 'required|string|max:255',
            'item_code' => 'required|string|max:50|unique:aspak_items,item_code',
            'item_synonym' => 'nullable|string|max:255',
            'tree' => 'required|in:BRANCH,LEAF',
            'parent_id' => 'nullable|exists:aspak_items,id',
        ], [
            'item_name.required' => 'Nama barang wajib diisi',
            'item_code.required' => 'Kode barang wajib diisi',
            'item_code.unique' => 'Kode barang sudah digunakan, silakan gunakan kode lain',
            'tree.required' => 'Tree wajib diisi',
            'tree.in' => 'Tree harus BRANCH atau LEAF',
            'parent_id.exists' => 'Parent tidak valid',
        ]);

        // Additional validation for LEAF type
        if ($request->tree === 'LEAF' && !$request->parent_id) {
            return response()->json([
                'message' => 'Parent wajib diisi untuk tipe LEAF',
                'errors' => [
                    'parent_id' => ['Parent wajib diisi untuk tipe LEAF']
                ]
            ], 422);
        }

        try {
            $data = $request->only([
                'item_name',
                'item_code',
                'item_synonym',
                'tree',
                'parent_id'
            ]);

            // Add user information
            $data['created_by'] = auth()->id();
            $data['created_by_name'] = auth()->user()->name;

            $item = AspakItem::create($data);

            return response()->json([
                "data" => $item,
                "message" => "Data berhasil disimpan"
            ], 201);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => "Terjadi kesalahan saat menyimpan data",
                "error" => $th->getMessage()
            ], 500);
        }
    }

    public function update(Request $request, AspakItem $aspakItem)
    {
        $request->validate([
            'item_name' => 'required|string|max:255',
            'item_code' => 'required|string|max:50|unique:aspak_items,item_code,' . $aspakItem->id,
            'item_synonym' => 'nullable|string|max:255',
            'tree' => 'required|in:BRANCH,LEAF',
            'parent_id' => 'nullable|exists:aspak_items,id',
        ], [
            'item_name.required' => 'Nama barang wajib diisi',
            'item_code.required' => 'Kode barang wajib diisi',
            'item_code.unique' => 'Kode barang sudah digunakan, silakan gunakan kode lain',
            'tree.required' => 'Tree wajib diisi',
            'tree.in' => 'Tree harus BRANCH atau LEAF',
            'parent_id.exists' => 'Parent tidak valid',
        ]);

        // Additional validation for LEAF type
        if ($request->tree === 'LEAF' && !$request->parent_id) {
            return response()->json([
                'message' => 'Parent wajib diisi untuk tipe LEAF',
                'errors' => [
                    'parent_id' => ['Parent wajib diisi untuk tipe LEAF']
                ]
            ], 422);
        }

        // Check if changing from BRANCH to LEAF and has children
        if ($aspakItem->tree === 'BRANCH' &&
            $request->tree === 'LEAF' &&
            $aspakItem->children()->count() > 0) {
            return response()->json([
                'message' => 'Tidak dapat mengubah BRANCH yang memiliki sub-item menjadi LEAF',
                'errors' => [
                    'tree' => ['Tidak dapat mengubah BRANCH yang memiliki sub-item menjadi LEAF']
                ]
            ], 422);
        }

        // Prevent setting parent to self or child
        if ($request->parent_id) {
            if ($request->parent_id == $aspakItem->id) {
                return response()->json([
                    'message' => 'Tidak dapat menjadikan diri sendiri sebagai parent',
                    'errors' => [
                        'parent_id' => ['Tidak dapat menjadikan diri sendiri sebagai parent']
                    ]
                ], 422);
            }

            // Check if the selected parent is a descendant of current item
            $isDescendant = $this->isDescendant($aspakItem->id, $request->parent_id);
            if ($isDescendant) {
                return response()->json([
                    'message' => 'Tidak dapat menjadikan sub-item sebagai parent',
                    'errors' => [
                        'parent_id' => ['Tidak dapat menjadikan sub-item sebagai parent']
                    ]
                ], 422);
            }
        }

        try {
            $data = $request->only([
                'item_name',
                'item_code',
                'item_synonym',
                'tree',
                'parent_id'
            ]);

            // Add user information
            $data['updated_by'] = auth()->id();
            $data['updated_by_name'] = auth()->user()->name;

            $aspakItem->update($data);

            return response()->json([
                "data" => $aspakItem,
                "message" => "Data berhasil diperbarui"
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                "message" => "Terjadi kesalahan saat memperbarui data",
                "error" => $th->getMessage()
            ], 500);
        }
    }

    /**
     * Check if an item is a descendant of another item
     *
     * @param int $parentId
     * @param int $childId
     * @return bool
     */
    private function isDescendant($parentId, $childId)
    {
        $child = AspakItem::find($childId);

        while ($child && $child->parent_id) {
            if ($child->parent_id == $parentId) {
                return true;
            }
            $child = $child->parent;
        }

        return false;
    }

    /**
     * Import ASPAK items from Excel file
     */
    public function import(Request $request)
    {
        try {
            // Validate the uploaded file with stricter validation
            $validator = Validator::make($request->all(), [
                'excel_file' => [
                    'required',
                    'file',
                    'mimes:xlsx,xls',
                    'mimetypes:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel',
                    'max:5120' // 5MB max
                ]
            ], [
                'excel_file.required' => 'File Excel wajib dipilih.',
                'excel_file.file' => 'File yang dipilih tidak valid.',
                'excel_file.mimes' => 'File harus berformat Excel (.xlsx atau .xls).',
                'excel_file.mimetypes' => 'File harus berformat Excel yang valid.',
                'excel_file.max' => 'Ukuran file maksimal 5MB.'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validasi file gagal.',
                    'errors' => $validator->errors()
                ], 422);
            }

            $file = $request->file('excel_file');

            // Additional file validation
            if (!$this->isValidExcelFile($file)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File yang diupload bukan file Excel yang valid. Pastikan file memiliki format .xlsx atau .xls yang benar.'
                ], 422);
            }

            // Create import instance to access error collection
            $import = new AspakItemImport();

            // Import the Excel file (transaction is handled inside import class)
            Excel::import($import, $file);

            // Check for errors collected during import
            $errors = $import->getErrors();
            $successCount = $import->getSuccessCount();

            if (!empty($errors)) {
                // Partial success scenario - some records imported, some failed
                return response()->json([
                    'success' => false,
                    'message' => "Import selesai dengan {$successCount} data berhasil dan " . count($errors) . " data gagal.",
                    'errors' => $errors,
                    'success_count' => $successCount,
                    'error_count' => count($errors),
                    'partial_success' => true
                ], 422);
            }

            return response()->json([
                'success' => true,
                'message' => "Data ASPAK Item berhasil diimport. Total: {$successCount} record.",
                'success_count' => $successCount
            ]);

        } catch (ValidationException $e) {
            DB::rollBack();

            $failures = $e->failures();
            $errors = [];
            $errorCount = 0;

            foreach ($failures as $failure) {
                $errorCount++;
                $errors[] = [
                    'row' => $failure->row(),
                    'attribute' => $failure->attribute(),
                    'errors' => $failure->errors(),
                    'values' => $failure->values()
                ];
            }

            return response()->json([
                'success' => false,
                'message' => 'Terdapat kesalahan validasi pada file Excel.',
                'errors' => $errors,
                'error_count' => $errorCount
            ], 422);

        } catch (\Maatwebsite\Excel\Exceptions\NoTypeDetectedException $e) {
            DB::rollBack();
            Log::error('Excel type detection failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'File yang diupload tidak dapat dikenali sebagai file Excel. Pastikan file memiliki format .xlsx atau .xls yang benar dan tidak rusak.'
            ], 422);

        } catch (\PhpOffice\PhpSpreadsheet\Exception $e) {
            DB::rollBack();
            Log::error('PhpSpreadsheet error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'File Excel tidak dapat dibaca. Pastikan file tidak rusak dan memiliki format yang benar.'
            ], 422);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error importing ASPAK items: ' . $e->getMessage());

            // Check if it's an OLE file error
            if (strpos($e->getMessage(), 'OLE file') !== false || strpos($e->getMessage(), 'not recognised') !== false) {
                return response()->json([
                    'success' => false,
                    'message' => 'File yang diupload tidak dapat dikenali sebagai file Excel yang valid. Silakan gunakan file Excel (.xlsx atau .xls) yang dibuat dengan Microsoft Excel atau aplikasi spreadsheet lainnya.'
                ], 422);
            }

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengimport data. Silakan periksa format file Excel dan coba lagi.'
            ], 500);
        }
    }

    /**
     * Validate if the uploaded file is a valid Excel file
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @return bool
     */
    private function isValidExcelFile($file)
    {
        try {
            // Check file extension
            $extension = strtolower($file->getClientOriginalExtension());
            if (!in_array($extension, ['xlsx', 'xls'])) {
                return false;
            }

            // Check MIME type
            $mimeType = $file->getMimeType();
            $validMimeTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
                'application/vnd.ms-excel', // .xls
                'application/excel',
                'application/x-excel',
                'application/x-msexcel'
            ];

            if (!in_array($mimeType, $validMimeTypes)) {
                return false;
            }

            // Try to read the file header to validate it's a real Excel file
            $filePath = $file->getRealPath();
            $handle = fopen($filePath, 'rb');

            if (!$handle) {
                return false;
            }

            $header = fread($handle, 8);
            fclose($handle);

            // Check for Excel file signatures
            if ($extension === 'xlsx') {
                // XLSX files start with PK (ZIP signature)
                return substr($header, 0, 2) === 'PK';
            } elseif ($extension === 'xls') {
                // XLS files have OLE signature
                return substr($header, 0, 8) === "\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1" ||
                    substr($header, 0, 4) === "\x09\x08\x06\x00" ||
                    substr($header, 0, 4) === "\x09\x08\x08\x00";
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Error validating Excel file: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Download Excel template for ASPAK item import
     */
    public function downloadTemplate()
    {
        try {
            return Excel::download(
                new AspakItemTemplateExport,
                'template_aspak_item.xlsx'
            );
        } catch (\Exception $e) {
            Log::error('Error downloading ASPAK item template: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mendownload template: ' . $e->getMessage()
            ], 500);
        }
    }
}
