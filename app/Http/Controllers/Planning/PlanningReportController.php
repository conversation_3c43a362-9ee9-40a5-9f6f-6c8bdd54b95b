<?php

namespace App\Http\Controllers\Planning;

use App\Http\Controllers\Controller;
use App\Models\Program;
use App\Models\Plan;
use Illuminate\Http\Request;
use App\Exports\PlanningReportExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\MaintenancePlan;
use App\Exports\MaintenanceReportExport;

class PlanningReportController extends Controller
{
    function planning(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Perencanaan - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = Plan::with(["program", "room", "planDetails.planDetailPrograms", "planDetails.uom"])
                ->whereNotNull('approval_date')
                ->when($request->year, function($query) use ($request) {
                    return $query->whereYear('plan_date', $request->year);
                })
                ->orderBy('id', 'DESC')
                ->get();

            return view("components.planning.report_planning", compact("data"));
        }

        $title = "Laporan Perencanaan";
        $breadcrumbs = ["Laporan", "Perencanaan"];

        return view("planning.report.planning", compact("title", "breadcrumbs"));
    }

    public function export(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Perencanaan - View')) {
            abort(403, "Unauthorized action.");
        }

        if (!$request->year) {
            return back()->with('error', 'Silakan pilih tahun terlebih dahulu');
        }

        return Excel::download(new PlanningReportExport($request->year), 'laporan_perencanaan_' . $request->year . '.xlsx');
    }

    function maintenance(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Pemeliharaan Perencanaan - View')) {
            abort(403, "Unauthorized action.");
        }

        if ($request->ajax()) {
            $data = MaintenancePlan::with([
                'room',
                'maintenancePlanDetails.item',
                'maintenancePlanDetails.uom',
                'maintenancePlanDetails.maintenanceCategory',
                'maintenancePlanDetails.maintenancePlanDetailPrograms' => function($query) {
                    $query->orderBy('level', 'asc');
                }
            ])
            ->whereNotNull('approver_id')
            ->when($request->year, function($query) use ($request) {
                return $query->whereYear('maintenance_plan_date', $request->year);
            })
            ->orderBy('id', 'DESC')
            ->get();

            return view("components.planning.report_maintenance", compact("data"));
        }

        $title = "Laporan Pemeliharaan";
        $breadcrumbs = ["Laporan", "Pemeliharaan"];

        return view("planning.report.maintenance", compact("title", "breadcrumbs"));
    }

    public function exportMaintenance(Request $request)
    {
        if (!hasPermissionInGuard('Laporan Pemeliharaan Perencanaan - View')) {
            abort(403, "Unauthorized action.");
        }

        if (!$request->year) {
            return back()->with('error', 'Silakan pilih tahun terlebih dahulu');
        }

        $maintenancePlans = MaintenancePlan::with([
            'room',
            'maintenancePlanDetails.item',
            'maintenancePlanDetails.uom',
            'maintenancePlanDetails.maintenanceCategory',
            'maintenancePlanDetails.maintenancePlanDetailPrograms' => function($query) {
                $query->orderBy('level', 'asc');
            }
        ])
        ->whereNotNull('approver_id')
        ->whereYear('maintenance_plan_date', $request->year)
        ->orderBy('id', 'DESC')
        ->get();

        return Excel::download(
            new MaintenanceReportExport($maintenancePlans, $request->year),
            'laporan_pemeliharaan_' . $request->year . '.xlsx'
        );
    }
}
