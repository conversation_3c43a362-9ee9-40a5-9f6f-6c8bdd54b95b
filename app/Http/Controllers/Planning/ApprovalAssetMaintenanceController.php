<?php

namespace App\Http\Controllers\Planning;

use App\Http\Controllers\Controller;
use App\Models\MaintenancePlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use App\Exports\MaintenancePlanExport;
use Maatwebsite\Excel\Facades\Excel;

class ApprovalAssetMaintenanceController extends Controller
{

    function index()
    {
        if (!hasPermissionInGuard('Approve Pemeliharaan - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Pengajuan Pemeliharaan Barang";
        $breadcrumbs = ["Perencanaan", "Pengajuan Pemeliharaan Barang"];

        return view('planning.approval-asset-maintenance.index', compact('title', "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Approve Pemeliharaan - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $data = MaintenancePlan::with(["room", "program"])
                ->filterApprove()
                ->when($request->year, function($query) use ($request) {
                    return $query->whereYear('maintenance_plan_date', $request->year);
                })
                ->when($request->room && $request->room != 'all', function($query) use ($request) {
                    return $query->where('room_id', $request->room);
                })
                ->orderBy('id', 'DESC');

            return DataTables::eloquent($data)
                ->addColumn('judul', function ($row) {
                    return $row->maintenance_plan_title;
                })
                ->addColumn('ruangan', function ($row) {
                    return $row->room ? $row->room->room_code . ' - ' . $row->room->room_name : '-';
                })
                ->addColumn('created_at', function ($row) {
                    return $row->created_at ? date('d/m/Y', strtotime($row->created_at)) : '-';
                })
                ->addColumn('tahun', function ($row) {
                    return $row->maintenance_plan_date ? date('Y', strtotime($row->maintenance_plan_date)) : '-';
                })
                ->addColumn('total_qty', function ($row) {
                    return number_format($row->maintenance_plan_total_quantity, 0, ',', '.');
                })
                ->addColumn('action', function ($row) {
                    $btn = '<button type="button" class="btn btn-sm btn-outline-info btn-show" data-route="' . route('planning.asset-maintenance.show', $row->id) . '" data-id="' . $row->id . '"><i class="fas fa-search"></i></button>';

                    if ($row->approver_id == null) {
                        $btn .= ' <button type="button" class="btn btn-sm btn-outline-warning btn-approve" data-route="' . route('planning.approval-asset-maintenance.approve', $row->id) . '"><i class="fas fa-check"></i></button>';
                    }
                    return $btn;
                })
                ->addColumn("status", function ($row) {
                    if ($row->approver_id != null) {
                        return '<span class="badge bg-success">Disetujui</span>';
                    } else {
                        return '<span class="badge bg-danger">Belum Disetujui</span>';
                    }
                })
                ->rawColumns(['action', "status"])
                ->make(true);
        }
    }

    function approve(MaintenancePlan $maintenancePlan)
    {
        if (!hasPermissionInGuard('Approve Pemeliharaan - Action')) {
            abort(403, "Unauthorized action.");
        }
        try {
            DB::beginTransaction();

            $maintenancePlan->update([
                "approver_id" => getAuthUserId(),
                "approver_name" => getAuthUserName(),
            ]);

            DB::commit();
            return response()->json(["message" => "Berhasil disetujui"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(["message" => $th->getMessage()], 500);
        }
    }

    function show($id)
    {
        if (!hasPermissionInGuard('Approve Pemeliharaan - View')) {
            abort(403, "Unauthorized action.");
        }

        $maintenancePlan = MaintenancePlan::with([
            'room',
            'maintenancePlanDetails.item',
            'maintenancePlanDetails.uom',
            'maintenancePlanDetails.maintenanceCategory',
            'maintenancePlanDetails.maintenancePlanDetailPrograms' => function($query) {
                $query->orderBy('level', 'asc');
            }
        ])->findOrFail($id);

        if (request()->ajax()) {
            return view('planning.asset-maintenance.show-modal', compact('maintenancePlan'));
        }

        $title = "Detail Pemeliharaan";
        $breadcrumbs = ["Perencanaan", "Detail Pemeliharaan Barang"];

        return view('planning.asset-maintenance.show', compact('title', 'breadcrumbs', 'maintenancePlan'));
    }

    function export($id)
    {
        if (!hasPermissionInGuard('Approve Pemeliharaan - View')) {
            abort(403, "Unauthorized action.");
        }

        $maintenancePlan = MaintenancePlan::with([
            'room',
            'maintenancePlanDetails.item',
            'maintenancePlanDetails.uom',
            'maintenancePlanDetails.maintenanceCategory',
            'maintenancePlanDetails.maintenancePlanDetailPrograms' => function($query) {
                $query->orderBy('level', 'asc');
            }
        ])->findOrFail($id);

        return Excel::download(
            new MaintenancePlanExport($maintenancePlan),
            'Pemeliharaan_' . $maintenancePlan->maintenance_plan_number . '.xlsx'
        );
    }
}
