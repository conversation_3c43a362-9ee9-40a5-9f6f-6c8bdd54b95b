<?php

namespace App\Http\Controllers\Planning;

use App\Http\Controllers\Controller;
use App\Models\Asset;
use App\Models\AssetEntry;
use App\Models\Item;
use App\Models\Plan;
use App\Models\Uom;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Validation\ValidationException;
use App\Exports\ConsumableProcurementExport;
use Maatwebsite\Excel\Facades\Excel;

class ConsumableProcurementController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Perencanaan - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Perencanaan Pengadaan";
        $breadcrumbs = ["Perencanaan", "Pengadaan"];

        return view('planning.consumable-procurement.index', compact('title', "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Perencanaan - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $data = Plan::with(["program", "room"])
                ->filterApprove()
                ->when($request->year, function($query) use ($request) {
                    return $query->whereYear('request_date', $request->year);
                })
                ->when($request->room && $request->room != 'all', function($query) use ($request) {
                    return $query->where('room_id', $request->room);
                })
                ->when($request->status && $request->status != 'all', function($query) use ($request) {
                    if ($request->status === 'approved') {
                        return $query->whereNotNull('approval_date');
                    } 
                    if ($request->status === 'notapproved') {
                        return $query->whereNull('approval_date');
                    }
                    return $query;
                })
                ->orderBy('id', 'DESC')
                ->get();

            return DataTables::of($data)
                ->addColumn('program', function ($row) {
                    return $row->program ? $row->program->program_name : '-';
                })
                ->addColumn('room', function ($row) {
                    return $row->room ? $row->room->room_code . ' - ' . $row->room->room_name : '-';
                })
                ->addColumn('request_date', function ($row) {
                    return $row->request_date ? date('Y', strtotime($row->request_date)) : '-';
                })
                ->addColumn('created_date', function ($row) {
                    return $row->created_at ? date('d/m/Y', strtotime($row->created_at)) : '-';
                })
                ->addColumn('total_qty', function ($row) {
                    return number_format($row->total_plan_quantity, 0, ',', '.');
                })
                ->addColumn('action', function ($row) {
                    $btn = '<a href="#modal-dialog" data-route="' . route('planning.consumable-procurement.show', $row->id) . '" class="btn btn-sm btn-outline-info btn-show" data-bs-toggle="modal"><i class="fas fa-search"></i></a>';

                    if ($row->approval_date == null) {
                        $btn .= ' <button type="button" class="btn btn-sm btn-outline-danger btn-delete" data-route="' . route('planning.consumable-procurement.destroy', $row->id) . '"><i class="fas fa-trash"></i></button>';
                    }
                    return $btn;
                })
                ->addColumn("status", function ($row) {
                    if ($row->approval_date != null) {
                        return '<span class="badge bg-success">Disetujui</span>';
                    } else {
                        return '<span class="badge bg-danger">Belum Disetujui</span>';
                    }
                })
                ->rawColumns(['action', 'status'])
                ->make(true);
        }
    }

    function create()
    {
        if (!hasPermissionInGuard('Perencanaan - Action')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Buat Perencanaan Pengadaan";
        $breadcrumbs = ["Perencanaan", "Buat Pengadaan"];

        return view('planning.consumable-procurement.create', compact('title', "breadcrumbs"));
    }

    function store(Request $request)
    {
        if (!hasPermissionInGuard('Perencanaan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $messages = [
            'tanggal.required' => 'Tanggal harus diisi',
            'tanggal.date' => 'Format tanggal tidak valid',
            'room.required' => 'Ruangan harus dipilih',
            'room.exists' => 'Ruangan yang dipilih tidak valid',
            'plan_title.required' => 'Judul perencanaan harus diisi',
            'forms.required' => 'Data form harus diisi',
            'forms.array' => 'Format data form tidak valid',
            'forms.*.output.required' => 'Program output harus dipilih',
            'forms.*.output.exists' => 'Program output yang dipilih tidak valid',
            'forms.*.asset_code.*.required_with' => 'Kode barang harus diisi jika ada nama barang, uom, atau qty',
            'forms.*.asset_name.*.required_with' => 'Nama barang harus diisi jika ada kode barang, uom, atau qty',
            'forms.*.asset_id.*.required_with' => 'Barang harus dipilih jika ada uom atau qty',
            'forms.*.uom.*.required_with' => 'UOM harus dipilih jika ada data barang',
            'forms.*.uom.*.exists' => 'UOM yang dipilih tidak valid',
            'forms.*.qty.*.required_with' => 'Quantity harus diisi jika ada data barang',
            'forms.*.qty.*.numeric' => 'Quantity harus berupa angka',
            'forms.*.qty.*.min' => 'Quantity minimal 1',
        ];

        $request->validate([
            "tanggal" => "required|date",
            "room" => "required|exists:rooms,id",
            "plan_title" => "required|string|max:128",
            "plan_notes" => "nullable|string",
            "forms" => "required|array",
            "forms.*.output" => "required|exists:programs,id",
            "forms.*.asset_code" => "array|nullable",
            "forms.*.asset_code.*" => "required_with:forms.*.asset_name.*,forms.*.uom.*,forms.*.qty.*",
            "forms.*.asset_name" => "array|nullable",
            "forms.*.asset_name.*" => "required_with:forms.*.asset_code.*,forms.*.uom.*,forms.*.qty.*",
            "forms.*.asset_id" => "array|nullable",
            "forms.*.asset_id.*" => "required_with:forms.*.uom.*,forms.*.qty.*",
            "forms.*.uom" => "array|nullable",
            "forms.*.uom.*" => "required_with:forms.*.asset_code.*,forms.*.asset_name.*,forms.*.qty.*|exists:uoms,id",
            "forms.*.qty" => "array|nullable",
            "forms.*.qty.*" => "required_with:forms.*.asset_code.*,forms.*.asset_name.*,forms.*.uom.*|numeric|min:1",
        ], $messages);

        foreach ($request->forms as $index => $form) {
            if (count($form) <= 1) {
                continue;
            }
            if (isset($form['asset_code'])) {
                $count = count($form['asset_code']);
                if (!isset($form['asset_name']) || count($form['asset_name']) !== $count) {
                    throw ValidationException::withMessages([
                        "forms.$index.asset_name" => ["Jumlah nama barang tidak sesuai dengan jumlah kode barang"]
                    ]);
                }
                if (!isset($form['uom']) || count($form['uom']) !== $count) {
                    throw ValidationException::withMessages([
                        "forms.$index.uom" => ["Jumlah UOM tidak sesuai dengan jumlah barang"]
                    ]);
                }
                if (!isset($form['qty']) || count($form['qty']) !== $count) {
                    throw ValidationException::withMessages([
                        "forms.$index.qty" => ["Jumlah quantity tidak sesuai dengan jumlah barang"]
                    ]);
                }
            }

            if (isset($form['asset_id'])) {
                $count = count($form['asset_id']);
                
                if (!isset($form['uom']) || count($form['uom']) !== $count) {
                    throw ValidationException::withMessages([
                        "forms.$index.uom" => ["Jumlah UOM tidak sesuai dengan jumlah barang yang dipilih"]
                    ]);
                }
                
                if (!isset($form['qty']) || count($form['qty']) !== $count) {
                    throw ValidationException::withMessages([
                        "forms.$index.qty" => ["Jumlah quantity tidak sesuai dengan jumlah barang yang dipilih"]
                    ]);
                }
            }
        }

        try {
            DB::beginTransaction();

            // Menyiapkan data untuk tabel plans
            $planData = [
                'program_id' => null,
                'plan_number' => $this->generatePlanNumber(),
                'plan_date' => $request->tanggal,
                'request_date' => $request->tanggal,
                'room_id' => $request->room,
                'plan_title' => $request->plan_title,
                'plan_notes' => $request->plan_notes,
                'requester_id' => auth()->guard("employee")->user() ? auth()->guard('employee')->user()->id : getAuthUserId(),
                'requester_name' => auth()->guard("employee")->user() ? auth()->guard('employee')->user()->employee_name : getAuthUserName(),
                'created_by' => auth()->guard("employee")->user() ? auth()->guard('employee')->user()->id : getAuthUserId(),
                'created_by_name' => auth()->guard("employee")->user() ? auth()->guard('employee')->user()->employee_name : getAuthUserName(),
                'total_plan_quantity' => 0,
                'total_plan_price' => 0,
            ];

            // Menyiapkan data untuk tabel plan_details dan plan_detail_programs
            $planDetailsData = [];
            $planDetailProgramsData = [];
            $planDetailProgramsTemp = []; // Untuk menampung sementara dan menghindari duplikasi

            foreach ($request->forms as $index => $form) {
                if (count($form) <= 1) {
                    continue;
                }

                // Ambil data program output untuk mendapatkan hierarki program
                $programOutput = DB::table('programs as p')
                    ->where('p.id', $form['output'])
                    ->select([
                        'p.*',
                        DB::raw('(SELECT id FROM programs WHERE id = p.top_parent_id) as top_parent_id'),
                        DB::raw('(SELECT program_code FROM programs WHERE id = p.top_parent_id) as top_parent_code'),
                        DB::raw('(SELECT program_name FROM programs WHERE id = p.top_parent_id) as top_parent_name'),
                        DB::raw('(SELECT id FROM programs WHERE id = p.parent_id) as parent_id'),
                        DB::raw('(SELECT program_code FROM programs WHERE id = p.parent_id) as parent_code'),
                        DB::raw('(SELECT program_name FROM programs WHERE id = p.parent_id) as parent_name'),
                    ])
                    ->first();

                if (!$planData['program_id'] && $programOutput) {
                    $planData['program_id'] = $programOutput->top_parent_id;
                }

                if (isset($form['asset_id'])) {
                    foreach ($form['asset_id'] as $key => $assetId) {
                        $detailData = [
                            'type' => $programOutput->asset_type,
                            'program_id' => $form['output'],
                            'uom_id' => $form['uom'][$key],
                            'quantity' => $form['qty'][$key],
                            'unit_price' => 0,
                            'total_price' => 0,
                            'item_id' => null,
                            'asset_id' => null,
                            'asset_entry_id' => null,
                        ];

                        // Ambil uom_name dari tabel uoms
                        $uom = Uom::find($form['uom'][$key]);
                        if ($uom) {
                            $detailData['uom_name'] = $uom->uom_name;
                        }

                        // Cek tipe asset dan set field yang sesuai
                        if ($programOutput->asset_type === 'ASSET') {
                            // Untuk ASSET, gunakan item_id dan cari di tabel items
                            $item = Item::find($assetId);
                            if ($item) {
                                $detailData['item_id'] = $assetId;
                                $detailData['code'] = $item->item_code;
                                $detailData['name'] = $item->item_name;
                            } else {
                                throw new \Exception("Item dengan ID {$assetId} tidak ditemukan");
                            }
                        } else if ($programOutput->asset_type === 'LOGISTIC') {
                            // Untuk LOGISTIC, gunakan asset_entry_id
                            $assetEntry = AssetEntry::find($assetId);
                            if ($assetEntry) {
                                $detailData['asset_entry_id'] = $assetId;
                                $detailData['code'] = $assetEntry->asset_entry_code;
                                $detailData['name'] = $assetEntry->asset_name;
                            } else {
                                throw new \Exception("Asset Entry dengan ID {$assetId} tidak ditemukan");
                            }
                        }

                        $planDetailsData[] = $detailData;
                    }
                }

                if (isset($form['asset_code'])) {
                    foreach ($form['asset_code'] as $key => $code) {
                        // Ambil uom_name dari tabel uoms
                        $uom = Uom::find($form['uom'][$key]);
                        
                        $planDetailsData[] = [
                            'type' => 'OTHER',
                            'program_id' => $form['output'],
                            'code' => $code,
                            'name' => $form['asset_name'][$key],
                            'uom_id' => $form['uom'][$key],
                            'uom_name' => $uom ? $uom->uom_name : null,
                            'quantity' => $form['qty'][$key],
                            'unit_price' => 0,
                            'total_price' => 0,
                            'item_id' => null,
                            'asset_id' => null,
                            'asset_entry_id' => null,
                        ];
                    }
                }

                // Menyiapkan data program hierarchy untuk plan_detail_programs
                if ($programOutput) {
                    // Program Output
                    $outputKey = "output_{$programOutput->id}";
                    if (!isset($planDetailProgramsTemp[$outputKey])) {
                        $planDetailProgramsTemp[$outputKey] = [
                            'program_type' => 'OUTPUT',
                            'program_code' => $programOutput->program_code,
                            'program_name' => $programOutput->program_name,
                            'level' => 3
                        ];
                    }

                    // Program Parent (Kegiatan)
                    if ($programOutput->parent_id) {
                        $parentKey = "parent_{$programOutput->parent_id}";
                        if (!isset($planDetailProgramsTemp[$parentKey])) {
                            $planDetailProgramsTemp[$parentKey] = [
                                'program_type' => 'GROUP',
                                'program_code' => $programOutput->parent_code,
                                'program_name' => $programOutput->parent_name,
                                'level' => 2
                            ];
                        }
                    }

                    // Program Top Parent
                    if ($programOutput->top_parent_id) {
                        $topKey = "top_{$programOutput->top_parent_id}";
                        if (!isset($planDetailProgramsTemp[$topKey])) {
                            $planDetailProgramsTemp[$topKey] = [
                                'program_type' => 'GROUP',
                                'program_code' => $programOutput->top_parent_code,
                                'program_name' => $programOutput->top_parent_name,
                                'level' => 1
                            ];
                        }
                    }
                }
            }

            // Convert temporary program data to final array
            $planDetailProgramsData = array_values($planDetailProgramsTemp);

            // Hitung total quantity dan price
            $planData['total_plan_quantity'] = array_sum(array_column($planDetailsData, 'quantity'));
            $planData['total_plan_price'] = array_sum(array_column($planDetailsData, 'total_price'));

            // Simpan plan
            $plan = Plan::create($planData);

            // Simpan plan details dan program hierarchy
            foreach ($planDetailsData as $detail) {
                $detail['plan_id'] = $plan->id;
                $planDetail = $plan->planDetails()->create($detail);

                // Simpan program hierarchy untuk setiap detail
                $programOutput = DB::table('programs as p')
                    ->where('p.id', $detail['program_id'])
                    ->select([
                        'p.*',
                        DB::raw('(SELECT id FROM programs WHERE id = p.top_parent_id) as top_parent_id'),
                        DB::raw('(SELECT program_code FROM programs WHERE id = p.top_parent_id) as top_parent_code'),
                        DB::raw('(SELECT program_name FROM programs WHERE id = p.top_parent_id) as top_parent_name'),
                        DB::raw('(SELECT id FROM programs WHERE id = p.parent_id) as parent_id'),
                        DB::raw('(SELECT program_code FROM programs WHERE id = p.parent_id) as parent_code'),
                        DB::raw('(SELECT program_name FROM programs WHERE id = p.parent_id) as parent_name'),
                    ])
                    ->first();

                if ($programOutput) {
                    // Program Output
                    DB::table('plan_detail_programs')->insert([
                        'plan_detail_id' => $planDetail->id,
                        'program_type' => 'OUTPUT',
                        'program_code' => $programOutput->program_code,
                        'program_name' => $programOutput->program_name,
                        'level' => 3
                    ]);

                    // Program Parent (Kegiatan)
                    if ($programOutput->parent_id) {
                        DB::table('plan_detail_programs')->insert([
                            'plan_detail_id' => $planDetail->id,
                            'program_type' => 'GROUP',
                            'program_code' => $programOutput->parent_code,
                            'program_name' => $programOutput->parent_name,
                            'level' => 2
                        ]);
                    }

                    // Program Top Parent
                    if ($programOutput->top_parent_id) {
                        DB::table('plan_detail_programs')->insert([
                            'plan_detail_id' => $planDetail->id,
                            'program_type' => 'GROUP',
                            'program_code' => $programOutput->top_parent_code,
                            'program_name' => $programOutput->top_parent_name,
                            'level' => 1
                        ]);
                    }
                }
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Data perencanaan berhasil disimpan',
                'data' => [
                    'id' => $plan->id,
                    'plan_number' => $plan->plan_number
                ]
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => 'Terjadi kesalahan saat menyimpan data: ' . $e->getMessage()
            ], 500);
        }
    }

    function show(Plan $plan)
    {
        return view("components.planning.consumable-procurement", ["plan" => $plan]);
    }

    function _dropdownProgram()
    {
        if (request()->has('room_id')) {
            $programs = DB::table('programs as p')
                ->join('room_has_programs as rhp', 'rhp.program_id', '=', 'p.id')
                ->where('rhp.room_id', request('room_id'))
                ->where('p.program_type', 'OUTPUT')
                ->select([
                    'p.*',
                    DB::raw('(SELECT program_code FROM programs WHERE id = p.top_parent_id) as top_parent_code'),
                    DB::raw('(SELECT program_name FROM programs WHERE id = p.top_parent_id) as top_parent_name'),
                    DB::raw('(SELECT program_code FROM programs WHERE id = p.parent_id) as parent_code'),
                    DB::raw('(SELECT program_name FROM programs WHERE id = p.parent_id) as parent_name')
                ])
                ->get();

            return response()->json([
                'data' => $programs
            ]);
        }

        $programs = DB::table("programs")
            ->where(["top_parent_id" => 0, "program_category" => "PERENCANAAN"])
            ->when(request("q"), function ($query) {
                return $query->where("program_name", "like", "%" . request("q") . "%");
            })
            ->paginate(25);

        return response()->json($programs);
    }

    function _dropdownKegiatan()
    {
        $kegiatans = DB::table("programs")
            ->where(["program_type" => "GROUP", "parent_id" => request("parent_id")])
            ->when(request("q"), function ($query) {
                return $query->where("program_name", "like", "%" . request("q") . "%");
            })
            ->paginate(25);

        return response()->json($kegiatans);
    }


    function _dropdownProgramOutput()
    {
        $programs = DB::table("programs")
            ->where("program_type", "OUTPUT")
            ->where("parent_id", request("parent_id"))
            ->when(request("q"), function ($query) {
                return $query->where("program_name", "like", "%" . request("q") . "%");
            })
            ->paginate(25);

        return response()->json($programs);
    }

    // Helper function to generate plan number
    private function generatePlanNumber()
    {
        $prefix = 'PLAN-' . date('Ym');
        $lastPlan = Plan::where('plan_number', 'like', $prefix . '%')
            ->orderBy('plan_number', 'desc')
            ->first();

        if ($lastPlan) {
            $lastNumber = intval(substr($lastPlan->plan_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . $newNumber;
    }

    public function exportExcel(Plan $plan)
    {
        if (!hasPermissionInGuard('Perencanaan - View')) {
            abort(403, "Unauthorized action.");
        }

        return Excel::download(new ConsumableProcurementExport($plan), 'perencanaan_pengadaan_' . $plan->plan_number . '.xlsx');
    }

    public function destroy(Plan $plan)
    {
        if (!hasPermissionInGuard('Perencanaan - Action')) {
            abort(403, "Unauthorized action.");
        }

        // Double check jika data sudah disetujui
        if ($plan->approval_date != null) {
            return response()->json([
                'status' => 'error',
                'message' => 'Data yang sudah disetujui tidak dapat dihapus'
            ], 422);
        }

        try {
            DB::beginTransaction();

            // Hapus data plan_detail_programs
            DB::table('plan_detail_programs')
                ->whereIn('plan_detail_id', function($query) use ($plan) {
                    $query->select('id')
                        ->from('plan_details')
                        ->where('plan_id', $plan->id);
                })
                ->delete();

            // Hapus data plan_details
            $plan->planDetails()->delete();

            // Hapus data plan
            $plan->delete();

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Data perencanaan berhasil dihapus'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => 'Terjadi kesalahan saat menghapus data: ' . $e->getMessage()
            ], 500);
        }
    }
}
