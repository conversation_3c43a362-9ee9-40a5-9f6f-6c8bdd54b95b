<?php

namespace App\Http\Controllers\Planning;

use App\Http\Controllers\Controller;
use App\Models\MaintenancePlan;
use App\Models\Uom;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Item;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\MaintenancePlanExport;
use Illuminate\Validation\ValidationException;

class AssetMaintenanceController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Pemeliharaan - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Pemeliharaan Barang";
        $breadcrumbs = ["Perencanaan", "Pemeliharaan Barang"];

        return view('planning.asset-maintenance.index', compact('title', "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Pemeliharaan - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $data = MaintenancePlan::with(["room", "program"])
                ->filterApprove()
                ->when($request->year, function($query) use ($request) {
                    return $query->whereYear('maintenance_plan_date', $request->year);
                })
                ->when($request->room && $request->room != 'all', function($query) use ($request) {
                    return $query->where('room_id', $request->room);
                })
                ->orderBy('id', 'DESC');

            return DataTables::eloquent($data)
                ->addColumn('judul', function ($row) {
                    return $row->maintenance_plan_title;
                })
                ->addColumn('ruangan', function ($row) {
                    return $row->room ? $row->room->room_code . ' - ' . $row->room->room_name : '-';
                })
                ->addColumn('created_at', function ($row) {
                    return $row->created_at ? date('d/m/Y', strtotime($row->created_at)) : '-';
                })
                ->addColumn('tahun', function ($row) {
                    return $row->maintenance_plan_date ? date('Y', strtotime($row->maintenance_plan_date)) : '-';
                })
                ->addColumn('total_qty', function ($row) {
                    return number_format($row->maintenance_plan_total_quantity, 0, ',', '.');
                })
                ->addColumn('action', function ($row) {
                    $btn = '<button type="button" class="btn btn-sm btn-outline-info btn-show" data-route="' . route('planning.asset-maintenance.show', $row->id) . '" data-id="' . $row->id . '"><i class="fas fa-search"></i></button>';

                    if ($row->approver_id == null) {
                        $btn .= ' <button type="button" class="btn btn-sm btn-outline-danger btn-delete" data-route="' . route('planning.asset-maintenance.destroy', $row->id) . '"><i class="fas fa-trash"></i></button>';
                    }
                    return $btn;
                })
                ->addColumn("status", function ($row) {
                    if ($row->approver_id != null) {
                        return '<span class="badge bg-success">Disetujui</span>';
                    } else {
                        return '<span class="badge bg-danger">Belum Disetujui</span>';
                    }
                })
                ->rawColumns(['action', "status"])
                ->make(true);
        }
    }

    function create()
    {
        if (!hasPermissionInGuard('Pemeliharaan - Action')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Buat Pemeliharaan";
        $breadcrumbs = ["Perencanaan", "Buat Pemeliharaan Barang"];

        return view('planning.asset-maintenance.create', compact('title', "breadcrumbs"));
    }

    function store(Request $request)
    {
        if (!hasPermissionInGuard('Pemeliharaan - Action')) {
            abort(403, "Unauthorized action.");
        }

        $messages = [
            'ruangan.required' => 'Ruangan harus dipilih',
            'ruangan.exists' => 'Ruangan yang dipilih tidak valid',
            'tanggal.required' => 'Tahun pemeliharaan harus diisi',
            'tanggal.date' => 'Format tanggal tidak valid',
            'judul.required' => 'Judul pemeliharaan harus diisi',
            'programs.required' => 'Program harus diisi',
            'programs.*.program_output_id.required' => 'Program output harus dipilih',
            'programs.*.details.required' => 'Detail program harus diisi',
            'programs.*.details.*.asset_id.required' => 'Asset harus dipilih',
            'programs.*.details.*.satuan.required' => 'Satuan harus dipilih',
            'programs.*.details.*.qty.required' => 'Quantity harus diisi',
            'programs.*.details.*.qty.numeric' => 'Quantity harus berupa angka',
            'programs.*.details.*.qty.min' => 'Quantity minimal 1',
            'programs.*.details.*.kondisi.required' => 'Kondisi barang harus dipilih',
            'programs.*.details.*.pemeliharaan.required' => 'Kategori pemeliharaan harus dipilih',
        ];

        $request->validate([
            'ruangan' => 'required|exists:rooms,id',
            'tanggal' => 'required|date',
            'judul' => 'required|string|max:128',
            'notes' => 'nullable|string',
            'programs' => 'required|array',
            'programs.*.program_output_id' => 'required|exists:programs,id',
            'programs.*.details' => 'required|array',
            'programs.*.details.*.asset_id' => 'required|exists:items,id',
            'programs.*.details.*.satuan' => 'required|exists:uoms,id',
            'programs.*.details.*.qty' => 'required|numeric|min:1',
            'programs.*.details.*.kondisi' => 'required|in:BAIK,RUSAK_RINGAN,RUSAK_BERAT',
            'programs.*.details.*.pemeliharaan' => 'required|exists:maintenance_categories,id',
        ], $messages);

        // Validasi array programs
        foreach ($request->programs as $index => $program) {
            // Validasi jika program kosong
            if (empty($program) || !isset($program['details']) || empty($program['details'])) {
                throw ValidationException::withMessages([
                    "programs.$index" => ["Program tidak boleh kosong"]
                ]);
            }

            // Validasi program_output_id
            if (!isset($program['program_output_id'])) {
                throw ValidationException::withMessages([
                    "programs.$index.program_output_id" => ["Program output harus dipilih"]
                ]);
            }

            // Validasi details array
            foreach ($program['details'] as $detailIndex => $detail) {
                // Validasi asset_id
                if (!isset($detail['asset_id'])) {
                    throw ValidationException::withMessages([
                        "programs.$index.details.$detailIndex.asset_id" => ["Asset harus dipilih"]
                    ]);
                }

                // Validasi satuan
                if (!isset($detail['satuan'])) {
                    throw ValidationException::withMessages([
                        "programs.$index.details.$detailIndex.satuan" => ["Satuan harus dipilih"]
                    ]);
                }

                // Validasi qty
                if (!isset($detail['qty'])) {
                    throw ValidationException::withMessages([
                        "programs.$index.details.$detailIndex.qty" => ["Quantity harus diisi"]
                    ]);
                }

                // Validasi kondisi
                if (!isset($detail['kondisi'])) {
                    throw ValidationException::withMessages([
                        "programs.$index.details.$detailIndex.kondisi" => ["Kondisi barang harus dipilih"]
                    ]);
                }

                // Validasi pemeliharaan
                if (!isset($detail['pemeliharaan'])) {
                    throw ValidationException::withMessages([
                        "programs.$index.details.$detailIndex.pemeliharaan" => ["Kategori pemeliharaan harus dipilih"]
                    ]);
                }

                // Validasi tipe data qty
                if (!is_numeric($detail['qty']) || $detail['qty'] <= 0) {
                    throw ValidationException::withMessages([
                        "programs.$index.details.$detailIndex.qty" => ["Quantity harus berupa angka dan lebih dari 0"]
                    ]);
                }
            }
        }

        try {
            DB::beginTransaction();

            // Hitung total quantity dari semua program
            $totalQuantity = 0;
            foreach ($request->programs as $program) {
                foreach ($program['details'] as $detail) {
                    $totalQuantity += $detail['qty'];
                }
            }

            // Buat maintenance plan
            $maintenancePlan = MaintenancePlan::create([
                'room_id' => $request->ruangan,
                'maintenance_plan_number' => $this->generateMaintenanceNumber(),
                'maintenance_plan_date' => $request->tanggal,
                'maintenance_plan_title' => $request->judul,
                'maintenance_plan_notes' => $request->notes,
                'maintenance_plan_total_quantity' => $totalQuantity,
                'request_date' => $request->tanggal,
                'requester_id' => auth()->guard('employee')->user() ? auth()->guard('employee')->user()->id : getAuthUserId(),
                'requester_name' => auth()->guard('employee')->user() ? auth()->guard('employee')->user()->employee_name : getAuthUserName(),
                'created_by' => auth()->guard('employee')->user() ? auth()->guard('employee')->user()->id : getAuthUserId(),
                'created_by_name' => auth()->guard('employee')->user() ? auth()->guard('employee')->user()->employee_name : getAuthUserName(),
            ]);

            // Simpan detail untuk setiap program
            foreach ($request->programs as $program) {
                // Ambil data program untuk mendapatkan hierarki
                $programOutput = DB::table('programs as p')
                    ->where('p.id', $program['program_output_id'])
                    ->select([
                        'p.*',
                        DB::raw('(SELECT id FROM programs WHERE id = p.top_parent_id) as top_parent_id'),
                        DB::raw('(SELECT program_code FROM programs WHERE id = p.top_parent_id) as top_parent_code'),
                        DB::raw('(SELECT program_name FROM programs WHERE id = p.top_parent_id) as top_parent_name'),
                        DB::raw('(SELECT id FROM programs WHERE id = p.parent_id) as parent_id'),
                        DB::raw('(SELECT program_code FROM programs WHERE id = p.parent_id) as parent_code'),
                        DB::raw('(SELECT program_name FROM programs WHERE id = p.parent_id) as parent_name'),
                    ])
                    ->first();

                foreach ($program['details'] as $detail) {
                    // Ambil data item
                    $item = Item::find($detail['asset_id']);
                    // Ambil data UOM
                    $uom = Uom::find($detail['satuan']);

                    // Buat maintenance plan detail
                    $planDetail = $maintenancePlan->maintenancePlanDetails()->create([
                        'program_id' => $program['program_output_id'],
                        'maintenance_category_id' => $detail['pemeliharaan'],
                        'item_id' => $item->id,
                        'code' => $item->item_code,
                        'name' => $item->item_name,
                        'uom_id' => $uom->id,
                        'uom_name' => $uom->uom_name,
                        'quantity' => $detail['qty'],
                        'condition' => $detail['kondisi'],
                    ]);

                    // Simpan program hierarchy
                    if ($programOutput) {
                        // Program Output
                        $planDetail->maintenancePlanDetailPrograms()->create([
                            'program_type' => 'OUTPUT',
                            'program_code' => $programOutput->program_code,
                            'program_name' => $programOutput->program_name,
                            'level' => 3
                        ]);

                        // Program Parent (Kegiatan)
                        if ($programOutput->parent_id) {
                            $planDetail->maintenancePlanDetailPrograms()->create([
                                'program_type' => 'GROUP',
                                'program_code' => $programOutput->parent_code,
                                'program_name' => $programOutput->parent_name,
                                'level' => 2
                            ]);
                        }

                        // Program Top Parent
                        if ($programOutput->top_parent_id) {
                            $planDetail->maintenancePlanDetailPrograms()->create([
                                'program_type' => 'GROUP',
                                'program_code' => $programOutput->top_parent_code,
                                'program_name' => $programOutput->top_parent_name,
                                'level' => 1
                            ]);
                        }
                    }
                }
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Data pemeliharaan berhasil disimpan',
                'data' => [
                    'id' => $maintenancePlan->id,
                    'maintenance_plan_number' => $maintenancePlan->maintenance_plan_number
                ]
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => 'Terjadi kesalahan saat menyimpan data: ' . $e->getMessage()
            ], 500);
        }
    }

    private function generateMaintenanceNumber()
    {
        $prefix = 'MAINT-' . date('Ym');
        $lastPlan = MaintenancePlan::where('maintenance_plan_number', 'like', $prefix . '%')
            ->orderBy('maintenance_plan_number', 'desc')
            ->first();

        if ($lastPlan) {
            $lastNumber = intval(substr($lastPlan->maintenance_plan_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . $newNumber;
    }

    function getProgramOutput(Request $request)
    {
        $programs = DB::table("programs as p")
            ->where("p.program_type", "OUTPUT")
            ->where("p.program_category", "PEMELIHARAAN")
            ->select([
                'p.*',
                DB::raw('(SELECT program_code FROM programs WHERE id = p.top_parent_id) as top_parent_code'),
                DB::raw('(SELECT program_name FROM programs WHERE id = p.top_parent_id) as top_parent_name'),
                DB::raw('(SELECT program_code FROM programs WHERE id = p.parent_id) as parent_code'),
                DB::raw('(SELECT program_name FROM programs WHERE id = p.parent_id) as parent_name')
            ])
            ->when($request->q, function ($query) use ($request) {
                return $query->where(function($q) use ($request) {
                    $q->where("p.program_name", "like", "%" . $request->q . "%")
                      ->orWhere("p.program_code", "like", "%" . $request->q . "%");
                });
            })
            ->paginate(25);

        return response()->json($programs);
    }

    function _dropdownProgram()
    {
        $programs = DB::table("programs")
            ->where(["program_category" => "PEMELIHARAAN", "top_parent_id" => 0])
            ->when(request("q"), function ($query) {
                return $query->where("program_name", "like", "%" . request("q") . "%");
            })
            ->paginate(25);

        return response()->json($programs);
    }

    function _dropdownProgramOutput()
    {
        $programs = DB::table("programs as p")
            ->where("p.program_type", "OUTPUT")
            ->where("p.program_category", "PEMELIHARAAN")
            ->select([
                'p.*',
                DB::raw('(SELECT program_code FROM programs WHERE id = p.top_parent_id) as top_parent_code'),
                DB::raw('(SELECT program_name FROM programs WHERE id = p.top_parent_id) as top_parent_name'),
                DB::raw('(SELECT program_code FROM programs WHERE id = p.parent_id) as parent_code'),
                DB::raw('(SELECT program_name FROM programs WHERE id = p.parent_id) as parent_name')
            ])
            ->when(request("q"), function ($query) {
                return $query->where("p.program_name", "like", "%" . request("q") . "%")
                    ->orWhere("p.program_code", "like", "%" . request("q") . "%");
            })
            ->paginate(25);

        return response()->json($programs);
    }

    function _dropdownPemeliharaan()
    {
        $pemeliharaan = DB::table("maintenance_categories")
            ->where("active", 1)
            ->when(request("q"), function ($query) {
                return $query->where("maintenance_category_name", "like", "%" . request("q") . "%");
            })
            ->paginate(25);

        return response()->json($pemeliharaan);
    }

    function show($id)
    {
        // if (!hasPermissionInGuard('Pemeliharaan - View')) {
            // abort(403, "Unauthorized action.");
        // }

        $maintenancePlan = MaintenancePlan::with([
            'room',
            'maintenancePlanDetails.item',
            'maintenancePlanDetails.uom',
            'maintenancePlanDetails.maintenanceCategory',
            'maintenancePlanDetails.maintenancePlanDetailPrograms' => function($query) {
                $query->orderBy('level', 'asc');
            }
        ])->findOrFail($id);

        if (request()->ajax()) {
            return view('planning.asset-maintenance.show-modal', compact('maintenancePlan'));
        }

        $title = "Detail Pemeliharaan";
        $breadcrumbs = ["Perencanaan", "Detail Pemeliharaan Barang"];

        return view('planning.asset-maintenance.show', compact('title', 'breadcrumbs', 'maintenancePlan'));
    }

    public function export($id)
    {
        if (!hasPermissionInGuard('Pemeliharaan - View')) {
            abort(403, "Unauthorized action.");
        }

        $maintenancePlan = MaintenancePlan::with([
            'room',
            'maintenancePlanDetails.item',
            'maintenancePlanDetails.uom',
            'maintenancePlanDetails.maintenanceCategory',
            'maintenancePlanDetails.maintenancePlanDetailPrograms' => function($query) {
                $query->orderBy('level', 'asc');
            }
        ])->findOrFail($id);

        return Excel::download(
            new MaintenancePlanExport($maintenancePlan),
            'Pemeliharaan_' . $maintenancePlan->maintenance_plan_number . '.xlsx'
        );
    }

    public function destroy($id)
    {
        if (!hasPermissionInGuard('Pemeliharaan - Action')) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized action.'
            ], 403);
        }

        try {
            DB::beginTransaction();

            $maintenancePlan = MaintenancePlan::with(['maintenancePlanDetails.maintenancePlanDetailPrograms'])
                ->where('approver_id', null)
                ->findOrFail($id);

            // Hapus maintenance_plan_detail_programs
            foreach ($maintenancePlan->maintenancePlanDetails as $detail) {
                $detail->maintenancePlanDetailPrograms()->delete();
            }

            // Hapus maintenance_plan_details
            $maintenancePlan->maintenancePlanDetails()->delete();

            // Hapus maintenance_plan
            $maintenancePlan->delete();

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Data pemeliharaan berhasil dihapus'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 'error',
                'message' => 'Terjadi kesalahan saat menghapus data: ' . $e->getMessage()
            ], 500);
        }
    }
}
