<?php

namespace App\Http\Controllers\Planning;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use App\Models\Room;
use App\Models\Division;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class ApprovalConsumableProcurementController extends Controller
{
    function index()
    {
        if (!hasPermissionInGuard('Approve Perencanaan - View')) {
            abort(403, "Unauthorized action.");
        }
        $title = "Approve Perencanaan";
        $breadcrumbs = ["Perencanaan", "Approve Perencanaan"];

        return view('planning.approval-consumable-procurement.index', compact('title', "breadcrumbs"));
    }

    function list(Request $request)
    {
        if (!hasPermissionInGuard('Approve Perencanaan - View')) {
            abort(403, "Unauthorized action.");
        }
        if ($request->ajax()) {
            $employeeId = auth()->guard('employee')->user()->id ?? null;
            $data = Plan::with(["program", "room"])
                ->filterApprove()
                ->when($employeeId && ($request->room && $request->room == 'all'), function ($query) use ($request, $employeeId) {
                    $listDivisionIds = Division::select('divisions.id')
                        ->join('rooms', 'rooms.division_id', '=', 'divisions.id')
                        ->where('rooms.pic_room', $employeeId)
                        ->pluck('divisions.id');
                    $listRoomId = Room::whereIn('division_id', $listDivisionIds)->pluck('id');

                    return $query->whereIn('room_id', $listRoomId);
                })
                ->when($request->year, function ($query) use ($request) {
                    return $query->whereYear('request_date', $request->year);
                })
                ->when($request->room && $request->room != 'all', function ($query) use ($request) {
                    return $query->where('room_id', $request->room);
                })
                ->when($request->status && $request->status != 'all', function ($query) use ($request) {
                    if ($request->status === 'approved') {
                        return $query->whereNotNull('approval_date');
                    }
                    if ($request->status === 'notapproved') {
                        return $query->whereNull('approval_date');
                    }
                    return $query;
                })
                ->orderBy('id', 'DESC')
                ->get();

            return DataTables::of($data)
                ->addColumn('program', function ($row) {
                    return $row->program ? $row->program->program_name : '-';
                })
                ->addColumn('room', function ($row) {
                    return $row->room ? $row->room->room_code . ' - ' . $row->room->room_name : '-';
                })
                ->addColumn('request_date', function ($row) {
                    return $row->request_date ? date('d/m/Y', strtotime($row->request_date)) : '-';
                })
                ->addColumn('total_qty', function ($row) {
                    return number_format($row->total_plan_quantity, 0, ',', '.');
                })
                ->addColumn('action', function ($row) {
                    $btn = '<a href="#modal-dialog" data-route="' . route('planning.consumable-procurement.show', $row->id) . '" class="btn btn-sm btn-outline-info btn-show" data-bs-toggle="modal"><i class="fas fa-search"></i></a>';

                    if ($row->approval_date == null) {
                        $btn .= ' <button type="button" class="btn btn-sm btn-outline-warning btn-approve" data-route="' .  route('planning.approval-consumable-procurement.approve', $row->id) . '"><i class="fas fa-edit"></i></button>';
                    }
                    return $btn;
                })
                ->addColumn("status", function ($row) {
                    if ($row->approval_date != null) {
                        return '<span class="badge bg-success">Disetujui</span>';
                    } else {
                        return '<span class="badge bg-danger">Belum Disetujui</span>';
                    }
                })
                ->rawColumns(['action', 'status'])
                ->make(true);
        }
    }

    function show(Plan $plan)
    {
        return view("components.planning.consumable-procurement", ["plan" => $plan]);
    }

    function approve(Plan $Plan)
    {
        if (!hasPermissionInGuard('Approve Perencanaan - Action')) {
            abort(403, "Unauthorized action.");
        }
        try {
            DB::beginTransaction();

            $Plan->update([
                "approver_id" => getAuthUserId(),
                "approver_name" => getAuthUserName(),
                "approval_date" => now(),
            ]);

            DB::commit();
            return response()->json(["message" => "Berhasil disetujui"], 200);
        } catch (\Throwable $th) {
            DB::rollBack();
            return response()->json(["message" => $th->getMessage()], 500);
        }
    }
}
