<?php

namespace App\Http\Resources\Planning;

use App\Models\Program;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReportMaintenanceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $programId = Program::where(["parent_id" => $this->id, "top_parent_id" => $this->id])->pluck("id");

        return [
            "id" => $this->id,
            "program_name" => $this->program_name,
            "programs" => Program::whereIn("id", $programId)->get(),
            "outputs" => Program::whereIn("parent_id", $programId)->get(),
        ];
    }
}
