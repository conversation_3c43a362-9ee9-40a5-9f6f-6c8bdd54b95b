<?php

namespace App\DataTables;

use App\Models\RegisterAsset;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Ya<PERSON>ra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Html\Editor\Editor;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Services\DataTable;

class AssetDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param QueryBuilder $query Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->editColumn('created_at', function ($row) {
                return $row->created_at->format('d/m/Y H:i:s');
            })
            ->addColumn("checkbox", function ($row) {
                return '<input type="checkbox" name="id[]" class="form-check-input checkbox-id" value="' . $row->id . '">';
            })
            ->addColumn("nama_barang", function ($row) {
                return $row->asset->barang->nama_barang;
            })
            ->addColumn("kode_barang", function ($row) {
                return $row->asset->barang->kode_barang;
            })
            ->addColumn("nama_ruangan", function ($row) {
                return $row->ruangan_id != 0 ? $row->ruangan->nama_ruangan : "<span class='text-danger'>Pilih Peruntukan</span>";
            })
            ->addColumn('action', function ($row) {
                return '<a href="' . route("asset.edit", $row->id) . '" class="text-warning fs-14px"><i class="fas fa-edit"></i></a> <a href="javascript:;" class="text-danger fs-14px btn-delete" data-route="' . route("asset.destroy", $row->id) . '"><i class="fas fa-trash"></i></a>';
            })
            ->rawColumns(["action", "checkbox", "nama_ruangan"])
            ->setRowId('id');
    }

    /**
     * Get the query source of dataTable.
     */
    public function query(RegisterAsset $model): QueryBuilder
    {
        return $model->newQuery()
            ->latest("created_at");
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('datatable')
            ->columns($this->getColumns())
            ->minifiedAjax()
            //->dom('Bfrtip')
            ->orderBy(1)
            ->selectStyleSingle()
            ->buttons([
                Button::make('excel'),
                Button::make('csv'),
                Button::make('pdf'),
                Button::make('print'),
                Button::make('reset'),
                Button::make('reload')
            ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        $checkbox = '<input type="checkbox" name="" id="check-all" class="form-check-input">';

        return [
            Column::make('checkbox')
                ->title($checkbox)
                ->addClass("text-center")
                ->width(50)
                ->searchable(false)
                ->orderable(false),
            Column::make('nama_barang'),
            Column::make('kode_barang'),
            Column::make('register_code'),
            Column::make('qr_code'),
            Column::make('tag_rfid'),
            Column::make('nama_ruangan')
                ->title("Peruntukan"),
            Column::make('created_at'),
            Column::computed('action')
                ->exportable(false)
                ->printable(false)
                ->width(50)
                ->addClass('text-center'),
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'Asset_' . date('YmdHis');
    }
}
