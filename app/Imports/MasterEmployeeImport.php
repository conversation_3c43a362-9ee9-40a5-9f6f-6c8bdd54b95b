<?php

namespace App\Imports;

use App\Models\Employee;
use App\Models\Role;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class MasterEmployeeImport implements ToModel, WithHeadingRow, WithValidation, WithBatchInserts
{
    use Importable;

    public $successCount = 0;
    private $batchEmployeeCodes = [];

    public function model(array $row)
    {
        // Skip empty rows
        if (empty(array_filter($row))) {
            return null;
        }

        $this->successCount++;

        // Find role by role_code if provided
        $roleId = null;
        if (!empty($row['role_code'])) {
            $role = Role::where('role_code', $row['role_code'])->first();
            $roleId = $role ? $role->id : null;
        }

        return new Employee([
            'employee_identification_number' => $row['kode_karyawan'],
            'employee_name' => $row['nama_karyawan'],
            'employee_grade' => $row['golongan_karyawan'],
            'employee_position' => $row['jabatan_karyawan'],
            'pic_type' => $row['tipe_karyawan'],
            'category' => $row['kategori'],
            'role_id' => $roleId,
            'active' => 1, // Semua employee otomatis aktif
            'created_by' => getAuthUserId()
        ]);
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function rules(): array
    {
        return [
            '*.kode_karyawan' => [
                'required',
                'string',
                'max:32',
                Rule::unique('employees', 'employee_identification_number'),
                function ($attribute, $value, $fail) {
                    // Check for duplicates within the current batch
                    if (in_array($value, $this->batchEmployeeCodes)) {
                        $fail('Kode karyawan duplikat ditemukan dalam file yang sama.');
                    } else {
                        // Add to batch tracking array
                        $this->batchEmployeeCodes[] = $value;
                    }
                }
            ],
            '*.nama_karyawan' => [
                'required',
                'string',
                'max:255'
            ],
            '*.golongan' => [
                'nullable',
                'string',
                'max:32'
            ],
            '*.jabatan' => [
                'nullable',
                'string',
                'max:128'
            ],
            '*.tipe' => [
                'nullable',
                'string',
                'max:64'
            ],
            '*.kategori' => [
                'nullable',
                'string',
                'max:64'
            ],
            '*.role_code' => [
                'nullable',
                'string',
                'exists:roles,role_code'
            ]
        ];
    }

    public function customValidationMessages()
    {
        return [
            '*.kode_karyawan.required' => 'Kode karyawan wajib diisi.',
            '*.kode_karyawan.string' => 'Kode karyawan harus berupa teks.',
            '*.kode_karyawan.max' => 'Kode karyawan maksimal 32 karakter.',
            '*.kode_karyawan.unique' => 'Kode karyawan sudah terdaftar di database.',
            '*.nama_karyawan.required' => 'Nama karyawan wajib diisi.',
            '*.nama_karyawan.string' => 'Nama karyawan harus berupa teks.',
            '*.nama_karyawan.max' => 'Nama karyawan maksimal 255 karakter.',
            '*.golongan.string' => 'Golongan harus berupa teks.',
            '*.golongan.max' => 'Golongan maksimal 32 karakter.',
            '*.jabatan.string' => 'Jabatan harus berupa teks.',
            '*.jabatan.max' => 'Jabatan maksimal 128 karakter.',
            '*.tipe.string' => 'Tipe harus berupa teks.',
            '*.tipe.max' => 'Tipe maksimal 64 karakter.',
            '*.kategori.string' => 'Kategori harus berupa teks.',
            '*.kategori.max' => 'Kategori maksimal 64 karakter.',
            '*.role_code.string' => 'Role code harus berupa teks.',
            '*.role_code.exists' => 'Role code tidak ditemukan dalam database.'
        ];
    }
}
