<?php

namespace App\Imports;

use App\Models\Room;
use App\Models\RoomCategory;
use App\Models\Division;
use App\Models\Employee;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class RoomImport implements ToModel, WithHeadingRow, WithValidation, WithBatchInserts
{
    use Importable;

    public $successCount = 0;
    private $batchRoomCodes = [];

    public function model(array $row)
    {
        // Skip empty rows
        if (empty(array_filter($row))) {
            return null;
        }

        $this->successCount++;

        // Find room category by room_category_code
        $roomCategory = null;
        if (!empty($row['kategori_ruangan'])) {
            $category = RoomCategory::where('room_category_code', $row['kategori_ruangan'])->first();
            $roomCategory = $category ? $category->id : null;
        }

        // Find division by id
        $divisionId = null;
        if (!empty($row['divisi'])) {
            $division = Division::find($row['divisi']);
            $divisionId = $division ? $division->id : null;
        }

        // Find PIC by employee_identification_number
        $picRoom = null;
        if (!empty($row['penanggung_jawab'])) {
            $employee = Employee::where('employee_identification_number', $row['penanggung_jawab'])->first();
            $picRoom = $employee ? $employee->id : null;
        }

        return new Room([
            'room_code' => $row['kode_ruangan'],
            'room_name' => $row['nama_ruangan'],
            'building_name' => $row['nama_gedung'],
            'room_category' => $roomCategory,
            'pic_room' => $picRoom,
            'division_id' => $divisionId,
            'active' => 1,
            'created_by' => getAuthUserId()
        ]);
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function rules(): array
    {
        return [
            '*.kode_ruangan' => [
                'required',
                'string',
                'max:32',
                Rule::unique('rooms', 'room_code'),
                function ($attribute, $value, $fail) {
                    // Check for duplicates within the current batch
                    if (in_array($value, $this->batchRoomCodes)) {
                        $fail('Kode ruangan duplikat ditemukan dalam file yang sama.');
                    } else {
                        // Add to batch tracking array
                        $this->batchRoomCodes[] = $value;
                    }
                }
            ],
            '*.nama_ruangan' => [
                'required',
                'string',
                'max:255'
            ],
            '*.nama_gedung' => [
                'required',
                'string',
                'max:255'
            ],
            '*.kategori_ruangan' => [
                'nullable',
                'string',
                'exists:room_categories,room_category_code'
            ],
            '*.penanggung_jawab' => [
                'nullable',
                'string',
                'exists:employees,employee_identification_number'
            ],
            '*.divisi' => [
                'nullable',
                'integer',
                'exists:divisions,id'
            ]
        ];
    }

    public function customValidationMessages()
    {
        return [
            '*.kode_ruangan.required' => 'Kode ruangan wajib diisi.',
            '*.kode_ruangan.string' => 'Kode ruangan harus berupa teks.',
            '*.kode_ruangan.max' => 'Kode ruangan maksimal 32 karakter.',
            '*.kode_ruangan.unique' => 'Kode ruangan sudah terdaftar di database.',
            '*.nama_ruangan.required' => 'Nama ruangan wajib diisi.',
            '*.nama_ruangan.string' => 'Nama ruangan harus berupa teks.',
            '*.nama_ruangan.max' => 'Nama ruangan maksimal 255 karakter.',
            '*.nama_gedung.required' => 'Nama gedung wajib diisi.',
            '*.nama_gedung.string' => 'Nama gedung harus berupa teks.',
            '*.nama_gedung.max' => 'Nama gedung maksimal 255 karakter.',
            '*.kategori_ruangan.string' => 'Kategori ruangan harus berupa teks.',
            '*.kategori_ruangan.exists' => 'Kategori ruangan tidak ditemukan dalam database.',
            '*.penanggung_jawab.string' => 'Penanggung jawab harus berupa teks.',
            '*.penanggung_jawab.exists' => 'Penanggung jawab tidak ditemukan dalam database.',
            '*.divisi.integer' => 'Divisi harus berupa angka.',
            '*.divisi.exists' => 'Divisi tidak ditemukan dalam database.'
        ];
    }
}
