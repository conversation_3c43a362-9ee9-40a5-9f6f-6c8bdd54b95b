<?php

namespace App\Imports;

use App\Models\SiapBmdProgram;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Validators\Failure;
use Maatwebsite\Excel\Validators\ValidationException;

class SiapBmdProgramImport implements ToCollection, WithHeadingRow, SkipsEmptyRows
{
    public int $successCount = 0;
    public array $errors = [];

    private array $batchCodes = [];
    private array $parentMapping = [];
    private array $batchBranchCodes = [];

    public function collection(Collection $rows): void
    {
        DB::beginTransaction();

        try {
            Log::info('SiapBmdProgramImport: Starting import process', [
                'total_rows' => $rows->count(),
                'user_id' => getAuthUserId()
            ]);

            $validRows = $this->filterAndPrepareRows($rows);

            if (empty($validRows)) {
                Log::warning('SiapBmdProgramImport: No valid rows found');
                DB::rollBack();

                $failures = [];
                foreach ($this->errors as $error) {
                    $errors = [$error['error']];
                    $failures[] = new Failure(
                        $error['row'],
                        $error['attribute'],
                        $errors,
                        $error['data']
                    );
                }

                throw new ValidationException(
                    \Illuminate\Validation\ValidationException::withMessages($this->formatErrorsForValidationException()),
                    $failures
                );
            }

            $this->insertRecordsFirstPass($validRows);
            $this->resolveParentRelationships($validRows);

            DB::commit();

            Log::info('SiapBmdProgramImport: Import completed successfully', [
                'success_count' => $this->successCount,
                'error_count' => count($this->errors)
            ]);
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('SiapBmdProgramImport: Import failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    private function filterAndPrepareRows(Collection $rows): array
    {
        $validRows = [];
        $rowIndex = 2;

        foreach ($rows as $row) {
            $row = $this->pickField($row);

            $validationErrors = $this->validateRow($row->toArray(), $rowIndex);
            if (!empty($validationErrors)) {
                $this->errors = array_merge($this->errors, $validationErrors);
                $rowIndex++;
                continue;
            }

            if (in_array($row['kode'], $this->batchCodes)) {
                $this->errors[] = [
                    'row' => $rowIndex,
                    'attribute' => 'kode',
                    'error' => 'Kode duplikat ditemukan dalam file import: ' . $row['kode'],
                    'data' => $row->toArray()
                ];
                $rowIndex++;
                continue;
            }

            if (SiapBmdProgram::where('program_code', $row['kode'])->exists()) {
                $this->errors[] = [
                    'row' => $rowIndex,
                    'attribute' => 'kode',
                    'error' => 'Kode sudah terdaftar di database: ' . $row['kode'],
                    'data' => $row->toArray()
                ];
                $rowIndex++;
                continue;
            }

            $this->batchCodes[] = $row['kode'];

            if (!empty($row['parent'])) {
                $this->parentMapping[$row['kode']] = $row['parent'];
            }

            if ($row['tree'] === 'BRANCH') {
                $this->batchBranchCodes[] = $row['kode'];
            }

            $validRows[] = [
                'row_index' => $rowIndex,
                'data' => $row->toArray()
            ];

            $rowIndex++;
        }

        return $validRows;
    }

    private function pickField(Collection $row): Collection
    {
        return $row->only(['kode', 'nama', 'type', 'parent', 'tree']);
    }

    private function validateRow(array $rowData, int $rowIndex): array
    {
        $errors = [];

        if (empty($rowData['kode'])) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'kode',
                'error' => 'Kode program wajib diisi.',
                'data' => $rowData
            ];
        } elseif (!is_string($rowData['kode']) || strlen($rowData['kode']) > 50) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'kode',
                'error' => 'Kode program harus berupa teks maksimal 50 karakter.',
                'data' => $rowData
            ];
        }

        if (empty($rowData['nama'])) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'nama',
                'error' => 'Nama program wajib diisi.',
                'data' => $rowData
            ];
        } elseif (!is_string($rowData['nama']) || strlen($rowData['nama']) > 255) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'nama',
                'error' => 'Nama program harus berupa teks maksimal 255 karakter.',
                'data' => $rowData
            ];
        }

        if (empty($rowData['type'])) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'type',
                'error' => 'Tipe program wajib diisi.',
                'data' => $rowData
            ];
        } elseif (!in_array($rowData['type'], ['PROGRAM', 'ACTIVITY', 'SUB_ACTIVITY'])) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'type',
                'error' => 'Tipe program harus salah satu dari: PROGRAM, ACTIVITY, SUB_ACTIVITY.',
                'data' => $rowData
            ];
        }

        if (empty($rowData['tree'])) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'tree',
                'error' => 'Tipe program wajib diisi.',
                'data' => $rowData
            ];
        } elseif (!in_array($rowData['tree'], ['BRANCH', 'LEAF'])) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'tree',
                'error' => 'Tipe program harus salah satu dari: BRANCH, LEAF.',
                'data' => $rowData
            ];
        }

        if (!empty($rowData['parent'])) {
            if (!is_string($rowData['parent']) || strlen($rowData['parent']) > 50) {
                $errors[] = [
                    'row' => $rowIndex,
                    'attribute' => 'parent',
                    'error' => 'Kode parent harus berupa teks maksimal 50 karakter.',
                    'data' => $rowData
                ];
            } else {
                $parentExists = SiapBmdProgram::where('program_code', $rowData['parent'])
                    ->where('tree', 'BRANCH')
                    ->exists() || in_array($rowData['parent'], $this->batchBranchCodes);

                if (!$parentExists) {
                    $errors[] = [
                        'row' => $rowIndex,
                        'attribute' => 'parent',
                        'error' => 'Kode parent tidak ditemukan.',
                        'data' => $rowData
                    ];
                }
            }
        }

        if (($rowData['tree'] ?? null) === 'LEAF' && empty($rowData['parent'])) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'parent',
                'error' => 'Parent wajib diisi untuk tipe LEAF.',
                'data' => $rowData
            ];
        }

        return $errors;
    }

    private function insertRecordsFirstPass(array $validRows): void
    {
        $dataToInsert = [];
        $now = now();
        $userId = getAuthUserId();

        foreach ($validRows as $rowData) {
            $row = $rowData['data'];

            $dataToInsert[] = [
                'program_code' => $row['kode'],
                'program_name' => $row['nama'],
                'type' => $row['type'],
                'parent_id' => null,
                'tree' => $row['tree'],
                'created_by' => $userId,
                'created_by_name' => Auth()->user()->name ?? '',
                'created_at' => $now,
                'updated_at' => $now
            ];

            $this->successCount++;
        }

        if (!empty($dataToInsert)) {
            SiapBmdProgram::insert($dataToInsert);

            Log::info('SiapBmdProgramImport: Phase 1 completed', [
                'inserted_count' => count($dataToInsert)
            ]);
        }
    }

    private function resolveParentRelationships(array $validRows): void
    {
        if (empty($this->parentMapping)) {
            Log::info('SiapBmdProgramImport: No parent relationships to resolve');
            return;
        }

        $insertedRecords = SiapBmdProgram::whereIn('program_code', $this->batchCodes)
            ->get()
            ->keyBy('program_code');

        $allParentCodes = array_values($this->parentMapping);
        $parentRecords = SiapBmdProgram::whereIn('program_code', $allParentCodes)
            ->get()
            ->keyBy('program_code');

        $updateBatch = [];

        foreach ($this->parentMapping as $childCode => $parentCode) {
            if (!isset($insertedRecords[$childCode])) {
                continue;
            }

            if (!isset($parentRecords[$parentCode])) {
                continue;
            }

            $updateBatch[] = [
                'id' => $insertedRecords[$childCode]->id,
                'parent_id' => $parentRecords[$parentCode]->id
            ];
        }

        if (!empty($updateBatch)) {
            $cases = [];
            $ids = [];

            foreach ($updateBatch as $update) {
                $id = (int) $update['id'];
                $parentId = (int) $update['parent_id'];
                $cases[$id] = "WHEN {$id} THEN {$parentId}";
                $ids[] = $id;
            }

            $whenClauses = implode(' ', $cases);
            $idsList = implode(',', $ids);

            DB::update("
                UPDATE siap_bmd_programs
                SET parent_id = CASE id {$whenClauses} END
                WHERE id IN ({$idsList})
            ");

            Log::info('SiapBmdProgramImport: Phase 2 completed', [
                'updated_count' => count($updateBatch)
            ]);
        }
    }

    private function formatErrorsForValidationException(): array
    {
        $formattedErrors = [];

        foreach ($this->errors as $error) {
            $key = $error['attribute'] ?? 'unknown';
            $formattedErrors[$key][] = $error['error'] ?? 'Unknown error';
        }

        return $formattedErrors;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getSuccessCount(): int
    {
        return $this->successCount;
    }
}

