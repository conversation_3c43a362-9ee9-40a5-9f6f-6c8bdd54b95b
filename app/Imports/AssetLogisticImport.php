<?php

namespace App\Imports;

use App\Models\AssetEntry;
use App\Models\ConfigStockField;
use App\Models\ConfigStockRecap;
use App\Models\Item;
use App\Models\Uom;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class AssetLogisticImport implements ToModel, WithHeadingRow
{
    function model(array $row)
    {
        $item = Item::firstWhere('item_code', $row["kode_barang"]);
        if (!$item) {
            throw new \Exception("{$row['kode_barang']} : Data item tidak ditemukan");
        }

        $uom = Uom::firstWhere('uom_name', $row["satuan"]);
        if (!$uom) {
            throw new \Exception("{$row['satuan']} : Data satuan tidak ditemukan");
        }

        $stokRecap = ConfigStockRecap::firstWhere("name", $row["stok_rekapitulasi"]);
        if (!$stokRecap) {
            throw new \Exception("{$row['stok_rekapitulasi']} : Data stok recapitulasi tidak ditemukan");
        }
        $stokField = ConfigStockField::firstWhere("name", $row["bidang_permintaan"]);
        if (!$stokField) {
            throw new \Exception("{$row['bidang_permintaan']} : Data bidang permintaan tidak ditemukan");
        }
        $assetEntryCode = $item->item_code . "." . $row["kode_register_logistik"];
        $assetEntry = AssetEntry::where("asset_entry_code", $assetEntryCode)->first();

        if (!$assetEntry) {
            return new AssetEntry([
                "item_id" => $item->id,
                "item_name" => $item->item_name,
                "category_type" => "LOGISTIC",
                "category_id" => $item->category_id,
                "config_stock_recapitulation_id" => $stokRecap->id,
                "config_stock_field_id" => $stokField->id,
                "uom_id" => $uom->id,
                "uom_name" => $uom->uom_name,
                "asset_name" => $row["nama_aset_logistik"],
                "asset_entry_code" => $assetEntryCode,
                "created_by" => getAuthUserId(),
                "created_by_name" => getAuthUserName(),
            ]);
        }
    }
}
