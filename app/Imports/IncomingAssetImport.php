<?php

namespace App\Imports;

use App\Models\Asset;
use App\Models\AssetEntry;
use App\Models\Logistic;
use App\Models\LogisticDetail;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;

class IncomingAssetImport implements ToCollection
{
    protected $date;

    function __construct($date)
    {
        $this->date = $date;
    }

    function collection(Collection $rows)
    {
        $rowIndex = 1;
        $rows->shift();

        DB::beginTransaction();
        try {
            $assetEntryIds = [];
            $totalQty = 0;
            $totalPrice = 0;

            foreach ($rows as $row) {
                $rowIndex++;
                // Log::info($row);
                $assetEntry = AssetEntry::firstWhere("asset_entry_code", $row[1]);
                array_push($assetEntryIds, $assetEntry->id);
                $assetEntryId = $assetEntry->id;
                $price = $row[3];
                $qty = $row[4];
                $key = $assetEntryId . '-' . $price;

                if (!$qty) {
                    throw new \Exception("Baris no. {$rowIndex} : {$row[1]} qty masih kosong atau 0.");
                }

                if (!$price) {
                    throw new \Exception("Baris no. {$rowIndex} : {$row[1]} harga masih kosong atau 0.");
                }

                $totalQty += ($qty);
                $totalPrice += ($price * $qty);
                if (!isset($inputItem[$key])) {
                    $inputItem[$key] = [
                        'key' => $key,
                        'asset_entry_id' => $assetEntryId,
                        'price' => $price,
                        'qty' => $qty,
                    ];
                } else {
                    $inputItem[$key]['qty'] += $qty;
                }
            }
            $inputItem = array_values($inputItem);

            Log::info($inputItem);
            $assetsEntries = AssetEntry::whereIn("id", $assetEntryIds)->get()->keyBy('id')->toArray();

            // Create Logistic entry
            $logistic = Logistic::create([
                "logistic_type" => "IN",
                "logistic_number" => "LGC-" . date('ymd') . "." . random_int(10000000, 99999999),
                "logistic_notes" => "Saldo Awal",
                "logistic_date" => $this->date,
                "total_logistic_quantity" => $totalQty,
                "total_logistic_price" => $totalPrice,
                "created_by" => getAuthUserId(),
                "created_by_name" => getAuthUserName(),
                "created_at" => date('Y-m-d H:i:s')
            ]);

            foreach ($inputItem as $item) {
                $dataAsset = Asset::where('asset_entry_id', $item['asset_entry_id'])->where('unit_price', $item['price'])->first();
                $assetId = null;
                $uomId = null;
                $uomName = null;
                if (isset($dataAsset)) {
                    $assetId = $dataAsset->id;
                    $uomId = $dataAsset->uom_id;
                    $uomName = $dataAsset->uom_name;
                } else {
                    $latestSubRegisterCode = Asset::where('asset_entry_id', $item['asset_entry_id'])->orderBy('id', 'desc')->first();
                    $latestCode = ($latestSubRegisterCode) ? (int)$latestSubRegisterCode->sub_register_code : 0;
                    $newSubRegisterCode = str_pad($latestCode + 1, 2, '0', STR_PAD_LEFT);
                    $dataNewAsset = [
                        'category_type' => 'LOGISTIC',
                        'asset_entry_id' => $item['asset_entry_id'],
                        'item_id' => $assetsEntries[$item['asset_entry_id']]['item_id'],
                        'asset_code' => $assetsEntries[$item['asset_entry_id']]['asset_entry_code'] . "." . $newSubRegisterCode,
                        'register_code' => substr($assetsEntries[$item['asset_entry_id']]['asset_entry_code'], -3),
                        'sub_register_code' => $newSubRegisterCode,
                        'qr_code' => $assetsEntries[$item['asset_entry_id']]['asset_entry_code'] . "." . $newSubRegisterCode,
                        'unit_price' => $item['price'],
                        'uom_id' => $assetsEntries[$item['asset_entry_id']]['uom_id'],
                        'uom_name' => $assetsEntries[$item['asset_entry_id']]['uom_name'],
                        'asset_name' => $assetsEntries[$item['asset_entry_id']]['asset_name']
                    ];
                    $newAsset = Asset::create($dataNewAsset);
                    $assetId = $newAsset->id;
                    $uomId = $assetsEntries[$item['asset_entry_id']]['uom_id'];
                    $uomName = $assetsEntries[$item['asset_entry_id']]['uom_name'];
                }

                LogisticDetail::create([
                    "logistic_id" => $logistic->id,
                    "logistic_type" => "IN",
                    "asset_id" => $assetId,
                    "uom_id" => $uomId,
                    "uom_name" => $uomName,
                    "unit_price" => $item['price'],
                    "quantity" => $item['qty'],
                    "total_price" => ($item['price'] * $item['qty']),
                ]);

                $latestBalanceAssetEntry = AssetEntry::where('id', $item['asset_entry_id'])->value('latest_balance') ?? 0;
                AssetEntry::where('id', $item['asset_entry_id'])->update(['latest_balance' => ($latestBalanceAssetEntry + $item['qty'])]);

                $latestBalanceAsset = Asset::where('id', $assetId)->value('latest_balance') ?? 0;
                Asset::where('id', $assetId)->update(['latest_balance' => ($latestBalanceAsset + $item['qty'])]);
            }

            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
}
