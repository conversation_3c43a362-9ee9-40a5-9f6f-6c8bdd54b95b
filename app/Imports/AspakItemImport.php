<?php

namespace App\Imports;

use App\Models\AspakItem;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Validators\Failure;
use Maatwebsite\Excel\Validators\ValidationException;

/**
 * AspakItemImport - Optimized Excel Import for ASPAK Item Data
 *
 * This class handles the import of ASPAK Item data from Excel files with the following features:
 * - Two-phase import process to handle parent-child relationships
 * - Comprehensive error handling with detailed error collection
 * - Database transaction management with rollback capability
 * - Batch processing for optimal performance
 * - Memory optimization for large import files
 * - Validation with custom error messages
 * - Duplicate detection within the same batch
 *
 * Import Process:
 * 1. First Pass: Insert all records with parent_id set to null
 * 2. Second Pass: Resolve and update parent_id relationships based on parent codes
 *
 * Expected Excel columns: kode, nama, sinonim, parent, tree
 *
 * @package App\Imports
 * <AUTHOR>
 * @version 2.1 - Further Optimized Version
 */
class AspakItemImport implements ToCollection, WithHeadingRow, SkipsEmptyRows
{
    /**
     * Success counter for tracking processed records
     */
    public int $successCount = 0;

    /**
     * Error collection for comprehensive error reporting
     *
     * @var array
     */
    public array $errors = [];

    /**
     * Batch tracking for duplicate detection
     *
     * @var array
     */
    private array $batchCodes = [];

    /**
     * Parent mapping from import data
     *
     * @var array
     */
    private array $parentMapping = [];

    /**
     * List of branch codes in the current batch
     *
     * @var array
     */
    private array $batchBranchCodes = [];

    /**
     * Process the imported collection with optimized two-phase approach
     *
     * This method implements a two-phase import strategy:
     * Phase 1: Insert all records with parent_id as null to avoid foreign key constraints
     * Phase 2: Resolve and update parent_id relationships based on parent codes
     *
     * Features:
     * - Database transaction management with rollback capability
     * - Comprehensive error collection and handling
     * - Batch processing for optimal performance
     * - Memory optimization for large datasets
     * - Detailed logging for debugging purposes
     *
     * @param Collection $rows The imported data collection from Excel
     * @return void
     * @throws ValidationException When validation errors occur
     * @throws \Exception When critical errors occur during import
     */
    public function collection(Collection $rows): void
    {
        // Start database transaction for atomicity
        DB::beginTransaction();

        try {
            Log::info('AspakItemImport: Starting import process', [
                'total_rows' => $rows->count(),
                'user_id' => getAuthUserId()
            ]);

            // Filter out empty rows and prepare data
            $validRows = $this->filterAndPrepareRows($rows);

            if (empty($validRows)) {
                Log::warning('AspakItemImport: No valid rows found');
                DB::rollBack();

                $failures = [];
                foreach ($this->errors as $error) {
                    $errors = [$error['error']];
                    $failures[] = new Failure(
                        $error['row'],
                        $error['attribute'],
                        $errors, // Must be an array
                        $error['data']
                    );
                }

                throw new ValidationException(
                    \Illuminate\Validation\ValidationException::withMessages($this->formatErrorsForValidationException()),
                    $failures
                );
            }

            // Phase 1: Insert all records with parent_id = null
            $this->insertRecordsFirstPass($validRows);

            // Phase 2: Resolve and update parent_id relationships
            $this->resolveParentRelationships($validRows);

            DB::commit();

            Log::info('AspakItemImport: Import completed successfully', [
                'success_count' => $this->successCount,
                'error_count' => count($this->errors)
            ]);
        } catch (ValidationException $e) {
            // Re-throw ValidationException without rolling back as it's already rolled back
            throw $e;
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('AspakItemImport: Import failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Filter empty rows and prepare data for processing with comprehensive validation
     *
     * @param Collection $rows
     * @return array
     */
    private function filterAndPrepareRows(Collection $rows): array
    {
        $validRows = [];
        $rowIndex = 2; // Start from row 2 (accounting for header)

        foreach ($rows as $row) {
            $row = $this->pickField($row);

            // Comprehensive validation for each row
            $validationErrors = $this->validateRow($row->toArray(), $rowIndex);

            if (!empty($validationErrors)) {
                $this->errors = array_merge($this->errors, $validationErrors);
                $rowIndex++;
                continue;
            }

            // Check for duplicate codes within batch
            if (in_array($row['kode'], $this->batchCodes)) {
                $this->errors[] = [
                    'row' => $rowIndex,
                    'attribute' => 'kode',
                    'error' => 'Kode duplikat ditemukan dalam file import: ' . $row['kode'],
                    'data' => $row->toArray()
                ];
                $rowIndex++;
                continue;
            }

            // Check if code already exists in database (memory efficient single query)
            if (AspakItem::where('item_code', $row['kode'])->exists()) {
                $this->errors[] = [
                    'row' => $rowIndex,
                    'attribute' => 'kode',
                    'error' => 'Kode sudah terdaftar di database: ' . $row['kode'],
                    'data' => $row->toArray()
                ];
                $rowIndex++;
                continue;
            }

            $this->batchCodes[] = $row['kode'];

            // Store parent mapping for phase 2
            if (!empty($row['parent'])) {
                $this->parentMapping[$row['kode']] = $row['parent'];
            }

            // Track branch codes in batch
            if ($row['tree'] === 'BRANCH') {
                $this->batchBranchCodes[] = $row['kode'];
            }

            $validRows[] = [
                'row_index' => $rowIndex,
                'data' => $row->toArray()
            ];

            $rowIndex++;
        }

        return $validRows;
    }

    /**
     * Pick specific fields from the row data for insertion
     *
     * @param Collection $row The row data
     * @return Collection Picked fields for insertion
     */
    private function pickField(Collection $row): Collection
    {
        return $row->only(['kode', 'nama', 'sinonim', 'parent', 'tree']);
    }

    /**
     * Validate individual row data efficiently
     *
     * @param array $rowData
     * @param int $rowIndex
     * @return array Array of validation errors
     */
    private function validateRow(array $rowData, int $rowIndex): array
    {
        $errors = [];

        // Required field validation
        if (empty($rowData['kode'])) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'kode',
                'error' => 'Kode item wajib diisi.',
                'data' => $rowData
            ];
        } elseif (!is_string($rowData['kode']) || strlen($rowData['kode']) > 50) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'kode',
                'error' => 'Kode item harus berupa teks maksimal 50 karakter.',
                'data' => $rowData
            ];
        }

        if (empty($rowData['nama'])) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'nama',
                'error' => 'Nama item wajib diisi.',
                'data' => $rowData
            ];
        } elseif (!is_string($rowData['nama']) || strlen($rowData['nama']) > 255) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'nama',
                'error' => 'Nama item harus berupa teks maksimal 255 karakter.',
                'data' => $rowData
            ];
        }

        if (empty($rowData['tree'])) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'tree',
                'error' => 'Tipe item wajib diisi.',
                'data' => $rowData
            ];
        } elseif (!in_array($rowData['tree'], ['BRANCH', 'LEAF'])) {
            $errors[] = [
                'row' => $rowIndex,
                'attribute' => 'tree',
                'error' => 'Tipe item harus salah satu dari: BRANCH, LEAF.',
                'data' => $rowData
            ];
        }

        // Parent validation (optional but must be valid if provided)
        if (!empty($rowData['parent'])) {
            if (!is_string($rowData['parent']) || strlen($rowData['parent']) > 50) {
                $errors[] = [
                    'row' => $rowIndex,
                    'attribute' => 'parent',
                    'error' => 'Kode parent harus berupa teks maksimal 50 karakter.',
                    'data' => $rowData
                ];
            } else {
                // Check if parent code exists in database or in current batch (branch codes only)
                $parentExists = AspakItem::where('item_code', $rowData['parent'])
                    ->where('tree', 'BRANCH')
                    ->exists() || in_array($rowData['parent'], $this->batchBranchCodes);

                if (!$parentExists) {
                    $errors[] = [
                        'row' => $rowIndex,
                        'attribute' => 'parent',
                        'error' => 'Kode parent tidak ditemukan.',
                        'data' => $rowData
                    ];
                }
            }
        }

        return $errors;
    }

    /**
     * First Pass: Insert all records with parent_id set to null
     *
     * This method handles the initial insertion of all records without
     * establishing parent-child relationships to avoid foreign key constraint issues.
     *
     * Features:
     * - Batch insertion for performance optimization
     * - Duplicate detection within the same batch
     * - Individual error handling per record
     * - Memory-efficient processing
     *
     * @param array $validRows The validated data rows
     * @return void
     */
    private function insertRecordsFirstPass(array $validRows): void
    {
        $dataToInsert = [];
        $now = now();
        $userId = getAuthUserId();

        foreach ($validRows as $rowData) {
            $row = $rowData['data'];

            $dataToInsert[] = [
                'item_code' => $row['kode'],
                'item_name' => $row['nama'],
                'item_synonym' => $row['sinonim'] ?? null,
                'parent_id' => null, // Always null in first pass
                'tree' => $row['tree'],
                'created_by' => $userId,
                'created_at' => $now,
                'updated_at' => $now
            ];

            $this->successCount++;
        }

        // Batch insert for performance
        if (!empty($dataToInsert)) {
            AspakItem::insert($dataToInsert);

            Log::info('AspakItemImport: Phase 1 completed', [
                'inserted_count' => count($dataToInsert)
            ]);
        }
    }

    /**
     * Second Pass: Resolve and update parent-child relationships
     *
     * This method processes all imported records to establish proper
     * parent-child relationships based on the parent codes provided in the import data.
     *
     * Features:
     * - Handles cases where parent records appear later than children in import file
     * - Supports both existing and newly imported parent records
     * - Efficient bulk updates to minimize database queries
     *
     * @param array $validRows The validated data rows
     * @return void
     */
    private function resolveParentRelationships(array $validRows): void
    {
        if (empty($this->parentMapping)) {
            Log::info('AspakItemImport: No parent relationships to resolve');
            return;
        }

        // Get all inserted records for this batch
        $insertedRecords = AspakItem::whereIn('item_code', $this->batchCodes)
            ->get()
            ->keyBy('item_code');

        // Get all potential parent records (both from current batch and existing)
        $allParentCodes = array_values($this->parentMapping);
        $parentRecords = AspakItem::whereIn('item_code', $allParentCodes)
            ->get()
            ->keyBy('item_code');

        $updateBatch = [];

        foreach ($this->parentMapping as $childCode => $parentCode) {
            if (!isset($insertedRecords[$childCode])) {
                continue; // Child record not found (shouldn't happen)
            }

            if (!isset($parentRecords[$parentCode])) {
                // This shouldn't happen as we've validated in phase 1
                continue;
            }

            $updateBatch[] = [
                'id' => $insertedRecords[$childCode]->id,
                'parent_id' => $parentRecords[$parentCode]->id
            ];
        }

        // Batch update parent relationships
        if (!empty($updateBatch)) {
            // Use batch update for better performance
            $cases = [];
            $ids = [];
            $params = [];

            foreach ($updateBatch as $update) {
                $id = (int) $update['id'];
                $parentId = (int) $update['parent_id'];
                $cases[$id] = "WHEN {$id} THEN {$parentId}";
                $ids[] = $id;
                $params[] = $parentId;
            }

            $whenClauses = implode(' ', $cases);
            $idsList = implode(',', $ids);

            // Perform batch update
            DB::update("
                UPDATE aspak_items
                SET parent_id = CASE id {$whenClauses} END
                WHERE id IN ({$idsList})
            ");

            Log::info('AspakItemImport: Phase 2 completed', [
                'updated_count' => count($updateBatch)
            ]);
        }
    }

    /**
     * Format errors for ValidationException
     *
     * @return array
     */
    private function formatErrorsForValidationException(): array
    {
        $formattedErrors = [];

        foreach ($this->errors as $error) {
            $key = $error['attribute'] ?? 'unknown';
            $formattedErrors[$key][] = $error['error'] ?? 'Unknown error';
        }

        return $formattedErrors;
    }

    /**
     * Get all collected errors during the import process
     *
     * Returns an array of errors with detailed information including:
     * - Row numbers where errors occurred
     * - Specific error messages
     * - Error types (validation, database, etc.)
     *
     * @return array Array of error details with row numbers and messages
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get the total number of successfully imported records
     *
     * @return int Number of records successfully imported
     */
    public function getSuccessCount(): int
    {
        return $this->successCount;
    }
}
