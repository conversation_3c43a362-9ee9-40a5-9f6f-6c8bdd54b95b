<?php

namespace App\Imports;

use App\Models\Category;
use App\Models\Employee;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class MasterCategoryImport implements ToModel, WithHeadingRow, WithValidation, WithBatchInserts
{
    use Importable;

    public $successCount = 0;
    private $batchCategoryCodes = [];

    public function model(array $row)
    {
        // Skip empty rows
        if (empty(array_filter($row))) {
            return null;
        }

        $this->successCount++;

        // Find employee by employee_identification_number for pic_kategori
        $picKategori = null;
        if (isset($row['pic_kategori']) && $row['pic_kategori'] !== null && $row['pic_kategori'] !== '') {
            $employee = Employee::where('employee_identification_number', $row['pic_kategori'])->first();
            if (!$employee) {
                throw new \Exception('Employee dengan identification number "' . $row['pic_kategori'] . '" tidak ditemukan. Proses import dihentikan.');
            }
            $picKategori = $employee->id;
        }

        return new Category([
            'category_code' => $row['kode_kategori'],
            'category_name' => $row['nama_kategori'],
            'category_type' => $row['tipe_kategori'],
            'category_sub_type' => $row['sub_tipe_kategori'],
            'pic_category' => $picKategori,
            'created_by' => getAuthUserId()
        ]);
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function rules(): array
    {
        return [
            '*.kode_kategori' => [
                'required',
                'string',
                'max:32',
                Rule::unique('categories', 'category_code'),
                function ($attribute, $value, $fail) {
                    // Check for duplicates within the current batch
                    if (in_array($value, $this->batchCategoryCodes)) {
                        $fail('Kode kategori duplikat ditemukan dalam file yang sama.');
                    } else {
                        // Add to batch tracking array
                        $this->batchCategoryCodes[] = $value;
                    }
                }
            ],
            '*.nama_kategori' => [
                'required',
                'string',
                'max:128'
            ],
            '*.tipe_kategori' => [
                'required',
                'in:EQUIPMENT,LOGISTIC,INFRASTRUCTURE,OTHER'
            ],
            '*.sub_tipe_kategori' => [
                'required',
                'in:ALAT_KESEHATAN,NON_ALAT_KESEHATAN'
            ],
            '*.pic_kategori' => 'nullable'
        ];
    }

    public function customValidationMessages()
    {
        return [
            '*.kode_kategori.required' => 'Kode kategori wajib diisi.',
            '*.kode_kategori.string' => 'Kode kategori harus berupa teks.',
            '*.kode_kategori.max' => 'Kode kategori maksimal 32 karakter.',
            '*.kode_kategori.unique' => 'Kode kategori sudah terdaftar di database.',
            '*.nama_kategori.required' => 'Nama kategori wajib diisi.',
            '*.nama_kategori.string' => 'Nama kategori harus berupa teks.',
            '*.nama_kategori.max' => 'Nama kategori maksimal 128 karakter.',
            '*.tipe_kategori.required' => 'Tipe kategori wajib diisi.',
            '*.tipe_kategori.in' => 'Tipe kategori harus salah satu dari: EQUIPMENT, LOGISTIC, INFRASTRUCTURE, OTHER.',
            '*.sub_tipe_kategori.required' => 'Sub tipe kategori wajib diisi.',
            '*.sub_tipe_kategori.in' => 'Sub tipe kategori harus salah satu dari: ALAT_KESEHATAN, NON_ALAT_KESEHATAN.',
            '*.pic_kategori.max' => 'PIC kategori maksimal 255 karakter.'
        ];
    }
}
