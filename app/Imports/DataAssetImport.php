<?php

namespace App\Imports;

use App\Models\AssetEntry;
use App\Models\Category;
use App\Models\Distributor;
use App\Models\Item;
use App\Models\Room;
use App\Models\Uom;
use App\Models\Asset;
use Carbon\Carbon;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithStartRow;
use PhpOffice\PhpSpreadsheet\Shared\Date as SharedDate;

class DataAssetImport implements ToModel, WithStartRow
{
    protected $rowIndex = 0;
    protected $dataToSave = [];

    public function model(array $row)
    {
        $this->rowIndex++;
        if (isset($row[7]) && isset($row[8]) && isset($row[9])) {
            $item = Item::firstWhere("item_code", $row[7]);
            if ($item) {
                $category = Category::firstWhere("category_name", $row[10]);

                if (!$category) {
                    throw new \Exception("Baris ke {$this->rowIndex} : Kategori tidak ditemukan");
                }

                $distributor = Distributor::firstWhere("distributor_name", $row[6]);
                if (!$distributor) {
                    throw new \Exception("Baris ke {$this->rowIndex} : Distributor tidak ditemukan");
                }

                $uom = Uom::firstWhere("uom_name", $row[21]);
                if (!$uom) {
                    throw new \Exception("Baris ke {$this->rowIndex} : Satuan tidak ditemukan");
                }

                if (!isset($row[0])) {
                    throw new \Exception("Baris ke {$this->rowIndex} : Tidak ada tanggal barang masuk");
                }

                if (!isset($row[3])) {
                    throw new \Exception("Baris ke {$this->rowIndex} : Tidak ada tanggal pembayaran");
                }

                try {
                    DB::beginTransaction();

                    $assetEntry = AssetEntry::create([
                        "item_id" => $item->id,
                        "category_id" => $category->id,
                        "distributor_id" => $distributor->id,
                        "uom_id" => $uom->id,
                        "uom_name" => $uom->uom_name,
                        "received_date" => SharedDate::excelToDateTimeObject($row[0])->format("Y-m-d"),
                        // "payment_date"  => Carbon::createFromFormat("d-m-Y", $row[3])->format("Y-m-d"),
                        "payment_date"  => SharedDate::excelToDateTimeObject($row[3])->format("Y-m-d"),
                        "source_supply" => $row[5],
                        "item_name" => $item->item_name,
                        "brand" => trim($row[13]),
                        "material" => trim($row[18]),
                        "quantity" => trim($row[20]),
                        "unit_price" => trim($row[22]),
                        "total_price" => trim($row[23]),
                        "condition" => trim($row[24]),
                        "size" => trim($row[19]),
                        "asset_name" => trim($row[11]) ?? trim($row[12]),
                        "purchasing_account" => trim($row[1]),
                        "bast_contract_number" => trim($row[2]),
                        "bast_payment_number" => trim($row[4]),
                        "akd_number" => trim($row[16]),
                        "general_specifications" => trim($row[17]),
                        
                        "spending_account_code" => trim($row[33]),
                        "contract_form" => trim($row[36]),
                    ]);

                    $room = Room::firstWhere("room_code", trim($row[30]));

                    $qrCode = $item->item_code . "." . trim($row[8]) . "." . SharedDate::excelToDateTimeObject($row[0])->format("Ymd");
                    $existingAsset = Asset::firstWhere("qr_code", $qrCode);

                    if ($existingAsset) {
                        throw new \Exception("Error, baris ke {$this->rowIndex} - QR code {$qrCode} sudah ada");
                    }

                    // Create asset
                    $assetEntry->assets()->create([
                        "item_id" => $assetEntry->item_id,
                        "qr_code" => $qrCode,
                        "document_room_id" => $room ? $room->id : null,
                        "register_code" => trim($row[8]),
                        "serial_number" => trim($row[15]),
                        "asset_code" => $item->item_code . "." . trim($row[8]),
                        "unit_price" => $assetEntry->unit_price,
                        "uom_id" => $assetEntry->uom_id,
                        "uom_name" => $assetEntry->uom_name,
                        "asset_name" => $assetEntry->asset_name,
                        "asset_condition" => strtoupper(trim($row[24])),

                        "sub_activity_code" => trim($row[34]),
                        "sub_activity_name" => trim($row[35]),
                        "electricity" => trim($row[37]),
                    ]);

                    DB::commit();
                } catch (\Throwable $th) {
                    DB::rollBack();
                    throw new \Exception($th);
                }
            } else {
                throw new \Exception("Error, baris ke {$this->rowIndex} - Item tidak ditemukan");
            }
        } else {
            throw new \Exception("Error, file tidak ditemukan");
        }
    }

    public function startRow(): int
    {
        return 2;
    }

    // public function chunkSize(): int
    // {
    //     return 1000;
    // }

    // public function batchSize(): int
    // {
    //     return 100;
    // }
}
