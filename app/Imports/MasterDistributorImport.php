<?php

namespace App\Imports;

use App\Models\Distributor;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class MasterDistributorImport implements ToModel, WithHeadingRow, WithValidation, WithBatchInserts
{
    use Importable;

    public $successCount = 0;
    private $batchDistributorNames = [];

    public function model(array $row)
    {
        // Skip empty rows
        if (empty(array_filter($row))) {
            return null;
        }

        $this->successCount++;

        return new Distributor([
            'distributor_name' => $row['nama_distributor'],
            'distributor_address' => $row['alamat'] ?? null,
            'distributor_phone' => $row['telepon'] ?? null,
            'distributor_email' => $row['email'] ?? null,
            'created_by' => getAuthUserId()
        ]);
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function rules(): array
    {
        return [
            '*.nama_distributor' => [
                'required',
                'string',
                'max:255',
                Rule::unique('distributors', 'distributor_name'),
                function ($attribute, $value, $fail) {
                    // Check for duplicates within the current batch
                    if (in_array($value, $this->batchDistributorNames)) {
                        $fail('Nama distributor duplikat ditemukan dalam file yang sama.');
                    } else {
                        // Add to batch tracking array
                        $this->batchDistributorNames[] = $value;
                    }
                }
            ],
            '*.alamat' => 'nullable|string|max:255',
            '*.telepon' => 'nullable|string|max:20',
            '*.email' => 'nullable|email|max:100'
        ];
    }

    public function customValidationMessages()
    {
        return [
            '*.nama_distributor.required' => 'Nama distributor wajib diisi.',
            '*.nama_distributor.string' => 'Nama distributor harus berupa teks.',
            '*.nama_distributor.max' => 'Nama distributor maksimal 255 karakter.',
            '*.nama_distributor.unique' => 'Nama distributor sudah terdaftar di database.',
            '*.alamat.string' => 'Alamat harus berupa teks.',
            '*.alamat.max' => 'Alamat maksimal 255 karakter.',
            '*.telepon.string' => 'Telepon harus berupa teks.',
            '*.telepon.max' => 'Telepon maksimal 20 karakter.',
            '*.email.email' => 'Format email tidak valid.',
            '*.email.max' => 'Email maksimal 100 karakter.'
        ];
    }
}