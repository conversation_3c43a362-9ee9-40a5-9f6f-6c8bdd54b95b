<?php

namespace App\Enums;

enum RepairRequestStatus: string
{
    case OPEN = 'Menunggu Diproses'; // REQUESTER
    case ACCEPTED = 'Request Diterima'; // IPFRS
    case ASSIGNED = 'Penugasan Petugas'; // IPFRS
    case INSPECTION = 'Petugas Melakukan Pengecekan'; // PETUGAS
    case ASSIGN_FIXED = 'Petugas Menyelesaikan Tugas'; // PETUGAS
    case ASSIGN_NOT_FIXED = 'Petugas Tidak Dapat Menyelesaikan Tugas'; // PETUGAS
    case REQUEST_BHP = 'Petugas Merequest BHP'; // PETUGAS
    case ACCEPTED_BHP = 'BHP Telah Disediakan'; // IPFRS
    case VENDOR_CALLED = 'Panggil Vendor'; // IPFRS
    case VENDOR_FIXED = 'Vendor Menyelesaikan Tugas'; // PETUGAS
    case VENDOR_NOT_FIXED = 'Vendor Tidak Dapat Menyelesaikan Tugas'; // PETUGAS
    case FIXED = 'Sudah Diperbaiki'; // IPFRS
    case NOT_FIXED = 'Tidak Diperbaiki'; // IPFRS

    /*
        kondisi normal
        = OPEN -> ACCEPTED -> ASSIGNED -> INSPECTION -> ASSIGN_FIXED(plus bhp) -> FIXED

        kondisi butuh bhp
        = OPEN -> ACCEPTED -> ASSIGNED -> INSPECTION -> REQUEST_BHP -> ACCEPTED_BHP -> ASSIGN_FIXED(plus bhp) -> FIXED

        kondisi vendor sukses
        = OPEN -> ACCEPTED -> ASSIGNED -> INSPECTION -> ASSIGN_NOT_FIXED -> VENDOR_CALLED -> VENDOR_FIXED -> FIXED

        kondisi vendor gagal
        = OPEN -> ACCEPTED -> ASSIGNED -> INSPECTION -> ASSIGN_NOT_FIXED -> VENDOR_CALLED -> VENDOR_NOT_FIXED -> NOT_FIXED
    */


    /**
     * Mapping case enum.
     *
     * @param string $shortName status (e.g., 'OPEN', 'ACCEPTED')
     * @return self|null Enum case
     */
    public static function fromShortName(string $shortName): ?self
    {
        return match ($shortName) {
            'OPEN' => self::OPEN,
            'ACCEPTED' => self::ACCEPTED,
            'ASSIGNED' => self::ASSIGNED,
            'INSPECTION' => self::INSPECTION,
            'ASSIGN_FIXED' => self::ASSIGN_FIXED,
            'ASSIGN_NOT_FIXED' => self::ASSIGN_NOT_FIXED,
            'REQUEST_BHP' => self::REQUEST_BHP,
            'ACCEPTED_BHP' => self::ACCEPTED_BHP,
            'VENDOR_CALLED' => self::VENDOR_CALLED,
            'VENDOR_FIXED' => self::VENDOR_FIXED,
            'VENDOR_NOT_FIXED' => self::VENDOR_NOT_FIXED,
            'FIXED' => self::FIXED,
            'NOT_FIXED' => self::NOT_FIXED,

            default => null,
        };
    }
}
