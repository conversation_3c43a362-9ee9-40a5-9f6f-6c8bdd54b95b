<?php

namespace App\Exports;

use App\Models\Employee;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class KartuStockExport implements FromCollection, WithTitle, WithStyles
{
    protected $summary;
    protected $details;
    protected $params;

    public function __construct($summary, $details, $params)
    {
        $this->summary = $summary;
        $this->details = $details;
        $this->params = $params;
    }

    public function collection()
    {
        return new Collection();
    }

    public function title(): string
    {
        return 'Kartu Stock';
    }

    public function styles(Worksheet $sheet)
    {
        // Header laporan
        $sheet->setCellValue('A1', 'Laporan Dibuat: ' . date('Y-m-d H:i:s'));
        $sheet->setCellValue('A3', config('app.report_header_hospital'));
        $sheet->setCellValue('A4', 'KARTU STOCK');
        $sheet->setCellValue('A5', 'PERIODE: ' . Carbon::parse($this->params['start'])->format('d/m/Y') . ' - ' . Carbon::parse($this->params['end'])->format('d/m/Y'));

        $sheet->mergeCells('A1:I1');
        $sheet->mergeCells('A3:I3');
        $sheet->mergeCells('A4:I4');
        $sheet->mergeCells('A5:I5');

        $sheet->getStyle('A1')->getFont()->setItalic(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('A3:A5')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A3:A5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Informasi summary
        $summaryRow = 7;
        $sheet->setCellValue('A' . $summaryRow, 'Saldo Awal');
        $sheet->setCellValue('C' . $summaryRow, $this->summary->qty_saldo_awal_asset);
        $sheet->mergeCells('A' . $summaryRow . ':B' . $summaryRow);

        $summaryRow++;
        $sheet->setCellValue('A' . $summaryRow, 'Total Masuk');
        $sheet->setCellValue('C' . $summaryRow, $this->summary->qty_asset_masuk);
        $sheet->mergeCells('A' . $summaryRow . ':B' . $summaryRow);

        $summaryRow++;
        $sheet->setCellValue('A' . $summaryRow, 'Total Keluar');
        $sheet->setCellValue('C' . $summaryRow, $this->summary->qty_asset_keluar);
        $sheet->mergeCells('A' . $summaryRow . ':B' . $summaryRow);

        $summaryRow++;
        $sheet->setCellValue('A' . $summaryRow, 'Saldo Akhir');
        $sheet->setCellValue('C' . $summaryRow, $this->summary->qty_saldo_awal_asset + $this->summary->qty_asset_masuk - $this->summary->qty_asset_keluar);
        $sheet->mergeCells('A' . $summaryRow . ':B' . $summaryRow);

        $sheet->getStyle('A7:C' . $summaryRow)->applyFromArray([
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN]
            ]
        ]);

        // Header tabel detail
        $headerRow = $summaryRow + 2;
        $headers = ['No', 'Kode QR', 'Nama Aset', 'Tanggal Transaksi', 'Nomor Logistik', 'Jenis Transaksi', 'Ruangan', 'Jumlah', 'Harga'];
        foreach ($headers as $key => $header) {
            $sheet->setCellValue(chr(65 + $key) . $headerRow, $header);
        }

        $sheet->getStyle('A' . $headerRow . ':I' . $headerRow)->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER
            ],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN]
            ]
        ]);

        // Data detail
        $row = $headerRow + 1;
        foreach ($this->details as $index => $detail) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $detail->asset->asset_code);
            $sheet->setCellValue('C' . $row, $detail->asset->asset_name);
            $sheet->setCellValue('D' . $row, Carbon::parse($detail->logistic->logistic_date)->format('d/m/Y'));
            $sheet->setCellValue('E' . $row, $detail->logistic->logistic_number);
            $sheet->setCellValue('F' . $row, $detail->logistic_type);
            $sheet->setCellValue('G' . $row, $detail->room_code ? $detail->room_code . ' - ' . $detail->room_name : '-');
            $sheet->setCellValue('H' . $row, $detail->quantity);
            $sheet->setCellValue('I' . $row, $detail->unit_price);

            $sheet->getStyle('A' . $row . ':I' . $row)->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN]
                ]
            ]);

            $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle('D' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle('F' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle('H' . $row . ':I' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            $row++;
        }

        // Set column widths
        $sheet->getColumnDimension('A')->setWidth(5);   // No
        $sheet->getColumnDimension('B')->setWidth(15);  // Kode QR
        $sheet->getColumnDimension('C')->setWidth(30);  // Nama Aset
        $sheet->getColumnDimension('D')->setWidth(15);  // Tanggal
        $sheet->getColumnDimension('E')->setWidth(15);  // Nomor Logistik
        $sheet->getColumnDimension('F')->setWidth(15);  // Jenis Transaksi
        $sheet->getColumnDimension('G')->setWidth(30);  // Ruangan
        $sheet->getColumnDimension('H')->setWidth(10);  // Jumlah
        $sheet->getColumnDimension('I')->setWidth(15);  // Harga

        // Tanda tangan
        $signRow = $row + 2;
        $sheet->setCellValue('G' . $signRow, 'PONTIANAK, ' . Carbon::now()->translatedFormat('d F Y'));
        $sheet->mergeCells('G' . $signRow . ':I' . $signRow);
        $sheet->getStyle('G' . $signRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $signRow += 2;
        $leftParty = Employee::where("pic_type", "KETUA_TEAM_ASET")->first();
        $rightParty = Employee::where("pic_type", "PIC_PENGURUS_BARANG")->first();

        // Jabatan kiri dan kanan
        $sheet->setCellValue('B' . $signRow, 'KA TIM ASET');
        $sheet->mergeCells('B' . $signRow . ':D' . $signRow);
        $sheet->setCellValue('G' . $signRow, 'PENGURUS BARANG PENGGUNA');
        $sheet->mergeCells('G' . $signRow . ':I' . $signRow);
        $sheet->getStyle('B' . $signRow . ':I' . $signRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Nama dan NIP
        $signRow += 5;
        // Kiri
        $sheet->setCellValue('B' . $signRow, $leftParty->employee_name);
        $sheet->mergeCells('B' . $signRow . ':D' . $signRow);
        $sheet->getStyle('B' . $signRow)->getFont()->setBold(true)->setUnderline(true);
        $sheet->getStyle('B' . $signRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $signRow += 1;
        $sheet->setCellValue('B' . $signRow, "'" . $leftParty->employee_identification_number);
        $sheet->mergeCells('B' . $signRow . ':D' . $signRow);
        $sheet->getStyle('B' . $signRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('B' . $signRow)->getNumberFormat()->setFormatCode('@');

        // Kanan
        $signRow -= 1;
        $sheet->setCellValue('G' . $signRow, $rightParty->employee_name);
        $sheet->mergeCells('G' . $signRow . ':I' . $signRow);
        $sheet->getStyle('G' . $signRow)->getFont()->setBold(true)->setUnderline(true);
        $sheet->getStyle('G' . $signRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $signRow += 1;
        $sheet->setCellValue('G' . $signRow, "'" . $rightParty->employee_identification_number);
        $sheet->mergeCells('G' . $signRow . ':I' . $signRow);
        $sheet->getStyle('G' . $signRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('G' . $signRow)->getNumberFormat()->setFormatCode('@');

        return $sheet;
    }
}
