<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class OutgoingLogisticsExport implements WithTitle, WithStyles
{
    protected $data;
    protected $params;

    public function __construct($data, $params)
    {
        $this->data = $data;
        $this->params = $params;
    }

    public function collection()
    {
        return $this->data;
    }

    public function title(): string
    {
        return 'Barang Keluar';
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setCellValue('A1', 'Laporan Dibuat: ' . date('Y-m-d H:i:s'));
        $sheet->setCellValue('A3', config('app.report_header_hospital'));
        $sheet->setCellValue('A4', 'PERIODE: ' . $this->params['start'] . ' - ' . $this->params['end']);

        $sheet->mergeCells('A1:K1');
        $sheet->mergeCells('A3:K3');
        $sheet->mergeCells('A4:K4');

        $sheet->getStyle('A1')->getFont()->setItalic(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('A3:A4')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A3:A4')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

        $sheet->setCellValue('A6', 'No');
        $sheet->setCellValue('B6', 'Nomor Logistik');
        $sheet->setCellValue('C6', 'Tanggal Logistik');
        $sheet->setCellValue('D6', 'Ruangan');
        $sheet->setCellValue('E6', 'Kode Barang');
        $sheet->setCellValue('F6', 'Nama Barang');
        $sheet->setCellValue('G6', 'Jumlah Barang');
        $sheet->setCellValue('H6', 'Satuan Barang');
        $sheet->setCellValue('I6', 'Harga Satuan');
        $sheet->setCellValue('J6', 'Total Harga');
        $sheet->setCellValue('K6', 'Keterangan');

        $sheet->getStyle('A6:K6')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN
                ]
            ]
        ]);

        $sheet->getColumnDimension('A')->setWidth(5);
        $sheet->getColumnDimension('B')->setWidth(20);
        $sheet->getColumnDimension('C')->setWidth(15);
        $sheet->getColumnDimension('D')->setWidth(20);
        $sheet->getColumnDimension('E')->setWidth(18);
        $sheet->getColumnDimension('F')->setWidth(22);
        $sheet->getColumnDimension('G')->setWidth(15);
        $sheet->getColumnDimension('H')->setWidth(15);
        $sheet->getColumnDimension('I')->setWidth(15);
        $sheet->getColumnDimension('J')->setWidth(18);
        $sheet->getColumnDimension('K')->setWidth(25);

        $rowNumber = 7;
        foreach ($this->data as $index => $item) {
            $sheet->setCellValue('A' . $rowNumber, $index + 1);
            $sheet->setCellValue('B' . $rowNumber, $item->logistic_number);
            $sheet->setCellValue('C' . $rowNumber, $item->logistic_date);
            $sheet->setCellValue('D' . $rowNumber, $item->room_name);
            $sheet->setCellValue('E' . $rowNumber, $item->asset_code);
            $sheet->setCellValue('F' . $rowNumber, $item->asset_name);
            $sheet->setCellValue('G' . $rowNumber, $item->quantity);
            $sheet->setCellValue('H' . $rowNumber, $item->uom_name);
            $sheet->setCellValue('I' . $rowNumber, number_format($item->unit_price, 2));
            $sheet->setCellValue('J' . $rowNumber, number_format($item->total_price, 2));
            $sheet->setCellValue('K' . $rowNumber, $item->logistic_notes ?? '');

            $sheet->getStyle('A' . $rowNumber . ':K' . $rowNumber)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            $rowNumber++;
        }
    }
}
