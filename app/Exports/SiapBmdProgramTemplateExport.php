<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class SiapBmdProgramTemplateExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        return [
            ['1.02.01', 'PROGRAM PENUNJANG URUSAN PEMERINTAHAN DAERAH PROVINSI', 'PROGRAM', '', 'BRANCH'],
            ['1.02.01.1.01', 'PERENCANAAN, PENGANGGARAN, DAN EVALUASI KINERJA PERANGKAT DAERAH', 'ACTIVITY', '1.02.01', '<PERSON><PERSON>CH'],
            ['1.02.01.1.01.0001', '<PERSON><PERSON><PERSON><PERSON>erah', 'SUB_ACTIVITY', '1.02.01.1.01', 'LEAF'],
            ['1.02.01.1.01.0002', 'Koordinasi dan Penyusunan Dokumen RKA-SKPD', 'SUB_ACTIVITY', '1.02.01.1.01', 'LEAF'],
            ['1.02.01.1.01.0003', 'Koordinasi dan Penyusunan Dokumen Perubahan RKA-SKPD', 'SUB_ACTIVITY', '1.02.01.1.01', 'LEAF'],
            ['1.02.01.1.02', 'ADMINISTRASI KEUANGAN PERANGKAT DAERAH', 'ACTIVITY', '1.02.01', 'BRANCH'],
            ['1.02.01.1.02.0001', 'Penyediaan Gaji dan Tunjangan ASN', 'SUB_ACTIVITY', '1.02.01.1.02', 'LEAF'],
            ['1.02.01.1.02.0002', 'Penyediaan Administrasi Pelaksanaan Tugas ASN', 'SUB_ACTIVITY', '1.02.01.1.02', 'LEAF'],
            ['1.02.01.1.03', 'ADMINISTRASI BARANG MILIK DAERAH PADA PERANGKAT DAERAH', 'ACTIVITY', '1.02.01', 'BRANCH'],
            ['1.02.01.1.03.0001', 'Penyusunan Perencanaan Kebutuhan Barang Milik Daerah SKPD', 'SUB_ACTIVITY', '1.02.01.1.03', 'LEAF'],
        ];
    }

    public function headings(): array
    {
        return ['Kode', 'Nama', 'Type', 'Parent', 'Tree'];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15,
            'B' => 40,
            'C' => 18,
            'D' => 15,
            'E' => 18,
        ];
    }

    public function title(): string
    {
        return 'Template Import SIAP BMD Program';
    }
}

