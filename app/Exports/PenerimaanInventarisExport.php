<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class PenerimaanInventarisExport implements FromCollection, WithTitle, WithStyles
{
    protected $data;
    protected $params;

    public function __construct($data, $params)
    {
        $this->data = $data;
        $this->params = $params;
    }

    public function collection()
    {
        return collect([]);  // Return empty collection karena kita akan mengisi data manual
    }

    public function title(): string
    {
        return 'Penerimaan Inventaris';
    }

    public function styles(Worksheet $sheet)
    {
        // Judul <PERSON>poran (di tengah)
        $sheet->setCellValue('D4', 'DAFTAR LAPORAN INVENTARIS ANGGARAN TAHUN ' . date('Y', strtotime($this->params['start'])));
        $sheet->setCellValue('D5', config('app.report_header_hospital'));
        $sheet->setCellValue('D6', 'PROVINSI ' . strtoupper(config('app.hospital_province')));
        $sheet->setCellValue('D7', 'PERIODE: ' . $this->params['start'] . ' - ' . $this->params['end']);

        // Tambahkan jenis inventaris
        $jenisText = $this->params['jenis'] === 'all' ? 'SEMUA JENIS INVENTARIS' :
            ($this->params['jenis'] === 'intrakomptabel' ? 'INTRAKOMPTABEL' : 'EKSTRAKOMPTABEL');
        $sheet->setCellValue('D8', $jenisText);

        $sheet->mergeCells('D4:K4');
        $sheet->mergeCells('D5:K5');
        $sheet->mergeCells('D6:K6');
        $sheet->mergeCells('D7:K7');
        $sheet->mergeCells('D8:K8');

        $sheet->getStyle('D4:D8')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('D4:D8')->getFont()->setBold(true);

        // Informasi Provinsi dan Kabupaten
        $sheet->setCellValue('A9', 'Provinsi');
        $sheet->setCellValue('C9', ': ' . strtoupper(config('app.hospital_province')));
        $sheet->setCellValue('A10', 'Kabupaten/Kota');
        $sheet->setCellValue('C10', ': ' . strtoupper(config('app.hospital_city')));

        // Header Tabel mulai dari baris 12
        $headerRow = 12;

        // Header Utama
        $sheet->setCellValue('A' . $headerRow, 'Kode Barang');
        $sheet->setCellValue('B' . $headerRow, 'Nama Barang');
        $sheet->setCellValue('C' . $headerRow, 'Spesifikasi');
        $sheet->setCellValue('E' . $headerRow, 'Jumlah Barang');
        $sheet->setCellValue('F' . $headerRow, 'Satuan Barang');
        $sheet->setCellValue('G' . $headerRow, 'Harga Satuan (Rp)');
        $sheet->setCellValue('H' . $headerRow, 'Total Nilai Barang (Rp)');
        $sheet->setCellValue('I' . $headerRow, 'Total Biaya Atribut (Rp)');
        $sheet->setCellValue('J' . $headerRow, 'Nilai Perolehan Barang (Rp)');
        $sheet->setCellValue('K' . $headerRow, 'Harga Satuan Perolehan (Rp)');
        $sheet->setCellValue('L' . $headerRow, 'Sub Kegiatan dan Rekening Anggaran Belanja Daerah Atas Pengadaan Barang');
        $sheet->setCellValue('P' . $headerRow, 'Tanggal Perolehan');
        $sheet->setCellValue('Q' . $headerRow, 'Dokumen Sumber Perolehan');
        $sheet->setCellValue('T' . $headerRow, 'Keterangan');

        // Sub Header
        $subHeaderRow = $headerRow + 1;
        $sheet->setCellValue('C' . $subHeaderRow, 'Nama Barang');
        $sheet->setCellValue('D' . $subHeaderRow, 'Merek/Type');
        $sheet->setCellValue('L' . $subHeaderRow, 'Kode Sub Kegiatan');
        $sheet->setCellValue('M' . $subHeaderRow, 'Nama Sub Kegiatan');
        $sheet->setCellValue('N' . $subHeaderRow, 'Kode Rekening');
        $sheet->setCellValue('O' . $subHeaderRow, 'Uraian Belanja');
        $sheet->setCellValue('Q' . $subHeaderRow, 'Bentuk');
        $sheet->setCellValue('R' . $subHeaderRow, 'Nama Penyedia');
        $sheet->setCellValue('S' . $subHeaderRow, 'Nomor');

        // Merge cells untuk header
        $sheet->mergeCells('A' . $headerRow . ':A' . ($headerRow + 1));
        $sheet->mergeCells('B' . $headerRow . ':B' . ($headerRow + 1));
        $sheet->mergeCells('C' . $headerRow . ':D' . $headerRow);
        $sheet->mergeCells('E' . $headerRow . ':E' . ($headerRow + 1));
        $sheet->mergeCells('F' . $headerRow . ':F' . ($headerRow + 1));
        $sheet->mergeCells('G' . $headerRow . ':G' . ($headerRow + 1));
        $sheet->mergeCells('H' . $headerRow . ':H' . ($headerRow + 1));
        $sheet->mergeCells('I' . $headerRow . ':I' . ($headerRow + 1));
        $sheet->mergeCells('J' . $headerRow . ':J' . ($headerRow + 1));
        $sheet->mergeCells('K' . $headerRow . ':K' . ($headerRow + 1));
        $sheet->mergeCells('L' . $headerRow . ':O' . $headerRow);
        $sheet->mergeCells('P' . $headerRow . ':P' . ($headerRow + 1));
        $sheet->mergeCells('Q' . $headerRow . ':S' . $headerRow);
        $sheet->mergeCells('T' . $headerRow . ':T' . ($headerRow + 1));

        // Style untuk header
        $sheet->getStyle('A' . $headerRow . ':T' . ($headerRow + 1))->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
                'wrapText' => true
            ],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN]
            ]
        ]);

        // Data
        $row = $headerRow + 2;
        foreach ($this->data as $val) {
            $sheet->setCellValue('A' . $row, $val->item->item_code ?? '-');
            $sheet->setCellValue('B' . $row, $val->item->item_name ?? '-');
            $sheet->setCellValue('C' . $row, $val->item->item_name ?? '-');
            $sheet->setCellValue('D' . $row, $val->brand ?? '-');
            $sheet->setCellValue('E' . $row, $val->quantity ?? '1');
            $sheet->setCellValue('F' . $row, $val->uom_name ?? 'Unit');
            $sheet->setCellValue('G' . $row, $val->unit_price ?? '0');
            $sheet->setCellValue('H' . $row, ($val->quantity * $val->unit_price) ?? '0');
            $sheet->setCellValue('I' . $row, '');
            $sheet->setCellValue('J' . $row, ($val->quantity * $val->unit_price) ?? '0');
            $sheet->setCellValue('K' . $row, $val->unit_price ?? '0');
            $sheet->setCellValue('L' . $row, $val->sub_activity_code ?? '-');
            $sheet->setCellValue('M' . $row, $val->sub_activity_name ?? '-');
            $sheet->setCellValue('N' . $row, $val->spending_account_code ?? '-');
            $sheet->setCellValue('O' . $row, $val->purchasing_account ?? '-');
            $sheet->setCellValue('P' . $row, $val->received_date ?? '-');
            $sheet->setCellValue('Q' . $row, $val->contract_form ?? '-');
            $sheet->setCellValue('R' . $row, '');
            $sheet->setCellValue('S' . $row, '');
            $sheet->setCellValue('T' . $row, '');

            // Style untuk angka
            $sheet->getStyle('G' . $row . ':K' . $row)->getNumberFormat()->setFormatCode('#,##0');
            $sheet->getStyle('G' . $row . ':K' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            // Border untuk data
            $sheet->getStyle('A' . $row . ':T' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

            $row++;
        }

        // Set column widths
        $sheet->getColumnDimension('A')->setWidth(15);  // Kode Barang
        $sheet->getColumnDimension('B')->setWidth(25);  // Nama Barang
        $sheet->getColumnDimension('C')->setWidth(25);  // Spesifikasi - Nama Barang
        $sheet->getColumnDimension('D')->setWidth(20);  // Spesifikasi - Merek/Type
        $sheet->getColumnDimension('E')->setWidth(12);  // Jumlah Barang
        $sheet->getColumnDimension('F')->setWidth(12);  // Satuan Barang
        $sheet->getColumnDimension('G')->setWidth(15);  // Harga Satuan
        $sheet->getColumnDimension('H')->setWidth(15);  // Total Nilai Barang
        $sheet->getColumnDimension('I')->setWidth(15);  // Total Biaya Atribut
        $sheet->getColumnDimension('J')->setWidth(15);  // Nilai Perolehan Barang
        $sheet->getColumnDimension('K')->setWidth(15);  // Harga Satuan Perolehan
        $sheet->getColumnDimension('L')->setWidth(15);  // Kode Sub Kegiatan
        $sheet->getColumnDimension('M')->setWidth(25);  // Nama Sub Kegiatan
        $sheet->getColumnDimension('N')->setWidth(15);  // Kode Rekening
        $sheet->getColumnDimension('O')->setWidth(25);  // Uraian Belanja
        $sheet->getColumnDimension('P')->setWidth(15);  // Tanggal Perolehan
        $sheet->getColumnDimension('Q')->setWidth(15);  // Bentuk
        $sheet->getColumnDimension('R')->setWidth(20);  // Nama Penyedia
        $sheet->getColumnDimension('S')->setWidth(15);  // Nomor
        $sheet->getColumnDimension('T')->setWidth(25);  // Keterangan

        return $sheet;
    }
}
