<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use App\Models\Item;

class AssetLogisticExport implements FromCollection, WithTitle, WithStyles
{
    protected $data;
    protected $params;

    public function __construct($data, $params = [])
    {
        $this->data = $data;
        $this->params = $params;
    }

    public function collection()
    {
        // Return empty collection to prevent data being added automatically
        return new Collection();
    }

    public function title(): string
    {
        return 'Asset Logistic';
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setCellValue('A1', 'Laporan Dibuat: ' . date('Y-m-d H:i:s'));
        $sheet->setCellValue('A3', config('app.report_header_hospital'));
        $sheet->setCellValue('A4', 'DAFTAR ASET LOGISTIK');

        // Add filter information if available
        $row = 5;
        if (isset($this->params['filter_kode_barang']) && $this->params['filter_kode_barang']) {
            // Get item details for the filtered kode barang
            $item = Item::find($this->params['filter_kode_barang']);
            if ($item) {
                $sheet->setCellValue('A' . $row, 'Kode Barang: ' . $item->item_code . ' - ' . $item->item_name);
                $row++;
            }
        }
        if (isset($this->params['filter_status']) && $this->params['filter_status']) {
            $statusText = $this->params['filter_status'] === 'active' ? 'Aktif' : 'Tidak Aktif';
            $sheet->setCellValue('A' . $row, 'Filter Status: ' . $statusText);
            $row++;
        }
        if (isset($this->params['search']) && $this->params['search']) {
            $sheet->setCellValue('A' . $row, 'Pencarian: ' . $this->params['search']);
            $row++;
        }

        $headerRow = $row + 1;

        $sheet->mergeCells('A1:G1');
        $sheet->mergeCells('A3:G3');
        $sheet->mergeCells('A4:G4');

        $sheet->getStyle('A1')->getFont()->setItalic(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('A3:A4')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A3:A4')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

        // Style filter rows if they exist
        if (isset($this->params['filter_kode_barang']) || isset($this->params['filter_status']) || isset($this->params['search'])) {
            for ($i = 5; $i < $headerRow; $i++) {
                $sheet->mergeCells('A' . $i . ':G' . $i);
                $sheet->getStyle('A' . $i)->getFont()->setItalic(true);
                $sheet->getStyle('A' . $i)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
            }
        }

        $headers = [
            'No', 'Kode Barang', 'Nama Barang Habis Pakai', 'Satuan', 
            'Stok Rekapitulasi', 'Bidang Permintaan', 'Status'
        ];

        foreach ($headers as $key => $header) {
            $sheet->setCellValue(chr(65 + $key) . $headerRow, $header);
        }

        $sheet->getStyle('A' . $headerRow . ':G' . $headerRow)->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
            ],
            'borders' => [
                'allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]
            ]
        ]);

        $dataRow = $headerRow + 1;

        foreach ($this->data as $index => $item) {
            $sheet->setCellValue('A' . $dataRow, $index + 1);
            $sheet->setCellValue('B' . $dataRow, $item->asset_entry_code);
            $sheet->setCellValue('C' . $dataRow, $item->asset_name);
            $sheet->setCellValue('D' . $dataRow, $item->uom_name);
            $sheet->setCellValue('E' . $dataRow, $item->recapitulation_name);
            $sheet->setCellValue('F' . $dataRow, $item->stock_name);
            $sheet->setCellValue('G' . $dataRow, $item->active == 1 ? 'Aktif' : 'Tidak Aktif');

            // Add borders
            $sheet->getStyle('A' . $dataRow . ':G' . $dataRow)->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]
                ]
            ]);

            // Center align status column
            $sheet->getStyle('G' . $dataRow)->getAlignment()
                ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

            $dataRow++;
        }

        // Add total row
        $totalRow = $dataRow;
        $sheet->setCellValue('A' . $totalRow, 'Total');
        $sheet->mergeCells('A' . $totalRow . ':F' . $totalRow);
        $sheet->setCellValue('G' . $totalRow, count($this->data) . ' item');

        // Style total row
        $sheet->getStyle('A' . $totalRow . ':G' . $totalRow)->applyFromArray([
            'font' => ['bold' => true],
            'borders' => [
                'allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]
            ]
        ]);

        $sheet->getStyle('G' . $totalRow)->getAlignment()
            ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

        // Format columns
        $sheet->getColumnDimension('A')->setWidth(5);
        $sheet->getColumnDimension('B')->setWidth(15);
        $sheet->getColumnDimension('C')->setWidth(30);
        $sheet->getColumnDimension('D')->setWidth(10);
        $sheet->getColumnDimension('E')->setWidth(20);
        $sheet->getColumnDimension('F')->setWidth(20);
        $sheet->getColumnDimension('G')->setWidth(12);

        return $sheet;
    }
}
