<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Collection;

class AssetPositionExport implements WithTitle, WithStyles
{
    protected $roomData;
    protected $assets;

    public function __construct($roomData, $assets)
    {
        $this->roomData = $roomData;
        $this->assets = $assets;
    }

    public function title(): string
    {
        return 'Asset Position Details';
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->mergeCells('A1:E1');
        $sheet->setCellValue('A1', 'Detail Kategori');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->setCellValue('A3', 'Kode Ruangan: ' . $this->roomData['room_code']);
        $sheet->setCellValue('A4', 'Nama Ruangan: ' . $this->roomData['room_name']);
        $sheet->setCellValue('E3', 'PIC Ruangan: ' . $this->roomData['pic_room']);
        $sheet->setCellValue('E4', 'Jumlah Aset: ' . $this->roomData['assets_count']);

        $sheet->getStyle('E3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('E4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $sheet->setCellValue('A7', '#');
        $sheet->setCellValue('B7', 'Kode QR');
        $sheet->setCellValue('C7', 'Kode Barang');
        $sheet->setCellValue('D7', 'Nama Aset');
        $sheet->setCellValue('E7', 'Kode Register / SN');
        $sheet->setCellValue('F7', 'Merk');
        $sheet->setCellValue('G7', 'Distributor');
        $sheet->setCellValue('H7', 'Harga');

        $sheet->getStyle('A7:H7')->getFont()->setBold(true);
        $sheet->getStyle('A7:H7')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A7:H7')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $sheet->getColumnDimension('A')->setWidth(5);
        $sheet->getColumnDimension('B')->setWidth(20);
        $sheet->getColumnDimension('C')->setWidth(20);
        $sheet->getColumnDimension('D')->setWidth(30);
        $sheet->getColumnDimension('E')->setWidth(30);
        $sheet->getColumnDimension('F')->setWidth(30);
        $sheet->getColumnDimension('G')->setWidth(30);
        $sheet->getColumnDimension('H')->setWidth(30);

        $row = 8;
        foreach ($this->assets as $index => $asset) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $asset->qr_code);
            $sheet->setCellValue('C' . $row, $asset->item_code);
            $sheet->setCellValue('D' . $row, $asset->asset_name);
            $sheet->setCellValue('E' . $row, $asset->register_code);
            $sheet->setCellValue('F' . $row, $asset->brand);
            $sheet->setCellValue('G' . $row, $asset->distributor_name);
            $sheet->setCellValue('H' . $row, number_format($asset->unit_price, 0, ',', '.'));

            $sheet->getStyle('A' . $row . ':H' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

            $row++;
        }
    }
}
