<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Collection;

class ResumeOutgoingExport implements WithTitle, WithStyles
{
    protected $roomData;
    protected $assets;

    public function __construct($roomData, $assets)
    {
        $this->roomData = $roomData;
        $this->assets = $assets;
    }

    public function title(): string
    {
        return 'Asset Position Details';
    }

    public function styles(Worksheet $sheet)
    {
        $isGrouped = $this->roomData['is_grouped'] ?? false;
        
        if ($isGrouped) {
            // Grouped mode - different columns
            $sheet->mergeCells('A1:G1');
            $sheet->setCellValue('A1', 'Detail Resume (Grouped by Item Code)');
            $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
            $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

            $sheet->setCellValue('A3', 'Kode Ruangan: ' . ($this->roomData['room_code'] ?? '-'));
            $sheet->setCellValue('A4', 'Nama Ruangan: ' . ($this->roomData['room_name'] ?? '-'));
            $sheet->setCellValue('A5', 'Periode: ' . ($this->roomData['periode'] ?? '-'));
            $sheet->setCellValue('G3', 'Jumlah Item: ' . (count($this->assets)));

            $sheet->getStyle('G3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            $sheet->setCellValue('A7', '#');
            $sheet->setCellValue('B7', 'Kode Barang');
            $sheet->setCellValue('C7', 'Nama Item');
            $sheet->setCellValue('D7', 'Nama Asset');
            $sheet->setCellValue('E7', 'Total Kuantitas');
            $sheet->setCellValue('F7', 'Rata-rata Harga');
            $sheet->setCellValue('G7', 'Total Harga');

            $sheet->getStyle('A7:G7')->getFont()->setBold(true);
            $sheet->getStyle('A7:G7')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle('A7:G7')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

            $sheet->getColumnDimension('A')->setWidth(5);
            $sheet->getColumnDimension('B')->setWidth(15);
            $sheet->getColumnDimension('C')->setWidth(25);
            $sheet->getColumnDimension('D')->setWidth(25);
            $sheet->getColumnDimension('E')->setWidth(15);
            $sheet->getColumnDimension('F')->setWidth(15);
            $sheet->getColumnDimension('G')->setWidth(20);

            $row = 8;
            foreach ($this->assets as $index => $asset) {
                $sheet->setCellValue('A' . $row, $index + 1);
                $sheet->setCellValue('B' . $row, $asset['item_code'] ?? '-');
                $sheet->setCellValue('C' . $row, $asset['item_name'] ?? '-');
                $sheet->setCellValue('D' . $row, $asset['asset_name'] ?? '-');
                $sheet->setCellValue('E' . $row, $asset['quantity'] ?? 0);
                $sheet->setCellValue('F' . $row, $asset['unit_price'] ?? 0);
                $sheet->setCellValue('G' . $row, $asset['total_price'] ?? 0);

                $sheet->getStyle('F' . $row)->getNumberFormat()->setFormatCode('#,##0.00');
                $sheet->getStyle('G' . $row)->getNumberFormat()->setFormatCode('#,##0.00');

                $sheet->getStyle('A' . $row . ':G' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

                $row++;
            }
        } else {
            // Normal mode - original columns
            $sheet->mergeCells('A1:I1');
            $sheet->setCellValue('A1', 'Detail Resume (Individual Transactions)');
            $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
            $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

            $sheet->setCellValue('A3', 'Kode Ruangan: ' . ($this->roomData['room_code'] ?? '-'));
            $sheet->setCellValue('A4', 'Nama Ruangan: ' . ($this->roomData['room_name'] ?? '-'));
            $sheet->setCellValue('A5', 'Periode: ' . ($this->roomData['periode'] ?? '-'));
            $sheet->setCellValue('I3', 'Jumlah Transaksi: ' . (count($this->assets)));

            $sheet->getStyle('I3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            $sheet->setCellValue('A7', '#');
            $sheet->setCellValue('B7', 'Nomor Logistik');
            $sheet->setCellValue('C7', 'Tanggal Logistik');
            $sheet->setCellValue('D7', 'Kode Barang');
            $sheet->setCellValue('E7', 'Nama Item');
            $sheet->setCellValue('F7', 'Nama Asset');
            $sheet->setCellValue('G7', 'Kuantitas');
            $sheet->setCellValue('H7', 'Harga Satuan');
            $sheet->setCellValue('I7', 'Total Harga');

            $sheet->getStyle('A7:I7')->getFont()->setBold(true);
            $sheet->getStyle('A7:I7')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle('A7:I7')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

            $sheet->getColumnDimension('A')->setWidth(5);
            $sheet->getColumnDimension('B')->setWidth(20);
            $sheet->getColumnDimension('C')->setWidth(20);
            $sheet->getColumnDimension('D')->setWidth(15);
            $sheet->getColumnDimension('E')->setWidth(25);
            $sheet->getColumnDimension('F')->setWidth(25);
            $sheet->getColumnDimension('G')->setWidth(15);
            $sheet->getColumnDimension('H')->setWidth(15);
            $sheet->getColumnDimension('I')->setWidth(20);

            $row = 8;
            foreach ($this->assets as $index => $asset) {
                $sheet->setCellValue('A' . $row, $index + 1);
                $sheet->setCellValue('B' . $row, $asset['logistic_number'] ?? '-');
                $sheet->setCellValue('C' . $row, $asset['logistic_date'] ?? '-');
                $sheet->setCellValue('D' . $row, $asset['item_code'] ?? '-');
                $sheet->setCellValue('E' . $row, $asset['item_name'] ?? '-');
                $sheet->setCellValue('F' . $row, $asset['asset_name'] ?? '-');
                $sheet->setCellValue('G' . $row, $asset['quantity'] ?? 0);
                $sheet->setCellValue('H' . $row, $asset['unit_price'] ?? 0);
                $sheet->setCellValue('I' . $row, $asset['total_price'] ?? 0);

                $sheet->getStyle('H' . $row)->getNumberFormat()->setFormatCode('#,##0.00');
                $sheet->getStyle('I' . $row)->getNumberFormat()->setFormatCode('#,##0.00');

                $sheet->getStyle('A' . $row . ':I' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

                $row++;
            }
        }
    }
}
