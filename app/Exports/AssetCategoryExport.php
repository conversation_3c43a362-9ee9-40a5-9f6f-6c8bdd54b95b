<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Collection;

class AssetCategoryExport implements WithTitle, WithStyles
{
    protected $categoryData;
    protected $assets;

    public function __construct($categoryData, $assets)
    {
        $this->categoryData = $categoryData;
        $this->assets = $assets;
    }


    public function title(): string
    {
        return 'Asset Category Details';
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->mergeCells('A1:E1');
        $sheet->setCellValue('A1', 'Detail Kategori');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->setCellValue('A3', 'Kode Kategori: ' . $this->categoryData['category_code']);
        $sheet->setCellValue('A4', 'Nama Kategori: ' . $this->categoryData['category_name']);
        $sheet->setCellValue('E3', 'Tipe Kategori: ' . $this->categoryData['category_type']);
        $sheet->setCellValue('E4', 'Jumlah Aset: ' .  $this->categoryData['total_asset']);

        $sheet->getStyle('E3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('E4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    

        $sheet->setCellValue('A7', '#');
        $sheet->setCellValue('B7', 'Kode QR');
        $sheet->setCellValue('C7', 'Kode Barang');
        $sheet->setCellValue('D7', 'Nama Aset');
        $sheet->setCellValue('E7', 'Kode Register / SN');

        $sheet->getStyle('A7:E7')->getFont()->setBold(true);
        $sheet->getStyle('A7:E7')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A7:E7')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $sheet->getColumnDimension('A')->setWidth(5);
        $sheet->getColumnDimension('B')->setWidth(20);
        $sheet->getColumnDimension('C')->setWidth(20);
        $sheet->getColumnDimension('D')->setWidth(30);
        $sheet->getColumnDimension('E')->setWidth(30);

        $row = 8;
        foreach ($this->assets as $index => $asset) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $asset->qr_code);
            $sheet->setCellValue('C' . $row, $asset->item_code);
            $sheet->setCellValue('D' . $row, $asset->item_name);
            $sheet->setCellValue('E' . $row, $asset->register_code);

            $sheet->getStyle('A' . $row . ':E' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

            $row++;
        }
    }
}
