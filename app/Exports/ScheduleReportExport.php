<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ScheduleReportExport implements WithStyles, WithTitle
{
    protected $data;
    protected $params;

    public function __construct($data, $params)
    {
        $this->data = $data;
        $this->params = $params;
    }

    public function title(): string
    {
        return 'Jadwal Pemeliharaan';
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setCellValue('F1', 'Laporan Dibuat: ' . date('Y-m-d : H:i:s'))->getStyle('F1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->setCellValue('A3', 'JADWAL PEMELIHARAAN');

        $sheet->setCellValue('A5', 'PROVINSI');
        $sheet->setCellValue('B5', ': ' . strtoupper(config('app.hospital_province')));
        $sheet->setCellValue('A6', 'SKPD');
        $sheet->setCellValue('B6', ': ' . config('app.report_header_hospital'));

        $sheet->mergeCells('A3:H3');
        $sheet->getStyle('A3')->getFont()->setBold(true)->setSize(17);
        $sheet->getStyle('A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->setCellValue('A8', 'Jadwal');
        $sheet->setCellValue('B8', 'Kategori');
        $sheet->setCellValue('C8', 'Type');
        $sheet->setCellValue('D8', 'Aset');
        $sheet->setCellValue('E8', 'Kode / Register');
        $sheet->setCellValue('G8', 'Catatan');

        $sheet->getStyle('A8:G8')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A8:G8')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A8:G8')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $sheet->getColumnDimension('A')->setWidth(20);
        $sheet->getColumnDimension('B')->setWidth(20);
        $sheet->getColumnDimension('C')->setWidth(20);
        $sheet->getColumnDimension('D')->setWidth(30);
        $sheet->getColumnDimension('E')->setWidth(25);
        $sheet->getColumnDimension('F')->setWidth(25);
        $sheet->getColumnDimension('G')->setWidth(30);

        $row = 9;
        foreach ($this->data as $item) {
            $sheet->setCellValue('A' . $row, $item->schedule_date);
            $sheet->setCellValue('B' . $row, $item->schedule_category);
            $sheet->setCellValue('C' . $row, $item->schedule_type);
            $sheet->setCellValue('D' . $row, $item->item_name);
            $sheet->setCellValue('E' . $row, $item->item_code . ' / ' . $item->register_code);
            $sheet->setCellValue('G' . $row, $item->notes);

            $sheet->getStyle('A' . $row . ':G' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
            $row++;
        }
    }
}
