<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class AspakExport implements WithTitle, WithStyles
{
    protected $data;
    protected $params;

    public function __construct($data, $params)
    {
        $this->data = $data;
        $this->params = $params;
    }


    public function title(): string
    {
        return 'Laporan Aspak';
    }
    public function styles(Worksheet $sheet)
    {
        $sheet->setCellValue('A1', 'Laporan Dibuat: ' . date('Y-m-d : H:i:s'));
        $sheet->setCellValue('A3', 'LAPORAN PENERIMAAN, UJI FUNGSI DAN DISTRIBUSI');
        $sheet->setCellValue('A4', 'DI UNIT PELAYANAN');
        $sheet->setCellValue('A5', 'TAHUN ' . now()->year);

        $sheet->mergeCells('A1:S1');
        $sheet->mergeCells('A3:S3');
        $sheet->mergeCells('A4:S4');
        $sheet->mergeCells('A5:S5');

        $sheet->getStyle('A1')->getFont()->setItalic(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('A3:A5')->getFont()->setBold(true);
        $sheet->getStyle('A3:A5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Headers
        $headers = [
            'No',
            'Nama Barang',
            'Nomor Serial Number',
            'Nomor AKD / AKL',
            'Merek',
            'Model / Type',
            'No. Foto Alkes',
            'Distributor',
            'Tahun Pembelian',
            'Asal Perolehan',
            'Harga Satuan (Rp)',
            'B',
            'RR',
            'RB',
            'Penempatan/Lokasi Barang',
            'Kode Barang',
            'Kode Register',
            'Kelistrikan',
            'Keterangan'
        ];

        $col = 'A';
        $row = 7;
        foreach ($headers as $header) {
            $sheet->setCellValue($col . $row, $header);
            $col++;
        }

        $sheet->getStyle('A7:S7')->getFont()->setBold(true);
        $sheet->getStyle('A7:S7')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A7:S7')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $row = 8;
        foreach ($this->data as $key => $asset) {
            $sheet->setCellValue('A' . $row, $key + 1);
            $sheet->setCellValue('B' . $row, $asset->item->item_name ?? '-');
            $sheet->setCellValue('C' . $row, $asset->serial_number ?? '-');
            $sheet->setCellValue('D' . $row, $asset->assetEntry->akd_number ?? '-');
            $sheet->setCellValue('E' . $row, $asset->assetEntry->brand ?? '-');
            $sheet->setCellValue('F' . $row, $asset->assetEntry->model_type ?? 'IPS');
            $sheet->setCellValue('G' . $row, $asset->last_media_path ? "http://**************".asset('/storage/' . $asset->last_media_path) : '');
            $sheet->setCellValue('H' . $row, $asset->assetEntry->distributor->distributor_name ?? '-');
            $sheet->setCellValue('I' . $row, Carbon::parse($asset->payment_date)->format('Y'));
            $sheet->setCellValue('J' . $row, $asset->assetEntry->source_supply ?? '-');
            $sheet->setCellValue('K' . $row, $asset->unit_price ?? 0);
            $sheet->setCellValue('L' . $row, $asset->asset_condition == 'BAIK' ? '√' : '-');
            $sheet->setCellValue('M' . $row, $asset->asset_condition == 'RUSAK_RINGAN' ? '√' : '-');
            $sheet->setCellValue('N' . $row, $asset->asset_condition == 'RUSAK_BERAT' ? '√' : '-');
            $sheet->setCellValue('O' . $row, $asset->room->room_name ?? '-');
            $sheet->setCellValue('P' . $row, $asset->item->item_code ?? '-');
            $sheet->setCellValue('Q' . $row, str_pad(($key + 1), 4, '0', STR_PAD_LEFT));
            $sheet->setCellValue('R' . $row, $asset->electricity ?? '-');
            $sheet->setCellValue('S' . $row, '');

            // Format currency for price
            $sheet->getStyle('K' . $row)->getNumberFormat()->setFormatCode('#,##0');
            
            $sheet->getStyle('A' . $row . ':S' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
            $sheet->getStyle('A' . $row . ':S' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $row++;
        }

        // Auto size columns
        foreach (range('A', 'S') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
    }
}
