<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class IncomingLogisticsExport implements WithTitle, WithStyles
{
    protected $data;
    protected $params;

    public function __construct($data, $params)
    {
        $this->data = $data;
        $this->params = $params;
    }

    public function collection()
    {
        return $this->data;
    }

    public function title(): string
    {
        return 'Barang Masuk';
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setCellValue('A1', 'Laporan Dibuat: ' . date('Y-m-d H:i:s'));
        $sheet->setCellValue('A3', 'LAPORAN PENGADAAN BMD BERUPA ASET LANCAR PERSEDIAAN');
        $sheet->setCellValue('A4', config('app.report_header_hospital'));
        $sheet->setCellValue('A5', 'PROVINSI ' . strtoupper(config('app.hospital_province')));
        $sheet->setCellValue('A6', 'PERIODE: ' . $this->params['start'] . ' - ' . $this->params['end']);

        $sheet->mergeCells('A1:T1');
        $sheet->mergeCells('A3:T3');
        $sheet->mergeCells('A4:T4');
        $sheet->mergeCells('A5:T5');
        $sheet->mergeCells('A6:T6');

        $sheet->setCellValue('A8', 'Provinsi');
        $sheet->setCellValue('B8', ': ' . strtoupper(config('app.hospital_province')));
        $sheet->setCellValue('A9', 'Kabupaten/Kota');
        $sheet->setCellValue('B9', ': ' . strtoupper(config('app.hospital_city')));

        // Header Tabel
        $sheet->mergeCells('A11:B11');
        $sheet->setCellValue('A11', 'Penggolongan dan Kodifikasi Barang');
        $sheet->mergeCells('M11:P11');
        $sheet->setCellValue('M11', 'Sub Kegiatan dan Rekening Anggaran Belanja Daerah Asal Pengadaan Barang');
        $sheet->mergeCells('Q11:R11');
        $sheet->setCellValue('Q11', 'Dokumen Sumber Perolehan');

        $sheet->setCellValue('A12', 'Kode Barang');
        $sheet->setCellValue('B12', 'Nama Barang');
        $sheet->setCellValue('C11', 'Spesifikasi Nama Barang');
        $sheet->setCellValue('D11', 'Merek/Type');
        $sheet->setCellValue('E11', 'Jumlah Barang');
        $sheet->setCellValue('F11', 'Satuan Barang');
        $sheet->setCellValue('G11', 'Harga Satuan (Rp)');
        $sheet->setCellValue('H11', 'Total Nilai Barang (Rp)');
        $sheet->setCellValue('I11', 'Total Biaya Ambil (Rp)');
        $sheet->setCellValue('J11', 'Nilai Perolehan Barang (Rp)');
        $sheet->setCellValue('K11', 'Harga Satuan Perolehan (Rp)');
        $sheet->setCellValue('M12', 'Kode Sub Kegiatan');
        $sheet->setCellValue('N12', 'Nama Sub Kegiatan');
        $sheet->setCellValue('O12', 'Kode Rekening');
        $sheet->setCellValue('P12', 'Uraian Belanja');
        $sheet->setCellValue('Q12', 'Bentuk');
        $sheet->setCellValue('R12', 'Nama Penyedia');
        $sheet->setCellValue('S11', 'Nomor');
        $sheet->setCellValue('T11', 'Keterangan');

        // Merge cells untuk header yang rowspan
        foreach (['C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'S', 'T'] as $col) {
            $sheet->mergeCells($col . '11:' . $col . '12');
        }

        // Style untuk header
        $sheet->getStyle('A11:T12')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 11
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
                'wrapText' => true
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN
                ]
            ]
        ]);

        // Data
        $rowNumber = 13;
        foreach ($this->data as $item) {
            $sheet->setCellValue('A' . $rowNumber, $item->asset_code);
            $sheet->setCellValue('B' . $rowNumber, $item->asset_name);
            $sheet->setCellValue('C' . $rowNumber, '');
            $sheet->setCellValue('D' . $rowNumber, '');
            $sheet->setCellValue('E' . $rowNumber, $item->quantity);
            $sheet->setCellValue('F' . $rowNumber, $item->uom_name);
            $sheet->setCellValue('G' . $rowNumber, $item->unit_price);
            $sheet->setCellValue('H' . $rowNumber, $item->total_price);
            $sheet->setCellValue('I' . $rowNumber, '');
            $sheet->setCellValue('J' . $rowNumber, '');
            $sheet->setCellValue('K' . $rowNumber, '');
            $sheet->setCellValue('L' . $rowNumber, '');
            $sheet->setCellValue('M' . $rowNumber, '');
            $sheet->setCellValue('N' . $rowNumber, '');
            $sheet->setCellValue('O' . $rowNumber, '');
            $sheet->setCellValue('P' . $rowNumber, '');
            $sheet->setCellValue('Q' . $rowNumber, '');
            $sheet->setCellValue('R' . $rowNumber, '');
            $sheet->setCellValue('S' . $rowNumber, $item->logistic_number);
            $sheet->setCellValue('T' . $rowNumber, $item->logistic_notes);

            // Style untuk data
            $sheet->getStyle('A' . $rowNumber . ':T' . $rowNumber)->applyFromArray([
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN
                    ]
                ]
            ]);

            // Alignment khusus
            $sheet->getStyle('A' . $rowNumber)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle('E' . $rowNumber)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            $sheet->getStyle('F' . $rowNumber)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle('G' . $rowNumber . ':K' . $rowNumber)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            $rowNumber++;
        }

        // Set column widths
        $sheet->getColumnDimension('A')->setWidth(15);
        $sheet->getColumnDimension('B')->setWidth(20);
        $sheet->getColumnDimension('C')->setWidth(20);
        $sheet->getColumnDimension('D')->setWidth(15);
        $sheet->getColumnDimension('E')->setWidth(12);
        $sheet->getColumnDimension('F')->setWidth(12);
        $sheet->getColumnDimension('G')->setWidth(15);
        $sheet->getColumnDimension('H')->setWidth(18);
        $sheet->getColumnDimension('I')->setWidth(18);
        $sheet->getColumnDimension('J')->setWidth(18);
        $sheet->getColumnDimension('K')->setWidth(18);
        $sheet->getColumnDimension('L')->setWidth(15);
        $sheet->getColumnDimension('M')->setWidth(15);
        $sheet->getColumnDimension('N')->setWidth(20);
        $sheet->getColumnDimension('O')->setWidth(15);
        $sheet->getColumnDimension('P')->setWidth(20);
        $sheet->getColumnDimension('Q')->setWidth(15);
        $sheet->getColumnDimension('R')->setWidth(20);
        $sheet->getColumnDimension('S')->setWidth(15);
        $sheet->getColumnDimension('T')->setWidth(25);

        // Style untuk header atas
        $sheet->getStyle('A1')->getFont()->setItalic(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('A3:A6')->getFont()->setBold(true);
        $sheet->getStyle('A3:A6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    }
}
