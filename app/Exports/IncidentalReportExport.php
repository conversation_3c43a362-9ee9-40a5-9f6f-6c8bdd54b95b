<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class IncidentalReportExport implements WithStyles, WithTitle
{
    protected $data;
    protected $params;

    public function __construct($data, $params)
    {
        $this->data = $data;
        $this->params = $params;
    }

    public function title(): string
    {
        return 'Formulir Log Perbaikan Aset';
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setCellValue('A1', 'Laporan Dibuat: ' . date('Y-m-d : H:i:s'));
        $sheet->setCellValue('A3', 'Formulir Log Perbaikan Aset');
        $sheet->setCellValue('A4', 'Periode ' . $this->params['start_date'] . ' - ' . $this->params['end_date']);
        $sheet->setCellValue('A7', 'PROVINSI');
        $sheet->setCellValue('B7', ': ' . config('app.hospital_province'));
        $sheet->setCellValue('A8', 'SKPD');
        $sheet->setCellValue('B8', ': ' . config('app.report_header_hospital'));

        $sheet->mergeCells('A1:I1');
        $sheet->mergeCells('A3:I3');
        $sheet->mergeCells('A4:I4');

        $sheet->getStyle('A1')->getFont()->setItalic(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('A3')->getFont()->setBold(true)->setSize(17);
        $sheet->getStyle('A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A4')->getFont()->setBold(true)->setSize(17);
        $sheet->getStyle('A4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A7')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('B7')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A8')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('B8')->getFont()->setBold(true)->setSize(12);

        $sheet->getColumnDimension('A')->setWidth(25);
        $sheet->getColumnDimension('B')->setWidth(40);
        $sheet->getColumnDimension('C')->setWidth(30);
        $sheet->getColumnDimension('D')->setWidth(30);
        $sheet->getColumnDimension('E')->setWidth(30);
        $sheet->getColumnDimension('F')->setWidth(30);
        $sheet->getColumnDimension('G')->setWidth(30);
        $sheet->getColumnDimension('H')->setWidth(30);
        $sheet->getColumnDimension('I')->setWidth(30);

        $sheet->setCellValue('A11', 'Tanggal Laporan');
        $sheet->setCellValue('B11', 'Aset');
        $sheet->setCellValue('C11', 'Kode / Register');
        $sheet->setCellValue('D11', 'Nama Pengguna');
        $sheet->setCellValue('E11', 'Unit Kerja Pengguna');
        $sheet->setCellValue('F11', 'Jenis Kerusakan');
        $sheet->setCellValue('G11', 'Solusi Perbaikan');
        $sheet->setCellValue('H11', 'Status Perbaikan');
        $sheet->setCellValue('I11', 'Pelaksana Perbaikan');

        $sheet->getStyle('A11:I11')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A11:I11')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getStyle('A11:I11')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $row = 12;
        foreach ($this->data as $item) {
            $sheet->setCellValue('A' . $row, $item->request_date);
            $sheet->setCellValue('B' . $row, $item->item_name);
            $sheet->setCellValue('C' . $row, $item->item_code . ' / ' . $item->register_code);
            $sheet->setCellValue('D' . $row, $item->room_name);
            $sheet->setCellValue('E' . $row, '-');
            $sheet->setCellValue('F' . $row, $item->request_issue);
            $sheet->setCellValue('G' . $row, $item->followup_notes);
            $sheet->setCellValue('H' . $row, $item->followup_type);
            $sheet->setCellValue('I' . $row, $item->followup_person);

            $sheet->getStyle('A' . $row . ':I' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

            $row++;
        }

        $sheet->getStyle('A11:I' . ($row - 1))->getBorders()->getOutline()->setBorderStyle(Border::BORDER_THICK);
    }


}
