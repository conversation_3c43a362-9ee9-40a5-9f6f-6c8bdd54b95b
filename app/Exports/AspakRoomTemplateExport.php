<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class AspakRoomTemplateExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        return [
            [
                '01',
                'Pelayanan Medik dan <PERSON>',
                '',
                'BRANCH'
            ],
            [
                '0101',
                'Pelayanan Rawat Jalan',
                '01',
                'BRANCH'
            ],
            [
                '010101',
                '<PERSON><PERSON><PERSON>k Sp. Penyakit Dalam',
                '0101',
                'LEAF'
            ],
            [
                '010102',
                '<PERSON>uangan Klinik Sp. Kesehatan <PERSON>',
                '0101',
                'LEAF'
            ],
            [
                '0102',
                'Pelayanan Gawat Darurat',
                '01',
                'BRANCH'
            ],
            [
                '010201',
                'Umum',
                '0102',
                'LEAF'
            ],
            [
                '010202',
                'Ruangan Triase',
                '0102',
                'LEAF'
            ]
        ];
    }

    public function headings(): array
    {
        return [
            'kode' => 'Kode',
            'nama' => 'Nama',
            'parent' => 'Parent',
            'tree' => 'Tree'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Header styling
            1 => [
                'font' => [
                    'bold' => true,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E8E8E8'], // Light gray background
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15, // kode - adequate for alphanumeric codes
            'B' => 40, // nama - wider for longer service room names
            'C' => 15, // parent - same as kode for consistency
            'D' => 18, // tree - adequate for BRANCH/LEAF
        ];
    }

    public function title(): string
    {
        return 'Template Import ASPAK Service Room';
    }
}