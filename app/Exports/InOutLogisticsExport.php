<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Contracts\View\View;

class InOutLogisticsExport implements FromView, WithTitle, WithStyles
{
    protected $data;
    protected $dataIncoming;
    protected $params;

    public function __construct($data, $dataIncoming, $params)
    {
        $this->data = $data;
        $this->dataIncoming = $dataIncoming;
        $this->params = $params;
    }

    public function view(): View
    {
        return view('components.logistic.report-inout', [
            'data' => $this->data,
            'dataIncoming' => $this->dataIncoming,
            'params' => $this->params
        ]);
    }

    public function title(): string
    {
        return 'Laporan <PERSON> dan <PERSON>';
    }

    public function styles(Worksheet $sheet)
    {
        // Set orientation to landscape
        $sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);
        
        // Merge cells for header
        $sheet->mergeCells('A1:T1');
        $sheet->mergeCells('A2:T2');
        $sheet->mergeCells('A3:T3');
        $sheet->mergeCells('A4:T4');

        // Style for headers
        $sheet->getStyle('A1:T4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A2:T4')->getFont()->setBold(true);
        
        // Style for table headers
        $sheet->getStyle('A5:T6')->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN
                ]
            ]
        ]);

        // Set column widths
        $columns = ['A' => 15, 'B' => 15, 'C' => 15, 'D' => 15, 'E' => 12, 
                   'F' => 20, 'G' => 15, 'H' => 15, 'I' => 15, 'J' => 10,
                   'K' => 10, 'L' => 15, 'M' => 15, 'N' => 20, 'O' => 12,
                   'P' => 20, 'Q' => 15, 'R' => 15, 'S' => 15, 'T' => 10];
        
        foreach ($columns as $column => $width) {
            $sheet->getColumnDimension($column)->setWidth($width);
        }

        // Style for data rows
        $lastRow = $sheet->getHighestRow();
        $sheet->getStyle('A7:T'.$lastRow)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN
                ]
            ]
        ]);

        // Right align for numeric columns
        $numericColumns = ['E', 'G', 'O', 'Q', 'R'];
        foreach ($numericColumns as $column) {
            $sheet->getStyle($column.'7:'.$column.$lastRow)
                ->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        }

        // Set row height
        $sheet->getDefaultRowDimension()->setRowHeight(20);
    }
}
