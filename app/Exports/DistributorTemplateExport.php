<?php

namespace App\Exports;

use Faker\Factory;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class DistributorTemplateExport implements FromArray, WithHeadings, WithStyles
{
    use Exportable;

    public function array(): array
    {
        // Create Faker instance
        $faker = Factory::create('id_ID'); // Using Indonesian locale
        
        // Generate dummy data
        $data = [];
        for ($i = 0; $i < 10; $i++) {
            $data[] = [
                $faker->company,
                $faker->address,
                $faker->phoneNumber,
                $faker->email
            ];
        }
        
        return $data;
    }

    public function headings(): array
    {
        return [
            'Nama Distributor',
            'Alamat',
            'Telepon',
            'Email'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Make headers bold
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}