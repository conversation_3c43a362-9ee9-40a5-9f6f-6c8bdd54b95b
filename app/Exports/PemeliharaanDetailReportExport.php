<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class PemeliharaanDetailReportExport implements WithStyles, WithTitle
{
    protected $data;
    protected $params;

    public function __construct($data, $params)
    {
        $this->data = $data;
        $this->params = $params;
    }

    public function title(): string
    {
        return 'Daftar Detail Hasil Pemeliharaan';
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setCellValue('A1', 'Laporan Dibuat: ' . date('Y-m-d : H:i:s'));
        $sheet->setCellValue('A3', 'DAFTAR DETAIL HASIL PEMELIHARAAN');
        $sheet->setCellValue('A4', 'Periode ' . $this->params['start_date'] . ' - ' . $this->params['end_date']);
        $sheet->setCellValue('A7', 'PROVINSI');
        $sheet->setCellValue('B7', ': ' . strtoupper(config('app.hospital_province')));
        $sheet->setCellValue('A8', 'SKPD');
        $sheet->setCellValue('B8', ': ' . config('app.report_header_hospital'));

        $sheet->mergeCells('A1:I1');
        $sheet->mergeCells('A3:I3');
        $sheet->mergeCells('A4:I4');

        $sheet->getStyle('A1')->getFont()->setItalic(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('A3')->getFont()->setBold(true)->setSize(17);
        $sheet->getStyle('A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A4')->getFont()->setBold(true)->setSize(17);
        $sheet->getStyle('A4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->getColumnDimension('A')->setWidth(20);
        $sheet->getColumnDimension('B')->setWidth(15);
        $sheet->getColumnDimension('C')->setWidth(30);
        $sheet->getColumnDimension('D')->setWidth(25);
        $sheet->getColumnDimension('E')->setWidth(25);
        $sheet->getColumnDimension('F')->setWidth(20);
        $sheet->getColumnDimension('G')->setWidth(20);
        $sheet->getColumnDimension('H')->setWidth(25);
        $sheet->getColumnDimension('I')->setWidth(30);

        $sheet->setCellValue('A11', 'Kode Barang');
        $sheet->setCellValue('B11', 'Register');
        $sheet->setCellValue('C11', 'Nama Barang');
        $sheet->setCellValue('D11', 'Jenis Pemeliharaan');
        $sheet->setCellValue('E11', 'Pemelihara');
        $sheet->setCellValue('F11', 'Tgl Pemeliharaan');
        $sheet->setCellValue('G11', 'Biaya Pemeliharaan');
        $sheet->setCellValue('H11', 'Bukti Pemeliharaan');
        $sheet->setCellValue('I11', 'Keterangan');

        $sheet->getStyle('A11:I11')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A11:I11')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A11:I11')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $row = 12;
        $totalActivityCost = 0;
        foreach ($this->data as $item) {
            $sheet->setCellValue('A' . $row, $item->item_code);
            $sheet->setCellValue('B' . $row, $item->register_code);
            $sheet->setCellValue('C' . $row, $item->item_name);
            $sheet->setCellValue('D' . $row, $item->activity_name);
            $sheet->setCellValue('E' . $row, $item->distributor_name);
            $sheet->setCellValue('F' . $row, $item->activity_date);
            $sheet->setCellValue('G' . $row, number_format($item->activity_cost, 0, ',', '.'));
            $sheet->setCellValue('H' . $row, $item->document_name);
            $sheet->setCellValue('I' . $row, $item->notes);

            $sheet->getStyle("A{$row}:I{$row}")->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
            $totalActivityCost += $item->activity_cost;
            $row++;
        }

        $sheet->setCellValue('A' . $row, 'TOTAL');
        $sheet->mergeCells("A{$row}:F{$row}");
        $sheet->setCellValue('G' . $row, number_format($totalActivityCost, 0, ',', '.'));

        $sheet->getStyle("A{$row}:G{$row}")->getFont()->setBold(true);
        $sheet->getStyle("A{$row}:G{$row}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle("A11:I{$row}")->getBorders()->getOutline()->setBorderStyle(Border::BORDER_THICK);
    }
}
