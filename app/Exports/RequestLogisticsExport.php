<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class RequestLogisticsExport implements WithTitle, WithStyles
{
    protected $data;
    protected $params;

    public function __construct($data, $params)
    {
        $this->data = $data;
        $this->params = $params;
    }

    public function collection()
    {
        return $this->data;
    }

    public function title(): string
    {
        return 'Request Barang';
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setCellValue('A1', 'Laporan Dibuat: ' . date('Y-m-d H:i:s'));
        $sheet->setCellValue('A3', config('app.report_header_hospital'));
        $sheet->setCellValue('A4', 'PERIODE: ' . $this->params['start'] . ' - ' . $this->params['end']);

        if (isset($this->params['room_name']) && $this->params['room_name']) {
            $sheet->setCellValue('A5', 'RUANGAN: ' . $this->params['room_name']);
            $sheet->mergeCells('A5:J5');
            $sheet->getStyle('A5')->getFont()->setBold(true)->setSize(14);
            $sheet->getStyle('A5')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

            $headerRow = 7;
        } else {
            $headerRow = 6;
        }

        $sheet->mergeCells('A1:J1');
        $sheet->mergeCells('A3:J3');
        $sheet->mergeCells('A4:J4');

        $sheet->getStyle('A1')->getFont()->setItalic(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('A3:A4')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A3:A4')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

        $sheet->setCellValue('A' . $headerRow, 'No');
        $sheet->setCellValue('B' . $headerRow, 'Nomor Request');
        $sheet->setCellValue('C' . $headerRow, 'Tanggal Logistik');
        $sheet->setCellValue('D' . $headerRow, 'Nama Ruangan');
        $sheet->setCellValue('E' . $headerRow, 'Kode Barang');
        $sheet->setCellValue('F' . $headerRow, 'Nama Barang');
        $sheet->setCellValue('G' . $headerRow, 'Jumlah Barang Request');
        $sheet->setCellValue('H' . $headerRow, 'Jumlah Barang Keluar');
        $sheet->setCellValue('I' . $headerRow, 'Satuan Barang');
        $sheet->setCellValue('J' . $headerRow, 'Status');

        $sheet->getStyle('A' . $headerRow . ':J' . $headerRow)->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN
                ]
            ]
        ]);

        $sheet->getColumnDimension('A')->setWidth(5);
        $sheet->getColumnDimension('B')->setWidth(20);
        $sheet->getColumnDimension('C')->setWidth(15);
        $sheet->getColumnDimension('D')->setWidth(20);
        $sheet->getColumnDimension('E')->setWidth(15);
        $sheet->getColumnDimension('F')->setWidth(25);
        $sheet->getColumnDimension('G')->setWidth(20);
        $sheet->getColumnDimension('H')->setWidth(20);
        $sheet->getColumnDimension('I')->setWidth(15);
        $sheet->getColumnDimension('J')->setWidth(20);

        $rowNumber = $headerRow + 1;
        foreach ($this->data as $index => $item) {
            $sheet->setCellValue('A' . $rowNumber, $index + 1);
            $sheet->setCellValue('B' . $rowNumber, $item->logistic_number);
            $sheet->setCellValue('C' . $rowNumber, $item->logistic_date);
            $sheet->setCellValue('D' . $rowNumber, $item->room_name);
            $sheet->setCellValue('E' . $rowNumber, $item->asset_entry_code);
            $sheet->setCellValue('F' . $rowNumber, $item->asset_name);
            $sheet->setCellValue('G' . $rowNumber, $item->request_quantity);
            $sheet->setCellValue('H' . $rowNumber, ($item->logistic_id) ? $item->quantity : "");
            $sheet->setCellValue('I' . $rowNumber, $item->uom_name);
            $sheet->setCellValue('J' . $rowNumber, ($item->logistic_id) ? "Barang Keluar" : "Menunggu Realisasi");

            $sheet->getStyle('A' . $rowNumber . ':J' . $rowNumber)->applyFromArray([
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN
                    ]
                ]
            ]);

            // Set right alignment for quantity columns
            $sheet->getStyle('G' . $rowNumber . ':H' . $rowNumber)->getAlignment()
                ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);

            $rowNumber++;
        }
    }
}
