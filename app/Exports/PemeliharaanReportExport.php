<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class PemeliharaanReportExport implements  WithStyles, WithTitle
{
    protected $data;
    protected $params;

    public function __construct($data, $params)
    {
        $this->data = $data;
        $this->params = $params;
    }


    public function title(): string
    {
        return 'Laporan Pemeliharaan';
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setCellValue('A1', 'Laporan Dibuat: ' . date('Y-m-d : H:i:s'));
        $sheet->setCellValue('A3', 'DAFTAR HASIL PEMELIHARAAN');
        $sheet->setCellValue('A4', 'Periode ' . $this->params['start_date'] . ' - ' . $this->params['end_date']);
        $sheet->setCellValue('A5', 'Tahun ' . date('Y', strtotime($this->params['end_date'])));
        $sheet->setCellValue('A6', '' . ($this->params['maintenance_category'] == 'all' ? 'Alat Kesehatan & Non Alat Kesehatan' : ($this->params['maintenance_category'] == 'ALKES' ? 'Alat Kesehatan' : 'Non Alat Kesehatan')));
        $sheet->setCellValue('A7', '' . ($this->params['maintenance_type'] ? $this->params['maintenance_type'] : 'Semua Kategori'));

        $sheet->setCellValue('A9', 'PROVINSI');
        $sheet->setCellValue('B9', ': '.config('app.hospital_province'));
        $sheet->setCellValue('A10', 'SKPD');
        $sheet->setCellValue('B10', ': '.config('app.report_header_hospital'));

        $sheet->mergeCells('A1:H1');
        $sheet->mergeCells('A3:H3');
        $sheet->mergeCells('A4:H4');
        $sheet->mergeCells('A5:H5');
        $sheet->mergeCells('A6:H6');
        $sheet->mergeCells('A7:H7');

        $sheet->getStyle('A1')->getFont()->setItalic(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('A3')->getFont()->setBold(true)->setSize(17);
        $sheet->getStyle('A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A4')->getFont()->setBold(true)->setSize(17);
        $sheet->getStyle('A4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A5')->getFont()->setBold(true)->setSize(17);
        $sheet->getStyle('A5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A6')->getFont()->setBold(true)->setSize(17);
        $sheet->getStyle('A6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A7')->getFont()->setBold(true)->setSize(17);
        $sheet->getStyle('A7')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A9')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('B9')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A10')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('B10')->getFont()->setBold(true)->setSize(12);

        $sheet->getColumnDimension('A')->setWidth(25);
        $sheet->getColumnDimension('B')->setWidth(40);
        $sheet->getColumnDimension('C')->setWidth(30);
        $sheet->getColumnDimension('D')->setWidth(30);
        $sheet->getColumnDimension('E')->setWidth(30);
        $sheet->getColumnDimension('F')->setWidth(30);
        $sheet->getColumnDimension('G')->setWidth(30);
        $sheet->getColumnDimension('H')->setWidth(30);
        $sheet->getColumnDimension('I')->setWidth(30);

        $sheet->setCellValue('A13', 'Kode Barang');
        $sheet->setCellValue('B13', 'Nama Barang');
        $sheet->setCellValue('C13', 'Jenis Pemeliharaan');
        $sheet->setCellValue('D13', 'Pemelihara');
        $sheet->setCellValue('E13', 'Tgl Pemeliharaan');
        $sheet->setCellValue('F13', 'Biaya Pemeliharaan');
        $sheet->setCellValue('G13', 'Bukti Pemeliharaan');
        $sheet->setCellValue('H13', 'Keterangan');

        $sheet->getStyle('A13:H13')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A13:H13')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A13:H13')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $row = 14;
        $totalActivityCost = 0;
        foreach ($this->data as $item) {
            $sheet->setCellValue('A' . $row, $item->item_code);
            $sheet->setCellValue('B' . $row, $item->item_name);
            $sheet->setCellValue('C' . $row, $item->activity_name);
            $sheet->setCellValue('D' . $row, $item->distributor_name);
            $sheet->setCellValue('E' . $row, $item->activity_date);
            $sheet->setCellValue('F' . $row, number_format($item->activity_cost, 0, ',', '.'));
            $sheet->setCellValue('G' . $row, $item->document_name);
            $sheet->setCellValue('H' . $row, $item->notes);

            $sheet->getStyle('A' . $row . ':H' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
            $row++;
            $totalActivityCost += $item->activity_cost;
        }

        $sheet->setCellValue('A' . $row, 'TOTAL');
        $sheet->setCellValue('F' . $row, number_format($totalActivityCost, 0, ',', '.'));
        $sheet->getStyle('A' . $row . ':H' . $row)->getBorders()->getOutline()->setBorderStyle(Border::BORDER_THICK);
    }
}
