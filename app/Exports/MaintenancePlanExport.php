<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class MaintenancePlanExport implements WithTitle, WithStyles
{
    protected $plan;

    public function __construct($plan)
    {
        $this->plan = $plan;
    }

    public function title(): string
    {
        return 'Detail Pemeliharaan';
    }

    public function styles(Worksheet $sheet)
    {
        // Header
        $sheet->mergeCells('A1:N1');
        $sheet->setCellValue('A1', 'USULAN RENCANA KEBUTUHAN PEMELIHARAAN BARANG MILIK DAERAH');
        $sheet->mergeCells('A2:N2');
        $sheet->setCellValue('A2', '(RENCANA PEMELIHARAAN)');
        $sheet->mergeCells('A3:N3');
        $sheet->setCellValue('A3', config('app.report_header_hospital'));
        $sheet->mergeCells('A4:N4');
        $sheet->setCellValue('A4', 'TAHUN ' . date('Y', strtotime($this->plan->maintenance_plan_date)));

        // Style Header
        $sheet->getStyle('A1:A4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A2:A4')->getFont()->setBold(true)->setSize(11);

        // Informasi Tambahan
        $sheet->setCellValue('A6', '');
        $sheet->setCellValue('A7', '');

        // Header Tabel
        $sheet->setCellValue('A9', 'No');
        $sheet->setCellValue('B9', 'Program / Kegiatan / Output');

        // Barang Yang Dipelihara
        $sheet->mergeCells('C9:H9');
        $sheet->setCellValue('C9', 'Barang Yang Dipelihara');
        $sheet->setCellValue('C10', 'Kode Barang');
        $sheet->setCellValue('D10', 'Nama Barang');
        $sheet->setCellValue('E10', 'Jumlah');
        $sheet->setCellValue('F10', 'Satuan');
        $sheet->setCellValue('G10', 'Status Barang');

        // Kondisi Barang
        $sheet->mergeCells('H10:J10');
        $sheet->setCellValue('H10', 'Kondisi Barang');
        $sheet->setCellValue('H11', 'B');
        $sheet->setCellValue('I11', 'RR');
        $sheet->setCellValue('J11', 'RB');

        // Usulan Kebutuhan Pemeliharaan
        $sheet->mergeCells('K9:L9');
        $sheet->setCellValue('K9', 'Usulan Kebutuhan Pemeliharaan');
        $sheet->setCellValue('K10', 'Nama Pemeliharaan');
        $sheet->setCellValue('L10', 'Jumlah');
        $sheet->setCellValue('M10', 'Satuan');

        // Keterangan
        $sheet->setCellValue('N9', 'Ket');
        $sheet->mergeCells('N9:N11');

        // Merge cells yang perlu di-merge
        $sheet->mergeCells('A9:A11');
        $sheet->mergeCells('B9:B11');
        $sheet->mergeCells('C10:C11');
        $sheet->mergeCells('D10:D11');
        $sheet->mergeCells('E10:E11');
        $sheet->mergeCells('F10:F11');
        $sheet->mergeCells('G10:G11');

        // Style Header Tabel
        $sheet->getStyle('A9:N11')->getFont()->setBold(true);
        $sheet->getStyle('A9:N11')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A9:N11')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle('A9:N11')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        // Set Column Width
        $sheet->getColumnDimension('A')->setWidth(5);
        $sheet->getColumnDimension('B')->setWidth(40);
        $sheet->getColumnDimension('C')->setWidth(15);
        $sheet->getColumnDimension('D')->setWidth(30);
        $sheet->getColumnDimension('E')->setWidth(10);
        $sheet->getColumnDimension('F')->setWidth(10);
        $sheet->getColumnDimension('G')->setWidth(15);
        $sheet->getColumnDimension('H')->setWidth(5);
        $sheet->getColumnDimension('I')->setWidth(5);
        $sheet->getColumnDimension('J')->setWidth(5);
        $sheet->getColumnDimension('K')->setWidth(30);
        $sheet->getColumnDimension('L')->setWidth(10);
        $sheet->getColumnDimension('M')->setWidth(10);
        $sheet->getColumnDimension('N')->setWidth(15);

        // Write data
        $row = 12;
        $no = 1;
        $currentProgram = null;

        foreach ($this->plan->maintenancePlanDetails as $detail) {
            $programs = $detail->maintenancePlanDetailPrograms->sortBy('level');

            // Write program hierarchy if different from current
            $programStructure = $programs->pluck('program_name')->implode(' / ');
            if ($programStructure != $currentProgram) {
                foreach ($programs as $program) {
                    $sheet->setCellValue('B' . $row, $program->program_code . ' - ' . $program->program_name);
                    $sheet->getStyle('A' . $row . ':N' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
                    $row++;
                }
                $currentProgram = $programStructure;
            }

            // Write detail data
            $sheet->setCellValue('A' . $row, $no);
            $sheet->setCellValue('C' . $row, $detail->code);
            $sheet->setCellValue('D' . $row, $detail->name);
            $sheet->setCellValue('E' . $row, $detail->quantity);
            $sheet->setCellValue('F' . $row, $detail->uom_name);

            // Set kondisi in the appropriate column
            if ($detail->condition == 'BAIK') {
                $sheet->setCellValue('H' . $row, '√');
            } elseif ($detail->condition == 'RUSAK_RINGAN') {
                $sheet->setCellValue('I' . $row, '√');
            } elseif ($detail->condition == 'RUSAK_BERAT') {
                $sheet->setCellValue('J' . $row, '√');
            }

            $sheet->setCellValue('K' . $row, $detail->maintenanceCategory->maintenance_category_name);
            $sheet->setCellValue('L' . $row, $detail->quantity);
            $sheet->setCellValue('M' . $row, $detail->uom_name);

            // Style row
            $sheet->getStyle('A' . $row . ':N' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
            $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle('E' . $row . ':J' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle('L' . $row . ':M' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

            $row++;
            $no++;
        }

        return $sheet;
    }
}
