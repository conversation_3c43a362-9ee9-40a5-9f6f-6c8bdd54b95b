<?php

namespace App\Exports;

use App\Models\Employee;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class LogisticRecapExport implements FromCollection, WithTitle, WithStyles
{
    protected $data;
    protected $params;

    public function __construct($data, $params)
    {
        $this->data = $data;
        $this->params = $params;
    }

    public function collection()
    {
        // Return empty collection to prevent data being added automatically
        return new Collection();
    }

    public function title(): string
    {
        return 'Rekap Stock Opname';
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setCellValue('A1', 'Laporan Dibuat: ' . date('Y-m-d H:i:s'));
        $sheet->setCellValue('A3', config('app.report_header_hospital'));
        $sheet->setCellValue('A4', 'PERIODE: ' . $this->params['period_label']);

        // Tambahkan baris untuk jenis laporan
        $jenisLaporan = $this->params['logistic_type'] === 'all' ? 'Seluruh Data Logistik' :
            ($this->params['logistic_type'] === 'internal' ? 'Logistik' : 'Non Logistik');
        $sheet->setCellValue('A5', 'JENIS LAPORAN: ' . $jenisLaporan);

        $sheet->mergeCells('A1:D1');
        $sheet->mergeCells('A3:D3');
        $sheet->mergeCells('A4:D4');
        $sheet->mergeCells('A5:D5');  // Merge cell untuk jenis laporan

        $sheet->getStyle('A1')->getFont()->setItalic(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('A3:A5')->getFont()->setBold(true)->setSize(14);  // Include A5 in styling
        $sheet->getStyle('A3:A5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Header
        $headerRow = 7;
        $headers = ['No', 'Jenis Barang', 'Jumlah Harga', 'Keterangan'];
        foreach ($headers as $key => $header) {
            $sheet->setCellValue(chr(65 + $key) . $headerRow, $header);
        }

        $sheet->getStyle('A' . $headerRow . ':D' . $headerRow)->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER
            ],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN]
            ]
        ]);

        // Data
        $row = $headerRow + 1;
        $totalPrice = 0;

        foreach ($this->data as $index => $item) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $item->jenis_barang);
            $sheet->setCellValue('C' . $row, $item->jumlah_harga ? number_format($item->jumlah_harga, 2, ',', '.') : 0);
            $sheet->setCellValue('D' . $row, '-');

            // Add borders
            $sheet->getStyle('A' . $row . ':D' . $row)->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => Border::BORDER_THIN]
                ]
            ]);

            // Center align No and Keterangan columns
            $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle('D' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

            // Right align Jumlah Harga column
            $sheet->getStyle('C' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            $totalPrice += $item->jumlah_harga ?? 0;
            $row++;
        }

        // Total row
        $totalRow = $row;
        $sheet->setCellValue('A' . $totalRow, '');
        $sheet->setCellValue('B' . $totalRow, 'Total');
        $sheet->setCellValue('C' . $totalRow, number_format($totalPrice, 2, ',', '.'));
        $sheet->setCellValue('D' . $totalRow, '');

        $sheet->getStyle('A' . $totalRow . ':D' . $totalRow)->applyFromArray([
            'font' => ['bold' => true],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN]
            ]
        ]);

        $sheet->getStyle('C' . $totalRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        // Setelah total row, tambahkan tanda tangan
        $signRow = $totalRow + 2;


        $leftParty = Employee::where("pic_type", "KETUA_TEAM_ASET")->first();
        $rightParty = Employee::where("pic_type", "PIC_PENGURUS_BARANG")->first();
        $bottomParty = Employee::where("pic_type", "KEPALA_PERENCANAAN_PROGRAM_ASET")->first();


        // Kota dan tanggal
        $sheet->setCellValue('C' . $signRow, 'PONTIANAK, ' . Carbon::now()->translatedFormat('d F Y'));
        $sheet->mergeCells('C' . $signRow . ':D' . $signRow);
        $sheet->getStyle('C' . $signRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Judul laporan
        $signRow += 2;
        $sheet->setCellValue('A' . $signRow, 'LAPORAN INI TELAH DIKETAHUI / DISETUJUI :');
        $sheet->mergeCells('A' . $signRow . ':D' . $signRow);
        $sheet->getStyle('A' . $signRow)->getFont()->setBold(true);
        $sheet->getStyle('A' . $signRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Jabatan kiri dan kanan
        $signRow += 2;
        $sheet->setCellValue('A' . $signRow, 'KA TIM ASET');
        $sheet->mergeCells('A' . $signRow . ':B' . $signRow);

        $sheet->setCellValue('C' . $signRow, 'PENGURUS BARANG PENGGUNA');
        $sheet->mergeCells('C' . $signRow . ':D' . $signRow);

        // Nama dan NIP kiri
        $signRow += 5;
        $sheet->setCellValue('A' . $signRow, $leftParty->employee_name);
        $sheet->mergeCells('A' . $signRow . ':B' . $signRow);
        $sheet->getStyle('A' . $signRow)->getFont()->setBold(true)->setUnderline(true);

        $signRow += 1;
        $sheet->setCellValue('A' . $signRow, "'" . $leftParty->employee_identification_number);
        $sheet->mergeCells('A' . $signRow . ':B' . $signRow);
        $sheet->getStyle('A' . $signRow)->getNumberFormat()->setFormatCode('@');

        // Nama dan NIP kanan
        $signRow -= 1;
        $sheet->setCellValue('C' . $signRow, $rightParty->employee_name);
        $sheet->mergeCells('C' . $signRow . ':D' . $signRow);
        $sheet->getStyle('C' . $signRow)->getFont()->setBold(true)->setUnderline(true);

        $signRow += 1;
        $sheet->setCellValue('C' . $signRow, "'" . $rightParty->employee_identification_number);
        $sheet->mergeCells('C' . $signRow . ':D' . $signRow);
        $sheet->getStyle('C' . $signRow)->getNumberFormat()->setFormatCode('@');

        // Jabatan tengah
        $signRow += 2;
        $sheet->setCellValue('A' . $signRow, 'KEPALA BAGIAN PERENCANAAN PROGRAM DAN ASET');
        $sheet->mergeCells('A' . $signRow . ':D' . $signRow);
        $sheet->getStyle('A' . $signRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Nama dan NIP tengah
        $signRow += 5;
        $sheet->setCellValue('A' . $signRow, $bottomParty->employee_name);
        $sheet->mergeCells('A' . $signRow . ':D' . $signRow);
        $sheet->getStyle('A' . $signRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A' . $signRow)->getFont()->setBold(true)->setUnderline(true);

        $signRow += 1;
        $sheet->setCellValue('A' . $signRow, "          '" . $bottomParty->employee_identification_number);
        $sheet->mergeCells('A' . $signRow . ':D' . $signRow);
        $sheet->getStyle('A' . $signRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A' . $signRow)->getNumberFormat()->setFormatCode('@');

        // Set column widths
        $sheet->getColumnDimension('A')->setWidth(5);  // No
        $sheet->getColumnDimension('B')->setWidth(40); // Jenis Barang
        $sheet->getColumnDimension('C')->setWidth(20); // Jumlah Harga
        $sheet->getColumnDimension('D')->setWidth(15); // Keterangan

        return $sheet;
    }
}
