<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StockOpnameExport implements FromCollection, WithTitle, WithStyles
{
    protected $data;
    protected $params;

    public function __construct($data, $params)
    {
        $this->data = $data;
        $this->params = $params;
    }

    public function collection()
    {
        // Return empty collection to prevent data being added automatically
        return new Collection();
    }

    public function title(): string
    {
        return 'Stock Opname';
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setCellValue('A1', 'Laporan Dibuat: ' . date('Y-m-d H:i:s'));
        $sheet->setCellValue('A3', config('app.report_header_hospital'));
        $sheet->setCellValue('A4', 'PERIODE: ' . $this->params['start'] . ' - ' . $this->params['end']);

        if (isset($this->params['stock_recap_name']) && $this->params['stock_recap_name']) {
            $sheet->setCellValue('A5', 'JENIS BARANG: ' . $this->params['stock_recap_name']);
            $sheet->mergeCells('A5:J5');
            $sheet->getStyle('A5')->getFont()->setBold(true)->setSize(14);
            $sheet->getStyle('A5')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            $headerRow = 7;
        } else {
            $headerRow = 6;
        }

        $sheet->mergeCells('A1:J1');
        $sheet->mergeCells('A3:J3');
        $sheet->mergeCells('A4:J4');

        $sheet->getStyle('A1')->getFont()->setItalic(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('A3:A4')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A3:A4')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

        $headers = [
            'No', 'Kode Logistik', 'Nama Barang', 'Tahun', 'Saldo Awal',
            'Total Masuk', 'Total Keluar', 'Saldo Akhir', 'Harga Satuan', 'Jumlah Harga'
        ];

        foreach ($headers as $key => $header) {
            $sheet->setCellValue(chr(65 + $key) . $headerRow, $header);
        }

        $sheet->getStyle('A' . $headerRow . ':J' . $headerRow)->applyFromArray([
            'font' => ['bold' => true],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
            ],
            'borders' => [
                'allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]
            ]
        ]);

        $row = $headerRow + 1;
        $totalSaldoAwal = 0;
        $totalMasuk = 0;
        $totalKeluar = 0;
        $totalSaldoAkhir = 0;
        $totalNilai = 0;

        foreach ($this->data as $index => $item) {
            $saldoAkhir = $item->qty_saldo_awal_asset + $item->qty_asset_masuk - $item->qty_asset_keluar;
            $nilaiTotal = $item->unit_price * $saldoAkhir;

            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $item->qr_code);
            $sheet->setCellValue('C' . $row, $item->asset_name);
            $sheet->setCellValue('D' . $row, $item->tahun_terakhir ?? '-');
            $sheet->setCellValue('E' . $row, $item->qty_saldo_awal_asset);
            $sheet->setCellValue('F' . $row, $item->qty_asset_masuk);
            $sheet->setCellValue('G' . $row, $item->qty_asset_keluar);
            $sheet->setCellValue('H' . $row, $saldoAkhir);
            $sheet->setCellValue('I' . $row, $item->unit_price);
            $sheet->setCellValue('J' . $row, $nilaiTotal);

            // Add borders
            $sheet->getStyle('A' . $row . ':J' . $row)->applyFromArray([
                'borders' => [
                    'allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]
                ]
            ]);

            // Right align numeric columns
            $sheet->getStyle('E' . $row . ':J' . $row)->getAlignment()
                ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);

            // Update totals
            $totalSaldoAwal += $item->qty_saldo_awal_asset;
            $totalMasuk += $item->qty_asset_masuk;
            $totalKeluar += $item->qty_asset_keluar;
            $totalSaldoAkhir += $saldoAkhir;
            $totalNilai += $nilaiTotal;

            $row++;
        }

        // Add total row
        $totalRow = $row;
        $sheet->setCellValue('A' . $totalRow, 'Total');
        $sheet->mergeCells('A' . $totalRow . ':D' . $totalRow);
        $sheet->setCellValue('E' . $totalRow, $totalSaldoAwal);
        $sheet->setCellValue('F' . $totalRow, $totalMasuk);
        $sheet->setCellValue('G' . $totalRow, $totalKeluar);
        $sheet->setCellValue('H' . $totalRow, $totalSaldoAkhir);
        $sheet->setCellValue('I' . $totalRow, '-');
        $sheet->setCellValue('J' . $totalRow, $totalNilai);

        // Style total row
        $sheet->getStyle('A' . $totalRow . ':J' . $totalRow)->applyFromArray([
            'font' => ['bold' => true],
            'borders' => [
                'allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]
            ]
        ]);

        $sheet->getStyle('E' . $totalRow . ':J' . $totalRow)->getAlignment()
            ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);

        // Format columns
        $sheet->getColumnDimension('A')->setWidth(5);
        $sheet->getColumnDimension('B')->setWidth(15);
        $sheet->getColumnDimension('C')->setWidth(30);
        $sheet->getColumnDimension('D')->setWidth(10);
        $sheet->getColumnDimension('E')->setWidth(12);
        $sheet->getColumnDimension('F')->setWidth(12);
        $sheet->getColumnDimension('G')->setWidth(12);
        $sheet->getColumnDimension('H')->setWidth(12);
        $sheet->getColumnDimension('I')->setWidth(15);
        $sheet->getColumnDimension('J')->setWidth(20);

        return $sheet;
    }
}
