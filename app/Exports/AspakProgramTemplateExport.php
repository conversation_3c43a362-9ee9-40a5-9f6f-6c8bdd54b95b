<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class AspakProgramTemplateExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    /**
     * Data for export
     *
     * @return array
     */
    public function array(): array
    {
        return [
            [
                '1.02.01',
                'PROGRAM PENUNJANG URUSAN PEMERINTAHAN DAERAH PROVINSI',
                'PROGRAM',
                '',
                'BRANCH'
            ],
            [
                '1.02.01.1.01',
                'PERENCANAAN, PENGANGGARAN, DAN EVALUASI KINERJA PERANGKAT DAERAH',
                'ACTIVITY',
                '1.02.01',
                'BRANCH'
            ],
            [
                '1.02.01.1.01.0001',
                'Penyusunan Dokumen Perencanaan Perangkat Daerah',
                'SUB_ACTIVITY',
                '1.02.01.1.01',
                'LEAF'
            ],
            [
                '1.02.01.1.01.0002',
                'Koordinasi dan Penyusunan Dokumen RKA-SKPD',
                'SUB_ACTIVITY',
                '1.02.01.1.01',
                'LEAF'
            ],
            [
                '1.02.01.1.01.0003',
                'Koordinasi dan Penyusunan Dokumen Perubahan RKA-SKPD',
                'SUB_ACTIVITY',
                '1.02.01.1.01',
                'LEAF'
            ],
            [
                '1.02.01.1.02',
                'ADMINISTRASI KEUANGAN PERANGKAT DAERAH',
                'ACTIVITY',
                '1.02.01',
                'BRANCH'
            ],
            [
                '1.02.01.1.02.0001',
                'Penyediaan Gaji dan Tunjangan ASN',
                'SUB_ACTIVITY',
                '1.02.01.1.02',
                'LEAF'
            ],
            [
                '1.02.01.1.02.0002',
                'Penyediaan Administrasi Pelaksanaan Tugas ASN',
                'SUB_ACTIVITY',
                '1.02.01.1.02',
                'LEAF'
            ],
            [
                '1.02.01.1.03',
                'ADMINISTRASI BARANG MILIK DAERAH PADA PERANGKAT DAERAH',
                'ACTIVITY',
                '1.02.01',
                'BRANCH'
            ],
            [
                '1.02.01.1.03.0001',
                'Penyusunan Perencanaan Kebutuhan Barang Milik Daerah SKPD',
                'SUB_ACTIVITY',
                '1.02.01.1.03',
                'LEAF'
            ]
        ];
    }

    /**
     * Define column headings
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            'Kode',
            'Nama',
            'Type',
            'Parent',
            'Tree'
        ];
    }

    /**
     * Apply styles to worksheet
     *
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Header styling
            1 => [
                'font' => [
                    'bold' => true,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E8E8E8'], // Light gray background
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15, // kode - adequate for alphanumeric codes
            'B' => 40, // nama - wider for longer service room names
            'C' => 18, // type - adequate for PROGRAM/ACTIVITY/SUB_ACTIVITY
            'D' => 15, // parent - same as kode for consistency
            'E' => 18, // tree - adequate for BRANCH/LEAF
        ];
    }

    public function title(): string
    {
        return 'Template Import ASPAK Program';
    }
}
