<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ConsumableProcurementExport implements WithTitle, WithStyles
{
    protected $plan;

    public function __construct($plan)
    {
        $this->plan = $plan;
    }

    public function title(): string
    {
        return 'Detail Perencanaan';
    }

    public function styles(Worksheet $sheet)
    {
        // Header
        $sheet->mergeCells('A1:R1');
        $sheet->setCellValue('A1', 'USULAN RENCANA KEBUTUHAN PENGADAAN BARANG MILIK DAERAH');
        $sheet->mergeCells('A2:R2');
        $sheet->setCellValue('A2', '(RENCANA PENGADAAN)');
        $sheet->mergeCells('A3:R3');
        $sheet->setCellValue('A3', config('app.report_header_hospital'));
        $sheet->mergeCells('A4:R4');
        $sheet->setCellValue('A4', 'TAHUN ' . date('Y', strtotime($this->plan->plan_date)));

        // Style Header
        $sheet->getStyle('A1:A4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A2:A4')->getFont()->setBold(true)->setSize(11);

        // Header Tabel
        $sheet->setCellValue('A6', 'No.');
        $sheet->setCellValue('B6', 'Program/Kegiatan/Output');

        // Usulan Barang Milik Daerah
        $sheet->mergeCells('C6:F6');
        $sheet->setCellValue('C6', 'Usulan Barang Milik Daerah');
        $sheet->setCellValue('C7', 'Kode Barang');
        $sheet->setCellValue('D7', 'Nama Barang');
        $sheet->setCellValue('E7', 'Jumlah');
        $sheet->setCellValue('F7', 'Satuan');

        // Kebutuhan Maksimum
        $sheet->mergeCells('G6:H6');
        $sheet->setCellValue('G6', 'Kebutuhan Maksimum');
        $sheet->setCellValue('G7', 'Jumlah');
        $sheet->setCellValue('H7', 'Satuan');

        // Data Barang Yang Dapat Dioptimalisasikan
        $sheet->mergeCells('I6:L6');
        $sheet->setCellValue('I6', 'Data Barang Yang Dapat Dioptimalisasikan');
        $sheet->setCellValue('I7', 'Kode Barang');
        $sheet->setCellValue('J7', 'Nama Barang');
        $sheet->setCellValue('K7', 'Jumlah');
        $sheet->setCellValue('L7', 'Satuan');

        // Kebutuhan Riil Barang Milik Daerah
        $sheet->mergeCells('M6:N6');
        $sheet->setCellValue('M6', 'Kebutuhan Riil Barang Milik Daerah');
        $sheet->setCellValue('M7', 'Jumlah');
        $sheet->setCellValue('N7', 'Satuan');

        // Rencana Kebutuhan BMD Yang Disetujui
        $sheet->mergeCells('O6:P6');
        $sheet->setCellValue('O6', 'Rencana Kebutuhan Pengadaan BMD Yang Disetujui');
        $sheet->setCellValue('O7', 'Jumlah');
        $sheet->setCellValue('P7', 'Satuan');

        // Cara Pemenuhan dan Ket
        $sheet->setCellValue('Q6', 'Cara Pemenuhan');
        $sheet->setCellValue('R6', 'Ket');
        $sheet->mergeCells('Q6:Q7');
        $sheet->mergeCells('R6:R7');

        // Merge cells for headers that span 2 rows
        $sheet->mergeCells('A6:A7');
        $sheet->mergeCells('B6:B7');

        // Style Header Tabel
        $sheet->getStyle('A6:R7')->getFont()->setBold(true);
        $sheet->getStyle('A6:R7')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A6:R7')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle('A6:R7')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        // Set Column Width
        $sheet->getColumnDimension('A')->setWidth(5);
        $sheet->getColumnDimension('B')->setWidth(60);
        $sheet->getColumnDimension('C')->setWidth(15);
        $sheet->getColumnDimension('D')->setWidth(30);
        $sheet->getColumnDimension('E')->setWidth(10);
        $sheet->getColumnDimension('F')->setWidth(10);
        $sheet->getColumnDimension('G')->setWidth(10);
        $sheet->getColumnDimension('H')->setWidth(10);
        $sheet->getColumnDimension('I')->setWidth(15);
        $sheet->getColumnDimension('J')->setWidth(30);
        $sheet->getColumnDimension('K')->setWidth(10);
        $sheet->getColumnDimension('L')->setWidth(10);
        $sheet->getColumnDimension('M')->setWidth(10);
        $sheet->getColumnDimension('N')->setWidth(10);
        $sheet->getColumnDimension('O')->setWidth(10);
        $sheet->getColumnDimension('P')->setWidth(10);
        $sheet->getColumnDimension('Q')->setWidth(15);
        $sheet->getColumnDimension('R')->setWidth(15);

        // Group data by program hierarchy
        $groupedData = [];
        foreach ($this->plan->planDetails as $planDetail) {
            $groupPrograms = $planDetail->planDetailPrograms()
                ->where('program_type', 'GROUP')
                ->orderBy('level')
                ->get();

            $outputProgram = $planDetail->planDetailPrograms()
                ->where('program_type', 'OUTPUT')
                ->first();

            // Create a unique key for the program hierarchy
            $key = '';
            foreach ($groupPrograms as $group) {
                $key .= $group->program_code;
            }
            $key .= $outputProgram ? $outputProgram->program_code : '';

            if (!isset($groupedData[$key])) {
                $groupedData[$key] = [
                    'groups' => $groupPrograms,
                    'output' => $outputProgram,
                    'items' => []
                ];
            }

            $groupedData[$key]['items'][] = $planDetail;
        }

        // Write data
        $row = 8;
        $no = 1;
        foreach ($groupedData as $data) {
            // Write Group Programs (Level 1 & 2)
            foreach ($data['groups'] as $group) {
                $sheet->setCellValue('B' . $row, $group->program_code . ' - ' . $group->program_name);
                $sheet->getStyle('A' . $row . ':R' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
                $row++;
            }

            // Write Output Program
            if ($data['output']) {
                $sheet->setCellValue('B' . $row, $data['output']->program_code . ' - ' . $data['output']->program_name);
                $sheet->getStyle('A' . $row . ':R' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
                $row++;
            }

            // Write items under this program
            foreach ($data['items'] as $planDetail) {
                $sheet->setCellValue('A' . $row, $no);

                // Usulan Barang
                $sheet->setCellValue('C' . $row, $planDetail->code ?? '-');
                $sheet->setCellValue('D' . $row, $planDetail->name);
                $sheet->setCellValue('E' . $row, $planDetail->quantity);
                $sheet->setCellValue('F' . $row, $planDetail->uom_name);

                // Kebutuhan Maksimum
                $sheet->setCellValue('G' . $row, $planDetail->quantity);
                $sheet->setCellValue('H' . $row, $planDetail->uom_name);

                // Data Barang Yang Dapat Dioptimalisasikan
                $sheet->setCellValue('I' . $row, $planDetail->code ?? '-');
                $sheet->setCellValue('J' . $row, $planDetail->name);
                $sheet->setCellValue('K' . $row, $planDetail->quantity);
                $sheet->setCellValue('L' . $row, $planDetail->uom_name);

                // Kebutuhan Riil
                $sheet->setCellValue('M' . $row, $planDetail->quantity);
                $sheet->setCellValue('N' . $row, $planDetail->uom_name);

                // Style
                $sheet->getStyle('A' . $row . ':R' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
                $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
                $sheet->getStyle('E' . $row . ':R' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

                $row++;
                $no++;
            }
        }

        // Set text wrapping for program column
        $sheet->getStyle('B8:B' . $row)->getAlignment()->setWrapText(true);
    }
}
