<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class CategoryTemplateExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle
{
    public function array(): array
    {
        return [
            [
                '********',
                'Tanah',
                'INFRASTRUCTURE',
                'NON_ALAT_KESEHATAN',
                ''
            ],
            [
                '********',
                'Alat Kesehatan',
                'EQUIPMENT',
                'ALAT_KESEHATAN',
                '43'
            ],
            [
                '********',
                'Bangunan dan Gedung Permanen dan Semi <PERSON>manen',
                'INFRASTRUCTURE',
                'NON_ALAT_KESEHATAN',
                '43'
            ],
            [
                '********',
                'Hewan',
                'OTHER',
                'NON_ALAT_KESEHATAN',
                ''
            ],
            [
                '********',
                'Barang Habis Pakai',
                'LOGISTIC',
                'NON_ALAT_KESEHATAN',
                ''
            ]
        ];
    }

    public function headings(): array
    {
        return [
            'kode_kategori' => 'Kode Kategori',
            'nama_kategori' => 'Nama Kategori',
            'tipe_kategori' => 'Tipe Kategori',
            'sub_tipe_kategori' => 'Sub Tipe Kategori',
            'pic_kategori' => 'PIC Kategori'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Set row height for better spacing
        $sheet->getRowDimension('1')->setRowHeight(25);
        $sheet->getRowDimension('2')->setRowHeight(20);
        $sheet->getRowDimension('3')->setRowHeight(20);
        $sheet->getRowDimension('4')->setRowHeight(20);

        return [
            // Professional header styling
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                    'color' => ['rgb' => 'FFFFFF'],
                    'name' => 'Calibri',
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '2C3E50'], // Professional dark navy
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_MEDIUM,
                        'color' => ['rgb' => '34495E'],
                    ],
                ],
            ],
            // Alternating row colors for better readability
            2 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFFFFF'], // White
                ],
                'font' => [
                    'size' => 11,
                    'name' => 'Calibri',
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D5D8DC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            3 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F8F9FA'], // Light gray
                ],
                'font' => [
                    'size' => 11,
                    'name' => 'Calibri',
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D5D8DC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            4 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFFFFF'], // White
                ],
                'font' => [
                    'size' => 11,
                    'name' => 'Calibri',
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D5D8DC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 18, // kode_kategori - wider for better readability
            'B' => 30, // nama_kategori - wider for longer names
            'C' => 22, // tipe_kategori - professional spacing
            'D' => 25, // sub_tipe_kategori - accommodate longer text
            'E' => 16, // pic_kategori - adequate for ID numbers
        ];
    }

    public function title(): string
    {
        return 'Template Import Kategori';
    }
}
