<?php

namespace App\Exports;

use App\Models\Division;
use App\Models\Employee;
use App\Models\RoomCategory;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class RoomTemplateExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle, WithColumnFormatting
{
    public function array(): array
    {
        // Ambil data dari database untuk contoh
        $roomCategories = RoomCategory::pluck('room_category_code')->take(5)->toArray();
        $divisions = Division::pluck('id')->take(5)->toArray();
        
        // Generate kode penanggung jawab menggunakan faker
        $employees = [];
        for ($i = 1; $i <= 5; $i++) {
            $employees[] = 'EMP' . str_pad(fake('id_ID')->numberBetween(1, 999), 3, '0', STR_PAD_LEFT);
        }

        // Pastikan ada data minimal
        $roomCategories = array_pad($roomCategories, 5, 'DEFAULT_CAT');
        $divisions = array_pad($divisions, 5, '1');

        $roomNames = [
            'Ruang Operasi',
            'Ruang ICU',
            'Ruang Rawat Inap',
            'Ruang Emergency',
            'Ruang Laboratorium'
        ];

        $buildingNames = [
            'Gedung Utama',
            'Gedung A',
            'Gedung B',
            'Gedung Rawat Inap',
            'Gedung Penunjang'
        ];

        return [
            [
                "ROOM_001",
                $roomNames[0] . ' ' . fake("id_ID")->randomNumber(2),
                $buildingNames[0],
                $roomCategories[0] ?? 'DEFAULT_CAT',
                $employees[0],
                $divisions[0] ?? '1'
            ],
            [
                "ROOM_002",
                $roomNames[1] . ' ' . fake("id_ID")->randomNumber(2),
                $buildingNames[1],
                $roomCategories[1] ?? 'DEFAULT_CAT',
                $employees[1],
                $divisions[1] ?? '2'
            ],
            [
                "ROOM_003",
                $roomNames[2] . ' ' . fake("id_ID")->randomNumber(2),
                $buildingNames[2],
                $roomCategories[2] ?? 'DEFAULT_CAT',
                $employees[2],
                $divisions[2] ?? '3'
            ],
            [
                "ROOM_004",
                $roomNames[3] . ' ' . fake("id_ID")->randomNumber(2),
                $buildingNames[3],
                $roomCategories[3] ?? 'DEFAULT_CAT',
                $employees[3],
                $divisions[3] ?? '4'
            ],
            [
                "ROOM_005",
                $roomNames[4] . ' ' . fake("id_ID")->randomNumber(2),
                $buildingNames[4],
                $roomCategories[4] ?? 'DEFAULT_CAT',
                $employees[4],
                $divisions[4] ?? '5'
            ]
        ];
    }

    public function headings(): array
    {
        return [
            'Kode Ruangan',
            'Nama Ruangan',
            'Nama Gedung',
            'Kategori Ruangan',
            'Penanggung Jawab',
            'Divisi'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Set row height for better spacing
        $sheet->getRowDimension('1')->setRowHeight(25);
        $sheet->getRowDimension('2')->setRowHeight(20);
        $sheet->getRowDimension('3')->setRowHeight(20);
        $sheet->getRowDimension('4')->setRowHeight(20);
        $sheet->getRowDimension('5')->setRowHeight(20);
        $sheet->getRowDimension('6')->setRowHeight(20);

        return [
            // Professional header styling
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                    'color' => ['rgb' => 'FFFFFF'],
                    'name' => 'Calibri',
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '2C3E50'], // Professional dark navy
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_MEDIUM,
                        'color' => ['rgb' => '34495E'],
                    ],
                ],
            ],
            // Alternating row colors for better readability
            2 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFFFFF'], // White
                ],
                'font' => [
                    'size' => 11,
                    'name' => 'Calibri',
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D5D8DC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            3 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F8F9FA'], // Light gray
                ],
                'font' => [
                    'size' => 11,
                    'name' => 'Calibri',
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D5D8DC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            4 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFFFFF'], // White
                ],
                'font' => [
                    'size' => 11,
                    'name' => 'Calibri',
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D5D8DC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            5 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F8F9FA'], // Light gray
                ],
                'font' => [
                    'size' => 11,
                    'name' => 'Calibri',
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D5D8DC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            6 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFFFFF'], // White
                ],
                'font' => [
                    'size' => 11,
                    'name' => 'Calibri',
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D5D8DC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 20, // kode_ruangan - wider for room identification codes
            'B' => 30, // nama_ruangan - wider for longer room names
            'C' => 25, // nama_gedung - adequate spacing for building names
            'D' => 20, // kategori_ruangan - adequate for category codes
            'E' => 25, // penanggung_jawab - adequate for employee identification
            'F' => 15, // divisi - adequate for division IDs
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT, // Format kode_ruangan sebagai text
            'E' => NumberFormat::FORMAT_TEXT, // Format penanggung_jawab sebagai text
        ];
    }

    public function title(): string
    {
        return 'Template Import Ruangan';
    }
}
