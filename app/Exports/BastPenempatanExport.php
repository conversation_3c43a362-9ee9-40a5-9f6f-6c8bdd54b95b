<?php

namespace App\Exports;

use App\Models\RegisterAsset;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class BastPenempatanExport implements FromCollection, WithHeadings
{
    protected $data;

    function __construct($data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        $assets = $this->data;
        $data = [];

        foreach ($assets as $asset) {
            $data[] = [
                "nama_barang" => $asset->barang->nama_barang,
                "kode_barang" => $asset->barang->kode_barang,
                "tag_rfid" => $asset->tag_rfid,
                "qr_code" => $asset->qr_code,
                "kode_register" => $asset->register_code,
            ];
        }

        return collect($data);
    }

    public function headings(): array
    {
        return [
            "Nama Barang",
            "Kode Barang",
            "Tag RFID",
            "QR Code",
            "Kode Register"
        ];
    }
}
