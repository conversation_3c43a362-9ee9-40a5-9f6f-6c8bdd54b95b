<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RequestDocument extends Model
{
    use HasFactory;
    protected $fillable = ["document_code", "document_type", "party1_id", "party1_name", "party1_identification_number", "party1_grade", "party1_position", "party2_id", "party2_name", "party2_identification_number", "party2_grade", "party2_position", "target_location", "created_by_name", "updated_by_name", "document_id"];

    function requestDocumentAssets(): HasMany
    {
        return $this->hasMany(RequestDocumentAsset::class);
    }

    function partyOne(): BelongsTo
    {
        return $this->belongsTo(Employee::class, "party1_id");
    }

    function partyTwo(): BelongsTo
    {
        return $this->belongsTo(Employee::class, "party2_id");
    }

    function targetLocation(): BelongsTo
    {
        return $this->belongsTo(Room::class, "target_location");
    }
}
