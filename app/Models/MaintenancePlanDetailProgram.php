<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MaintenancePlanDetailProgram extends Model
{
    use HasFactory;

    protected $table = 'maintenance_plan_detail_programs';

    protected $fillable = [	
        'maintenance_plan_detail_id',
        'program_type',
        'program_code',
        'program_name',
        'level'
    ];

    public $timestamps = false;

    public function maintenancePlanDetail(): BelongsTo
    {
        return $this->belongsTo(MaintenancePlanDetail::class, 'maintenance_plan_detail_id');
    }
} 
