<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MaintenancePlan extends Model
{
    use HasFactory;
    protected $fillable = [
        "room_id",
        "program_id",
        "maintenance_plan_number",
        "maintenance_plan_date",
        "maintenance_plan_title",
        "maintenance_plan_notes",
        "maintenance_plan_total_quantity",
        "maintenance_plan_document_path",
        "request_date",
        "requester_id",
        "requester_name",
        "requester_identification_number",
        "requester_grade",
        "requester_position",
        "approval_date",
        "approver_id",
        "approver_name",
        "approver_identification_number",
        "approver_grade",
        "approver_position",
        "created_at",
        "updated_at",
        "created_by",
        "updated_by",
        "created_by_name",
        "updated_by_name"
    ];

    function room(): BelongsTo
    {
        return $this->belongsTo(Room::class);
    }

    function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class);
    }

    function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }
    function maintenancePlanDetails()
    {
        return $this->hasMany(MaintenancePlanDetail::class);
    }

    function scopeFilterApprove($query)
    {
        if (request("status") ?? false) {
            if (request("status") == "approved") {
                return $query->whereNotNull('approver_id');
            }

            if (request("status") == "not-approved") {
                return $query->whereNull('approver_id');
            }
        }
    }
}
