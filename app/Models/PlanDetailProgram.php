<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PlanDetailProgram extends Model
{
    use HasFactory;

    /**
     * Menentukan nama tabel yang terkait dengan model ini
     *
     * @var string
     */
    protected $table = 'plan_detail_programs';

    /**
     * Atribut yang dapat diisi secara massal
     *
     * @var array
     */
    protected $fillable = [
        'plan_detail_id',
        'program_type',
        'program_code',
        'program_name',
        'level'
    ];

    /**
     * Menentukan apakah timestamps digunakan
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * Mendefinisikan relasi ke model PlanDetail
     *
     * @return BelongsTo
     */
    public function planDetail(): BelongsTo
    {
        return $this->belongsTo(PlanDetail::class, 'plan_detail_id');
    }
} 


/* 
CREATE TABLE `plan_detail_programs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `plan_detail_id` bigint DEFAULT NULL,
  `program_type` enum('GROUP','OUTPUT') DEFAULT NULL,
  `program_code` varchar(256) DEFAULT NULL,
  `program_name` varchar(256) DEFAULT NULL,
  `level` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `plan_detail_id` (`plan_detail_id`),
  CONSTRAINT `plan_detail_programs_ibfk_1` FOREIGN KEY (`plan_detail_id`) REFERENCES `plan_details` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
*/