<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DocumentAsset extends Model
{
    use HasFactory;
    public $timestamps = false;

    protected $fillable = [
        "request_document_id", 
        "asset_id"
    ];

    public function asset()
    {
        return $this->belongsTo(Asset::class);
    }

    public function document()
    {
        return $this->belongsTo(Document::class);
    }

    function documentAssetMedias()
    {
        return $this->hasMany(DocumentAssetMedia::class);
    }
}
