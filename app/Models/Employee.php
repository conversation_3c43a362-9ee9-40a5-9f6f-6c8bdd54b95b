<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Foundation\Auth\User as Authenticatable;

class Employee extends Authenticatable
{
    use HasFactory, HasRoles;

    protected $guard_name = 'employee';
    protected $fillable = ["employee_name", "employee_identification_number", "employee_grade", "employee_position", "pic_type", "active", "category", "role_id", "created_by", "updated_by", "created_by_name", "updated_by_name"];

    function rooms(): HasMany
    {
        return $this->hasMany(Room::class, "pic_room");
    }

    function scopeSearch($query)
    {
        if (request("q") ?? false) {
            $query->where("employee_name", "like", "%" . request("q") . "%")
                ->orWhere("employee_identification_number", "like", "%" . request("q") . "%");
        }
    }
}
