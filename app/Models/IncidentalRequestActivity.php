<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IncidentalRequestActivity extends Model
{
    use HasFactory;

    protected $table = "incidental_request_activities";
    public $timestamps = false;
    protected $fillable = [
        "incidental_request_id",
        "activity_status",
        "activity_name",
        "activity_notes",
        "activity_time",
        "activity_issue",
        "activity_document_path",
        "created_at",
        "created_by",
        "created_by_name"
    ];
}
