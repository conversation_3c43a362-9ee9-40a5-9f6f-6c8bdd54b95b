<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MaintenanceSchedule extends Model
{
    use HasFactory;

    protected $table = "maintenance_schedules";
    protected $fillable = ["schedule_code", "schedule_category", "schedule_type", "schedule_date", "schedule_notes", "schedule_quantity", "item_id", "created_by", "updated_by", "created_by_name", "updated_by_name"];

    public static function getMaintenanceScheduleQuery($category)
    {
        return self::select('maintenance_schedules.*', 'items.item_code', 'items.item_name')
            ->join("items", "items.id", "=", "maintenance_schedules.item_id")
            ->where("schedule_category", $category);
    }
}
