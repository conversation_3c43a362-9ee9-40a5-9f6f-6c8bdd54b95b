<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Room extends Model
{
    use HasFactory;
    protected $fillable = [
        "room_code", 
        "room_name", 
        "room_category", 
        "division_id", 
        "building_id", 
        "building_name", 
        "pic_room", 
        "room_type", 
        "device_id", 
        "active", 
        "created_by", 
        "updated_by", 
        "created_by_name", 
        "updated_by_name"
    ];

    protected $casts = [
        'active' => 'boolean',
    ];

    protected $attributes = [
        'active' => true,
    ];

    function assets(): HasMany
    {
        return $this->hasMany(Asset::class, "document_room_id");
    }

    function pic(): BelongsTo
    {
        return $this->belongsTo(Employee::class, "pic_room");
    }

    public function programs(): BelongsToMany
    {
        return $this->belongsToMany(Program::class, 'room_has_programs', 'room_id', 'program_id');
    }

    public function roomSubs(): HasMany
    {
        return $this->hasMany(RoomSub::class);
    }

    function scopeSearch($query)
    {
        if (request()->has("q")) {
            $query->where("room_name", "like", "%" . request("q") . "%")
                ->orWhere("room_code", "like", "%" . request("q") . "%");
        }
    }
}
