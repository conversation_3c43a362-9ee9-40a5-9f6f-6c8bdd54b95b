<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Document extends Model
{
    use HasFactory;
    protected $fillable = [
        "document_code",
        "document_type", // enum('CHECKLIST','PENEMPATAN','PEMBAYARAN','MUTASI','RUSAK')
        "document_sub_type", // enum('FAKTUR','SJ','COO','SERTIFIKAT','BA','REGISTER')
        "document_path", 
        "party1_id",
        "party1_name",
        "party1_identification_number",
        "party1_grade",
        "party1_position",
        "party2_id",
        "party2_name",
        "party2_identification_number",
        "party2_grade",
        "party2_position",
        "target_location",
        "created_by_name",
        "updated_by_name"
    ];

    function documentAsset(): HasMany
    {
        return $this->hasMany(DocumentAsset::class);
    }
}
