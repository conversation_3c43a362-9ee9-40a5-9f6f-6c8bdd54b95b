<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Logistic extends Model
{
    use HasFactory;

    protected $table = "logistics";
    protected $fillable = [
        "logistic_type",
        "logistic_category",
        "logistic_number",
        "room_id",
        "config_stock_field_id",
        "distributor_id",
        "logistic_date",
        "logistic_notes",
        "total_logistic_quantity",
        "total_logistic_price",
        "logistic_document_path",
        "request_date",
        "requester_id",
        "requester_name",
        "requester_identification_number",
        "requester_grade",
        "requester_position",
        "approval_date",
        "approver_id",
        "approver_name",
        "approver_identification_number",
        "approver_grade",
        "approver_position",
        "created_at",
        "updated_at",
        "created_by",
        "updated_by",
        "created_by_name",
        "updated_by_name"
    ];

    public static function getLogisticAdjustment()
    {
        $query = self::select('logistics.*', 'distributors.distributor_name', 'rooms.room_name', 'rooms.room_code')
            ->leftJoin('distributors', 'distributors.id', '=', 'logistics.distributor_id')
            ->leftJoin('rooms', 'rooms.id', '=', 'logistics.room_id')
            ->where("logistics.logistic_category", "!=", "TRANSACTION");

        return $query;
    }

    public static function getLogisticQuery($logisticType, $roomId)
    {
        $query = self::select('logistics.*', 'distributors.distributor_name', 'rooms.room_name', 'rooms.room_code')
            ->leftJoin('distributors', 'distributors.id', '=', 'logistics.distributor_id')
            ->leftJoin('rooms', 'rooms.id', '=', 'logistics.room_id')
            ->where("logistics.logistic_type", $logisticType)
            ->where("logistics.logistic_category", "TRANSACTION");

        if ($roomId) {
            $query->where("logistics.room_id", $roomId);
        }

        return $query;
    }

    public static function getLogisticQueryGroupByDay($logisticType, $roomId)
    {
        $query = self::select('logistics.*', 'distributors.distributor_name', 'rooms.room_name', 'rooms.room_code')
            ->leftJoin('distributors', 'distributors.id', '=', 'logistics.distributor_id')
            ->leftJoin('rooms', 'rooms.id', '=', 'logistics.room_id')
            ->where("logistics.logistic_type", $logisticType)
            ->where("logistics.logistic_category", "TRANSACTION");

        if ($roomId) {
            $query->where("logistics.room_id", $roomId);
        }

        $query->groupBy('logistics.room_id', 'logistics.logistic_date');

        return $query;
    }

    public static function listStockOpnameByAssetId($assetIds)
    {
        if (empty($assetIds)) {
            return collect([]);
        }

        $placeholders = implode(',', array_fill(0, count($assetIds), '?'));
        $sqlBalance = "
            SELECT a.id, a.asset_name, a.qr_code, a.asset_entry_id, a.unit_price,
                   IFNULL(b.total_in, 0) AS product_in,
                   IFNULL(c.total_out, 0) AS product_out,
                   IFNULL(IFNULL(b.total_in, 0) - IFNULL(c.total_out, 0), 0) AS product_opname
            FROM assets a
            LEFT JOIN (
                SELECT a.asset_id, SUM(a.quantity) as total_in
                FROM logistic_details a
                WHERE a.logistic_type = 'IN'
                GROUP BY a.asset_id
            ) AS b ON b.asset_id = a.id
            LEFT JOIN (
                SELECT a.asset_id, SUM(a.quantity) as total_out
                FROM logistic_details a
                WHERE a.logistic_type = 'OUT'
                GROUP BY a.asset_id
            ) AS c ON c.asset_id = a.id
            WHERE a.category_type = 'LOGISTIC'
            AND a.id IN ($placeholders)
            GROUP BY a.id
        ";

        return collect(DB::select($sqlBalance, $assetIds))->keyBy('id');
    }
}
