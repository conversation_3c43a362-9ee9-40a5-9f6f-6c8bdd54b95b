<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AssetLocationHistory extends Model
{
    use HasFactory;

    protected $fillable = ["asset_id", "room_id", "previous_room_id", "scan_time", "created_by", "updated_by", "created_by_name", "updated_by_name"];

    function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class);
    }

    function room(): BelongsTo
    {
        return $this->belongsTo(Room::class);
    }

    function prevRoom(): BelongsTo
    {
        return $this->belongsTo(Room::class, "previous_room_id");
    }

    function scopeFilterRuangan($query)
    {
        if (request("ruangan") != false) {
            $query->where("room_id", request("ruangan"));
        }
    }

    function scopeFilterDaterange($query)
    {
        if (request("periode") ?? false) {
            list($start, $end) = explode(" - ", request("periode"));
            $startDate = Carbon::parse($start)->format('Y-m-d');
            $endDate = Carbon::parse($end)->addDay(1)->format('Y-m-d');

            $query->whereBetween("scan_time", [$startDate, $endDate]);
        }
    }

    function scopeFilterAsset($query)
    {
        if (request("asset") != false) {
            $query->where("asset_id", request("asset"));
        }
    }
}
