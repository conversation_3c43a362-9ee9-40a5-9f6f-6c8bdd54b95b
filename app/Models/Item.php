<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Item extends Model
{
    use HasFactory;
    protected $fillable = ["item_code", "category_id", "item_name", "depreciation_year", "active", "created_by", "updated_by", "created_by_name", "updated_by_name"];
    
    protected $appends = ['category_code', 'category_name'];

    function assets(): HasMany
    {
        return $this->hasMany(Asset::class);
    }

    function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    function getCategoryCodeAttribute()
    {
        return $this->category ? $this->category->category_code : '';
    }

    function getCategoryNameAttribute()
    {
        return $this->category ? $this->category->category_name : '';
    }

    function scopeSearch($query)
    {
        if (request("q") ?? false) {
            $query->where("item_name", "like", "%" . request("q") . "%")
                ->orWhere("item_code", "like", "%" . request("q") . "%");
        }
    }
}
