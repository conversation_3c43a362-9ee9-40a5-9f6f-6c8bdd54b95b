<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Plan extends Model
{
    use HasFactory;
    protected $fillable = [
        "program_id",
        "room_id",
        "plan_number",
        "plan_date",
        "plan_title",
        "plan_notes",
        "total_plan_quantity",
        "total_plan_price",
        "plan_document_path",
        "request_date",
        "requester_id",
        "requester_name",
        "requester_identification_number",
        "requester_grade",
        "requester_position",
        "approval_date",
        "approver_id",
        "approver_name",
        "approver_identification_number",
        "approver_grade",
        "approver_position",
        "created_at",
        "updated_at",
        "created_by",
        "updated_by",
        "created_by_name",
        "updated_by_name",
    ];

    function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    function room(): BelongsTo
    {
        return $this->belongsTo(Room::class);
    }

    function planDetails(): HasMany
    {
        return $this->hasMany(PlanDetail::class);
    }

    function scopeFilterApprove($query)
    {
        if (request("status") ?? false) {
            if (request("status") == "approved") {
                return $query->whereNotNull('approver_id');
            }

            if (request("status") == "not-approved") {
                return $query->whereNull('approver_id');
            }
        }
    }
}


/* 
CREATE TABLE `plans` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `program_id` bigint DEFAULT NULL COMMENT 'program parent',
  `plan_number` varchar(64) DEFAULT NULL,
  `plan_date` date DEFAULT NULL,
  `plan_title` varchar(128) DEFAULT NULL,
  `plan_notes` text,
  `total_plan_quantity` decimal(12,2) DEFAULT NULL,
  `total_plan_price` decimal(14,2) DEFAULT NULL,
  `plan_document_path` text,
  `request_date` date DEFAULT NULL,
  `requester_id` bigint DEFAULT NULL,
  `requester_name` varchar(128) DEFAULT NULL,
  `requester_identification_number` varchar(128) DEFAULT NULL,
  `requester_grade` varchar(128) DEFAULT NULL,
  `requester_position` varchar(128) DEFAULT NULL,
  `approval_date` date DEFAULT NULL,
  `approver_id` varchar(128) DEFAULT NULL,
  `approver_name` varchar(128) DEFAULT NULL,
  `approver_identification_number` varchar(128) DEFAULT NULL,
  `approver_grade` varchar(128) DEFAULT NULL,
  `approver_position` varchar(128) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by` bigint DEFAULT NULL,
  `updated_by` bigint DEFAULT NULL,
  `created_by_name` varchar(128) DEFAULT NULL,
  `updated_by_name` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `logistic_room_id` (`plan_title`),
  KEY `program_id` (`program_id`),
  CONSTRAINT `plans_ibfk_1` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
*/