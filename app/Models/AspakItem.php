<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class AspakItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'aspak_items';

    protected $fillable = [
        'item_code',
        'item_name',
        'item_synonym',
        'parent_id',
        'tree',
        'created_by',
        'updated_by',
        'deleted_by',
        'created_by_name',
        'updated_by_name',
        'deleted_by_name'
    ];

    protected $casts = [
        'tree' => 'string',
    ];

    protected $enumTrees = [
        'BRANCH',
        'LEAF'
    ];

    /**
     * Relationship untuk parent (self-referencing)
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(AspakItem::class, 'parent_id');
    }

    /**
     * Relationship untuk children (self-referencing)
     */
    public function children(): HasMany
    {
        return $this->hasMany(AspakItem::class, 'parent_id');
    }

    /**
     * Relasi ke Asset: satu AspakItem dapat dimiliki banyak Asset
     */
    public function assets(): HasMany
    {
        return $this->hasMany(Asset::class, 'aspak_item_id');
    }

    /**
     * Relationship untuk user yang membuat record
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship untuk user yang mengupdate record
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Relationship untuk user yang menghapus record
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Scope untuk pencarian
     */
    public function scopeSearch($query)
    {
        if (request()->has('q')) {
            $query->where('item_name', 'like', '%' . request('q') . '%')
                ->orWhere('item_code', 'like', '%' . request('q') . '%');
        }
    }

    /**
     * Scope untuk filter berdasarkan tree
     */
    public function scopeByTree($query, $tree)
    {
        return $query->where('tree', $tree);
    }

    /**
     * Scope untuk mendapatkan hanya branch
     */
    public function scopeBranches($query)
    {
        return $query->where('tree', 'BRANCH');
    }

    /**
     * Scope untuk mendapatkan hanya leaf
     */
    public function scopeLeaves($query)
    {
        return $query->where('tree', 'LEAF');
    }

    /**
     * Scope untuk mendapatkan root items (tanpa parent)
     */
    public function scopeRoots($query)
    {
        return $query->whereNull('parent_id');
    }
}
