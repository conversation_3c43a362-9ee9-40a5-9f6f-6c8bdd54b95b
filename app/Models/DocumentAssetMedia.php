<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\BelongsToManyRelationship;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Yajra\DataTables\Html\Editor\Fields\BelongsTo;

class DocumentAssetMedia extends Model
{
    use HasFactory;
    protected $table = "document_asset_medias";
    public $timestamps = false;
    protected $fillable = ["document_asset_id", "type", "media_path"];

    function documentAsset()
    {
        return $this->belongsTo(DocumentAsset::class);
    }
}
