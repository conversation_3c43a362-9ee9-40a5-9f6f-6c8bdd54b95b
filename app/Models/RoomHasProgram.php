<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RoomHasProgram extends Model
{
    protected $table = 'room_has_programs';
    public $timestamps = false;
    protected $primaryKey = null;
    public $incrementing = false;

    protected $fillable = [
        'program_id',
        'room_id'
    ];

    public function program()
    {
        return $this->belongsTo(Program::class, 'program_id');
    }

    public function room()
    {
        return $this->belongsTo(Room::class, 'room_id');
    }
} 


/* 
contoh data
INSERT INTO `room_has_programs` (`program_id`, `room_id`) VALUES
(3,	1),
(8,	1),
(12,	1),
(15,	1);
*/