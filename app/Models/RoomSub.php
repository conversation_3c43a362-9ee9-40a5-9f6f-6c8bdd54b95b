<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RoomSub extends Model
{
    use HasFactory;
    
    protected $table = 'room_subs';
    
    protected $fillable = [
        'room_id',
        'sub_room_code',
        'sub_room_name',
        'device_id',
        'active',
        'created_by',
        'updated_by',
        'created_by_name',
        'updated_by_name'
    ];

    protected $casts = [
        'active' => 'boolean',
    ];

    protected $attributes = [
        'active' => true,
    ];

    /**
     * Relasi ke tabel rooms
     */
    public function room(): BelongsTo
    {
        return $this->belongsTo(Room::class);
    }

    /**
     * Scope untuk pencarian
     */
    public function scopeSearch($query)
    {
        if (request()->has('q')) {
            $query->where('sub_room_name', 'like', '%' . request('q') . '%')
                ->orWhere('sub_room_code', 'like', '%' . request('q') . '%');
        }
    }

    /**
     * Scope untuk filter berdasarkan status aktif
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }
}