<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\MaintenanceCategory;
use App\Models\Uom;

class MaintenancePlanDetail extends Model
{
    use HasFactory;
    public $timestamps = false;
    protected $fillable = [
        "maintenance_plan_id",
        "program_id",
        "maintenance_category_id",
        "item_id",
        "code",
        "name",
        "uom_id",
        "uom_name",
        "quantity",
        "condition"
    ];

    public function maintenancePlan()
    {
        return $this->belongsTo(MaintenancePlan::class);
    }

    function item(): BelongsTo
    {
        return $this->belongsTo(Item::class, 'item_id');
    }

    public function maintenancePlanDetailPrograms()
    {
        return $this->hasMany(MaintenancePlanDetailProgram::class, 'maintenance_plan_detail_id');
    }

    public function uom()
    {
        return $this->belongsTo(Uom::class, 'uom_id');
    }

    public function maintenanceCategory()
    {
        return $this->belongsTo(MaintenanceCategory::class, 'maintenance_category_id');
    }
}


/* 
CREATE TABLE `maintenance_plan_details` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `maintenance_plan_id` bigint DEFAULT NULL,
  `program_id` bigint DEFAULT NULL,
  `maintenance_category_id` bigint DEFAULT NULL,
  `item_id` bigint DEFAULT NULL,
  `code` varchar(64) DEFAULT NULL,
  `name` varchar(256) DEFAULT NULL,
  `uom_id` bigint DEFAULT NULL,
  `uom_name` varchar(128) DEFAULT NULL,
  `quantity` int DEFAULT NULL,
  `condition` enum('BAIK','RUSAK_RINGAN','RUSAK_BERAT') DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `uom_id` (`uom_id`),
  KEY `uom_name` (`uom_name`),
  KEY `logistic_id` (`maintenance_plan_id`),
  KEY `maintenance_category_id` (`maintenance_category_id`),
  KEY `program_id` (`program_id`),
  KEY `item_id` (`item_id`),
  CONSTRAINT `maintenance_plan_details_ibfk_1` FOREIGN KEY (`maintenance_plan_id`) REFERENCES `maintenance_plans` (`id`),
  CONSTRAINT `maintenance_plan_details_ibfk_2` FOREIGN KEY (`maintenance_category_id`) REFERENCES `maintenance_categories` (`id`),
  CONSTRAINT `maintenance_plan_details_ibfk_3` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`),
  CONSTRAINT `maintenance_plan_details_ibfk_4` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
*/