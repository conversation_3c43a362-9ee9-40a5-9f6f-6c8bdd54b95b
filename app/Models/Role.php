<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Permission\Models\Role as SpatieRole;

class Role extends SpatieRole
{
    use HasFactory;
    protected $guard_name = ['web', 'employee'];
    protected $fillable = ["name", "role_code", "role_description", "guard_name", "created_by", "updated_by", "created_by_name", "updated_by_name"];

    public function guardName()
    {
        return ['web', 'employee'];
    }

    function scopeFilterGuard($query)
    {
        if (request("guard_name") ?? false) {
            $query->where("guard_name", request("guard_name"));
        }
    }
}
