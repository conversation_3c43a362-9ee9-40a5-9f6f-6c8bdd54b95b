<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\SiapBmdProgram;

class AssetEntry extends Model
{
    use HasFactory;

    protected $casts = [
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'latest_balance' => 'decimal:2',
        'quantity' => 'integer',
        'active' => 'boolean',
        'received_date' => 'date',
        'payment_date' => 'date',
    ];

    protected $fillable = [
        "asset_entry_code",
        "category_type",
        "category_id",
        "item_id",
        "distributor_id",
        "source_supply",
        "received_date",
        "brand",
        "type",
        "material",
        "akd_number",
        "uom_id",
        "uom_name",
        "unit_price",
        "quantity",
        "total_price",
        "condition",
        "general_specifications",
        "asset_name",
        "item_name",
        "bast_payment_number",
        "bast_contract_number",
        "payment_date",
        "purchasing_account",
        "contract_form",
        "spending_account_code",
        "description",
        "latest_balance",
        "aspak_distributor",
        "aspak_maintenance_pic",
        "siap_bmd_asset_need_plan_code",
        "siap_bmd_activity_id",
        "siap_bmd_sub_activity_id",
        "siap_bmd_asset_user_name",
        "default_image",
        "active",
        "created_by",
        "updated_by",
        "size",
        "created_by_name",
        "updated_by_name",
        "config_stock_recapitulation_id",
        "config_stock_field_id",
    ];

    function assets(): HasMany
    {
        return $this->hasMany(Asset::class);
    }

    function item(): BelongsTo
    {
        return $this->belongsTo(Item::class);
    }

    function distributor(): BelongsTo
    {
        return $this->belongsTo(Distributor::class);
    }

    function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    function uom(): BelongsTo
    {
        return $this->belongsTo(Uom::class);
    }

    public function siapBmdActivity(): BelongsTo
    {
        return $this->belongsTo(SiapBmdProgram::class, 'siap_bmd_activity_id');
    }

    public function siapBmdSubActivity(): BelongsTo
    {
        return $this->belongsTo(SiapBmdProgram::class, 'siap_bmd_sub_activity_id');
    }

    function scopeSearch($query)
    {
        if (request()->has("q")) {
            $query->where("asset_name", "like", "%" . request("q") . "%")
                ->orWhere("asset_entry_code", "like", "%" . request("q") . "%");
        }
    }
}
