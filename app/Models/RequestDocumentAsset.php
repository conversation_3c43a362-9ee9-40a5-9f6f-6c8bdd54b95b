<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RequestDocumentAsset extends Model
{
    use HasFactory;
    public $timestamps = false;

    protected $fillable = ["request_document_id", "asset_id"];

    function asset(): BelongsTo
    {
        return $this->belongsTo(Asset::class);
    }
}
