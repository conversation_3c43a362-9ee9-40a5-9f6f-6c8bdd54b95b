<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Employee;

class Category extends Model
{
    use HasFactory;
    protected $fillable = ["category_name", "category_code", "pic_category", "category_type", 'category_sub_type', "active", "created_by", "updated_by", "created_by_name", "updated_by_name"];

    function items(): HasMany
    {
        return $this->hasMany(Item::class);
    }

    function pic(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'pic_category');
    }

    function scopeSearch($query)
    {
        if (request("q") ?? false) {
            $query->where("category_name", "like", "%" . request("q") . "%")
                ->orWhere("category_code", "like", "%" . request("q") . "%");
        }
    }
}
