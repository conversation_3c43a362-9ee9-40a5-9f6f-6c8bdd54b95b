<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IncidentalRequest extends Model
{
    use HasFactory;

    protected $table = "incidental_requests";
    public $timestamps = false;
    protected $fillable = [
        "request_code",
        "asset_id",
        "room_id",
        "request_date",
        "request_issue",
        "latest_activity_time",
        "latest_activity_status",
        "item_pic_id",
        "item_pic_name",
        "item_pic_identification_number",
        "item_pic_grade",
        "item_pic_position",
        "created_by",
        "created_by_name",
        "created_at",
    ];

    public static function getIncidentalRequestQuery()
    {
        $queryBuilder = self::select('incidental_requests.*',
            'rooms.room_name',
            'asset_entries.brand', 'asset_entries.type', 'asset_entries.general_specifications',
            'assets.qr_code', 'assets.register_code', 'assets.serial_number', 'assets.asset_code', 'assets.asset_name',
            'items.item_name', 'items.item_code')
            ->join("rooms", "rooms.id", "=", "incidental_requests.room_id")
            ->join("assets", "assets.id", "=", "incidental_requests.asset_id")
            ->join("asset_entries", "asset_entries.id", "=", "assets.asset_entry_id")
            ->join("items", "items.id", "=", "assets.item_id");
        return $queryBuilder;
    }

    function asset(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Asset::class);
    }

    function room(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Room::class);
    }
}
