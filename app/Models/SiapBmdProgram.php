<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class SiapBmdProgram extends Model
{
    use HasFactory, SoftDeletes;

    // Tabel sumber data di database
    protected $table = 'siap_bmd_programs';

    // Sesuaikan kolom yang umum dipakai di proyek ini
    protected $fillable = [
        'program_code',
        'program_name',
        'type',
        'parent_id',
        'tree',
        'created_by',
        'updated_by',
        'deleted_by',
        'created_by_name',
        'updated_by_name',
        'deleted_by_name',
    ];

    protected $casts = [
        'type' => 'string',
        'tree' => 'string',
    ];

    /**
     * Self relations
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(SiapBmdProgram::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(SiapBmdProgram::class, 'parent_id');
    }

    /**
     * Audit relations
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Scopes
     */
    public function scopeSearch($query)
    {
        if (request()->has('q')) {
            $q = request('q');
            $query->where(function ($sub) use ($q) {
                $sub->where('program_name', 'like', "%$q%")
                    ->orWhere('program_code', 'like', "%$q%");
            });
        }
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByTree($query, $tree)
    {
        return $query->where('tree', $tree);
    }

    public function scopePrograms($query)
    {
        return $query->where('type', 'PROGRAM');
    }

    public function scopeActivities($query)
    {
        return $query->where('type', 'ACTIVITY');
    }

    public function scopeSubActivities($query)
    {
        return $query->where('type', 'SUB_ACTIVITY');
    }

    public function scopeLeaves($query)
    {
        return $query->where('tree', 'LEAF');
    }

    public function scopeRoots($query)
    {
        return $query->whereNull('parent_id');
    }
}
