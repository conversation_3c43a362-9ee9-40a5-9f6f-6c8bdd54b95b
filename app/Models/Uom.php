<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Uom extends Model
{
    use HasFactory;
    protected $fillable = [
        "uom_name", 
        "active",
        "created_by", 
        "updated_by", 
        "created_by_name", 
        "updated_by_name"
    ];

    function scopeSearch($query)
    {
        if (request("q") ?? false) {
            return $query->where("uom_name", "like", "%" . request("q") . "%");
        }
    }
}
