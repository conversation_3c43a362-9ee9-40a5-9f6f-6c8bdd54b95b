<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Permission\Models\Permission as SpatiePermission;

class Permission extends SpatiePermission
{
    use HasFactory;
    protected $fillable = ["name", "permission_code", "permission_description", "guard_name", "created_by", "updated_by", "created_by_name", "updated_by_name"];

    public function guardName()
    {
        return ['web', 'employee'];
    }

    function scopeFilterGuard($query)
    {
        if (request("guard_name") ?? false) {
            $query->where("guard_name", request("guard_name"));
        }
    }

    function scopeSearch($query)
    {
        if (request("q") ?? false) {
            $query->where("name", "like", "%" . request("q") . "%");
        }
    }
}
