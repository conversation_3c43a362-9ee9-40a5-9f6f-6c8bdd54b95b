<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PlanDetail extends Model
{
    use HasFactory;
    protected $fillable = [
        "type",
        "plan_id",
        "program_id",
        "item_id",
        "asset_id",
        "asset_entry_id",
        "code",
        "name",
        "uom_id",
        "uom_name",
        "unit_price",
        "quantity",
        "total_price",
    ];
    public $timestamps = false;

    function programOutput()
    {
        return $this->belongsTo(Program::class, 'program_id');
    }

    function uom()
    {
        return $this->belongsTo(Uom::class, 'uom_id');
    }

    /**
     * Mendefinisikan relasi ke model PlanDetailProgram
     *
     * @return HasMany
     */
    public function planDetailPrograms()
    {
        return $this->hasMany(PlanDetailProgram::class);
    }
}


/* 
CREATE TABLE `plan_details` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `type` enum('ASSET','LOGISTIC','OTHER') DEFAULT NULL COMMENT 'asset = assets; logistic = assets_entries;  OTHER = free text',
  `plan_id` bigint DEFAULT NULL,
  `program_id` bigint DEFAULT NULL COMMENT 'program output',
  `item_id` bigint DEFAULT NULL,
  `asset_id` bigint DEFAULT NULL,
  `asset_entry_id` bigint DEFAULT NULL,
  `code` varchar(64) DEFAULT NULL,
  `name` varchar(256) DEFAULT NULL,
  `uom_id` bigint DEFAULT NULL,
  `uom_name` varchar(128) DEFAULT NULL,
  `unit_price` decimal(12,2) DEFAULT NULL,
  `quantity` int DEFAULT NULL,
  `total_price` decimal(12,2) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `asset_id` (`asset_id`),
  KEY `uom_id` (`uom_id`),
  KEY `uom_name` (`uom_name`),
  KEY `logistic_id` (`plan_id`),
  KEY `asset_entry_id` (`asset_entry_id`),
  KEY `program_id` (`program_id`),
  KEY `item_id` (`item_id`),
  CONSTRAINT `plan_details_ibfk_1` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`),
  CONSTRAINT `plan_details_ibfk_2` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`),
  CONSTRAINT `plan_details_ibfk_4` FOREIGN KEY (`asset_entry_id`) REFERENCES `asset_entries` (`id`),
  CONSTRAINT `plan_details_ibfk_5` FOREIGN KEY (`program_id`) REFERENCES `programs` (`id`),
  CONSTRAINT `plan_details_ibfk_6` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
*/