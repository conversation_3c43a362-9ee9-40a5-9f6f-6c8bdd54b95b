<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RequestLogistic extends Model
{
    use HasFactory;

    protected $table = "request_logistics";
    protected $fillable = [
        "logistic_type",
        "logistic_number",
        "room_id",
        "config_stock_field_id",
        "logistic_date",
        "logistic_notes",
        "total_logistic_quantity",
        "logistic_document_path",
        "request_date",
        "requester_id",
        "requester_name",
        "requester_identification_number",
        "requester_grade",
        "requester_position",
        "active",
        "logistic_id",
        "created_at",
        "updated_at",
        "created_by",
        "updated_by",
        "created_by_name",
        "updated_by_name"
    ];

    public static function getRequestLogisticQuery($logisticType, $roomId = null, $field_option = null)
    {
        $query = self::select('request_logistics.*', 'rooms.room_code', 'rooms.room_name', 'config_stock_fields.code AS field_code', 'config_stock_fields.name AS field_name')
            ->join("rooms", "rooms.id", "=", "request_logistics.room_id")
            ->join("config_stock_fields", "config_stock_fields.id", "=", "request_logistics.config_stock_field_id")
            ->where("request_logistics.logistic_type", $logisticType)
            ->where("request_logistics.active", 1);

        if ($roomId) {
            $query->where("request_logistics.room_id", $roomId);
        }

        if ($field_option) {
            $query->where("request_logistics.config_stock_field_id", $field_option);
        }

        return $query;
    }

}
