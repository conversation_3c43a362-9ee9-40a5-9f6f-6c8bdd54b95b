<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Officer extends Model
{
    use HasFactory;

    protected $table = "officers";
    protected $fillable = ["officer_name", "officer_identification_number"];


    function scopeSearch($query)
    {
        if (request("q") ?? false) {
            $query->where("officer_name", "like", "%" . request("q") . "%")
                ->orWhere("officer_identification_number", "like", "%" . request("q") . "%");
        }
    }
}
