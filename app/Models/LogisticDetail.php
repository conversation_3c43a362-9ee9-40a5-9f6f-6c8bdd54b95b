<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LogisticDetail extends Model
{
    use HasFactory;

    protected $table = "logistic_details";
    public $timestamps = false;
    protected $fillable = ["logistic_id", "logistic_type", "asset_id", "uom_id", "uom_name", "unit_price", "quantity", "total_price"];

    function logistic()
    {
        return $this->belongsTo(Logistic::class);
    }

    function asset()
    {
        return $this->belongsTo(Asset::class);
    }
}
