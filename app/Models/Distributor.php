<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Distributor extends Model
{
    use HasFactory;
    
    protected $fillable = [
        "distributor_name", 
        "distributor_address", 
        "distributor_phone", 
        "distributor_email", 
        "created_by", 
        "updated_by", 
        "created_by_name", 
        "updated_by_name"
    ];

    function scopeSearch($query)
    {
        if (request("q") ?? false) {
            return $query->where("distributor_name", "like", "%" . request("q") . "%");
        }
    }
}
