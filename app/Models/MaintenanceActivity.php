<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MaintenanceActivity extends Model
{
    use HasFactory;

    protected $table = "maintenance_activities";
    protected $fillable = ["activity_category", "distributor_id", "activity_type", "activity_code", "activity_date", "total_activity_cost", "document_path", "document_name", "notes", "room_id", "created_by", "created_by_name", "updated_by_name"];

    public static function getMaintenanceActivityQuery($category)
    {
        return self::select('maintenance_activities.id', 'maintenance_activities.activity_date', 'maintenance_activities.activity_category', 'maintenance_activities.activity_type', 'maintenance_activities.activity_code', 'maintenance_activities.notes', 'maintenance_activities.total_activity_cost',
            'distributors.distributor_name')
            ->join("distributors", "distributors.id", "=", "maintenance_activities.distributor_id")
            ->where("activity_category", $category);
    }
}
