<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Asset extends Model
{
    use HasFactory, SoftDeletes;

    protected $casts = [
        'connected_ups' => 'boolean',
        'product_origin' => 'string',
        'power_source' => 'string',
        'technical_lifetime_year' => 'integer',
        'unit_price' => 'decimal:2',
        'latest_balance' => 'decimal:2',
        'active' => 'boolean',
    ];

    protected $fillable = [
        "asset_entry_id",
        "category_type",
        "item_id",
        "item_name",
        "aspak_item_id",
        "asset_year",
        "document_room_id",
        "document_sub_room_id",
        "aspak_service_room_id",
        "aspak_tool",
        "aspak_description",
        "aspak_image_url",
        "actual_room_id",
        "register_code",
        "qr_code",
        "rfid_tag",
        "brand",
        "type",
        "akd_number",
        "general_specifications",
        "material",
        "size",
        "electricity",
        "chassis_number",
        "serial_number",
        "engine_number",
        "license_plate_number",
        "bpkb_number",
        "sub_activity_code",
        "sub_activity_name",
        "asset_code",
        "sub_register_code",
        "sub_register_price_code",
        "unit_price",
        "uom_id",
        "uom_name",
        "asset_name",
        "description",
        "active",
        "asset_condition",
        "default_image",
        "product_origin",
        "power_source",
        "connected_ups",
        "information_tkdn",
        "risk_class",
        "technical_lifetime_year",
        "created_by",
        "updated_by",
        "deleted_by",
        "deleted_at",
        "created_by_name",
        "updated_by_name",
        "deleted_by_name"
    ];

    function assetEntry(): BelongsTo
    {
        return $this->belongsTo(AssetEntry::class);
    }

    function item(): BelongsTo
    {
        return $this->belongsTo(Item::class);
    }

    function room(): BelongsTo
    {
        return $this->belongsTo(Room::class, "document_room_id")->withDefault();
    }

    function roomSub(): BelongsTo
    {
        return $this->belongsTo(RoomSub::class, "document_sub_room_id")->withDefault();
    }

    function uom(): BelongsTo
    {
        return $this->belongsTo(Uom::class, "uom_id");
    }

    function aspakItem(): BelongsTo
    {
        return $this->belongsTo(AspakItem::class, "aspak_item_id");
    }

    function aspakServiceRoom(): BelongsTo
    {
        return $this->belongsTo(AspakServiceRoom::class, "aspak_service_room_id");
    }

    function documentAssets()
    {
        return $this->hasMany(DocumentAsset::class);
    }

    public function maintenancePlanDetails()
    {
        return $this->hasMany(MaintenancePlanDetail::class, 'asset_id');
    }

    function scopeSearch($query)
    {
        if (request("q") ?? false) {
            $query->where("qr_code", "like", "%" . request("q") . "%")
                ->orWhere("serial_number", "like", "%" . request("q") . "%");
        }
    }

    function scopeFilterRoom($query)
    {
        if (request("room") ?? false) {
            $query->where("document_room_id", request("room"));
        }
    }

    function scopeFilterAllocation($query)
    {
        if (request("filter") ?? false) {
            if (request("filter") == "not-allocation") {
                $query->whereNull("document_room_id");
            }
        }
    }

    function scopeFilterById($query)
    {
        if (request("asset") ?? false) {
            $query->where("id", request("asset"));
        }
    }
}
