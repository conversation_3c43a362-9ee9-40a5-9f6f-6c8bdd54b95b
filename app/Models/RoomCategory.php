<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RoomCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        "room_category_code",
        "room_category_name",
        "active",
        "created_by",
        "updated_by",
        "created_by_name",
        "updated_by_name"
    ];

    public function rooms(): HasMany
    {
        return $this->hasMany(Room::class, "room_category", "id");
    }

    public function scopeSearch($query)
    {
        if (request()->has("q")) {
            $query->where("room_category_name", "like", "%" . request("q") . "%")
                ->orWhere("room_category_code", "like", "%" . request("q") . "%");
        }
    }
}
