<?php

use Illuminate\Support\Facades\Auth;

if (!function_exists('getAuthUserId')) {
    function getAuthUserId()
    {
        return auth()->guard('web')->user()
            ? auth()->user()->id
            : auth()->guard('employee')->user()->id;
    }
}

if (!function_exists('getAuthUserName')) {
    function getAuthUserName()
    {
        return auth()->guard('web')->user()
            ? auth()->user()->name
            : auth()->guard('employee')->user()->employee_name;
    }
}

if (!function_exists('getAuthGuardName')) {
    function getAuthGuardName()
    {
        return auth()->guard('employee')->check()
            ? "employee"
            : "web";
    }
}

if (!function_exists('getAuthUserRoleName')) {
    function getAuthUserRoleName()
    {
        return auth()->guard('web')->user()
            ? auth()->user()->roles->first()->name
            : auth()->guard('employee')->user()->roles->first()->name;
    }
}

if (!function_exists('hasPermissionInGuard')) {
    function hasPermissionInGuard($permission, $guards = ['web', 'employee'])
    {
        foreach ($guards as $guard) {
            $user = auth()->guard($guard)->user();
            if ($user && $user->can($permission)) {
                return true;
            }
        }
        return false;
    }
}
