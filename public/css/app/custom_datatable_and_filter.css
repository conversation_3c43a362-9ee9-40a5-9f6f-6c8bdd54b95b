/* ========================================
   MODERN DATATABLE & FILTER STYLES
   ======================================== */

/* Main Container */
.datatable-modern-container {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    padding: 24px;
    margin: 20px 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Control Bar */
.datatable-control-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    gap: 16px;
}

/* Search Container */
.datatable-search-container {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.datatable-search-input {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.datatable-search-input input {
    width: 100%;
    padding: 8px 16px 8px 44px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    background: #f8f9fa;
    font-size: 14px;
    transition: all 0.3s ease;
}

.datatable-search-input input:focus {
    outline: none;
    border-color: #007bff;
    background: #fff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.datatable-search-input input::placeholder {
    color: #adb5bd;
    font-style: italic;
}

.datatable-search-input::before {
    content: "🔍";
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #6c757d;
    z-index: 2;
}

/* Action Buttons */
.datatable-action-buttons {
    display: flex;
    align-items: center;
    gap: 12px;
}

.datatable-action-buttons .btn {
    display: flex;
    align-items: center;
    gap: 6px;
}

/* Button Styles */
.btn-modern-rounded {
    border-radius: 8px !important;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-modern-rounded:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Filter Icon */
.datatable-filter-icon {
    padding: 8px 12px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    background: #f8f9fa;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
}

.datatable-filter-icon:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Filter Icon Click Handler */
.datatable-filter-icon {
    cursor: pointer;
}

/* Modern Table */
.datatable-modern-table {
    border: none;
    border-radius: 8px;
    overflow: visible;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.datatable-modern-table thead th {
    background: #f8f9fa;
    border: none;
    padding: 10px 12px;
    font-weight: 600;
    color: #495057;
    font-size: 13px;
    position: relative;
}

.datatable-modern-table thead th.orderable::after {
    content: "↕";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    color: #adb5bd;
    cursor: pointer;
}

.datatable-modern-table tbody td {
    border: none;
    border-bottom: 1px solid #f1f3f4;
    padding: 8px 12px;
    vertical-align: middle;
    font-size: 13px;
    color: #212529;
    background: #fff;
}

.datatable-modern-table tbody tr:hover {
    background: #f8f9fa;
}

/* Action Column */
.datatable-modern-table th:last-child,
.datatable-modern-table td:last-child {
    text-align: center;
}

/* Dropdown Styles */
.datatable-dropdown-menu {
    border: none;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 6px 0;
    min-width: 100px;
    margin-top: 2px;
    z-index: 9999 !important;
    position: absolute !important;
}

/* Ensure dropdown container has proper positioning */
.datatable-modern-table td .dropdown {
    position: relative !important;
}

/* Force dropdown to appear above other elements */
.datatable-modern-table .dropdown-menu {
    z-index: 9999 !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    transform: none !important;
}

.datatable-dropdown-item {
    padding: 6px 12px;
    font-size: 12px;
    color: #495057;
    transition: all 0.3s ease;
    cursor: pointer;
}

.datatable-dropdown-item:hover {
    background: #f8f9fa;
    color: #212529;
}

/* Button Outline Secondary */
.btn-outline-secondary-modern {
    border: 1px solid #e1e5e9;
    background: transparent;
    color: #6c757d;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: 11px;
}

.btn-outline-secondary-modern:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
}

.btn-outline-secondary-modern:focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
}

.btn-outline-secondary-modern::after {
    display: none;
}

/* Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Hide Default DataTables Elements */
.dataTables_wrapper .dataTables_filter {
    display: none !important;
}

/* Fix DataTables dropdown positioning issues */
.dataTables_wrapper .dropdown-menu {
    z-index: 9999 !important;
}

/* Ensure table cells with dropdowns have proper overflow */
.datatable-modern-table td {
    overflow: visible !important;
}

.datatable-modern-table {
    overflow: visible !important;
}

/* Rows Per Page */
.datatable-rows-per-page {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #495057;
}

.datatable-rows-per-page label {
    margin: 0;
    font-weight: 500;
}

.datatable-rows-per-page select {
    padding: 4px 8px;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    background: #fff;
    font-size: 12px;
    min-width: 50px;
}

.datatable-rows-per-page span {
    color: #6c757d;
}

/* Custom Pagination */
.datatable-custom-pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 12px;
    padding: 8px 0;
    border-top: 1px solid #e1e5e9;
}

/* Filter Drawer Styles */
.modal-drawer {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: auto;
    width: 400px;
    height: 100%;
    margin: 0;
    transform: translateX(100%);
    transition: transform 0.2s ease-out;
    z-index: 1050;
}

.modal-drawer .modal-content {
    height: 100%;
    border-radius: 0;
    border: none;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
}

.modal-drawer .modal-header {
    border-bottom: none;
    background: #f8f9fa;
    padding: 1.5rem;
}

.modal-drawer .modal-title {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.modal-drawer .close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    line-height: 1;
}

.modal-drawer .close:hover {
    background: #e9ecef;
    color: #495057;
}

.modal-drawer .modal-body {
    padding: 2rem;
    height: calc(100% - 80px);
    overflow-y: auto;
}

.modal.show .modal-drawer {
    transform: translateX(0);
}

/* Modal Backdrop */
.modal-backdrop {
    z-index: 1040;
    transition: opacity 0.2s ease-out;
}

/* No Filter Message */
.datatable-no-filter-message {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.datatable-no-filter-message i {
    font-size: 3rem;
    color: #adb5bd;
    margin-bottom: 1rem;
    display: block;
}

.datatable-no-filter-message p {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #495057;
}

.datatable-no-filter-message small {
    font-size: 0.9rem;
    color: #adb5bd;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .modal-drawer {
        width: 100%;
    }

    .datatable-control-bar {
        flex-direction: column;
        align-items: stretch;
    }

    .datatable-search-container {
        order: 2;
    }

    .datatable-action-buttons {
        order: 1;
        justify-content: space-between;
    }

    .datatable-custom-pagination {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }

    .datatable-rows-per-page {
        width: 100%;
        justify-content: flex-end;
    }
}

@media (min-width: 768px) {
    /* Fix dropdown positioning in table-responsive */
    .table-responsive {
        overflow: visible !important;
    }
}


/* Ensure dropdowns appear above DataTables elements */
.dataTables_wrapper {
    overflow: visible !important;
}

.dataTables_scrollBody {
    overflow: visible !important;
}

/* Fix Select2 z-index in modal */
.modal .select2-container {
    /* z-index: 9999 !important; */
}

.modal .select2-dropdown {
    z-index: 9999 !important;
}

.modal .select2-results {
    z-index: 9999 !important;
}

/* Ensure Select2 appears above modal backdrop */
.select2-container--open {
    z-index: 9999 !important;
}

.select2-dropdown {
    z-index: 9999 !important;
}

.select2-results {
    z-index: 9999 !important;
}

/* Select2 styling compatibility */
.select2-container--default .select2-selection--single {
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    height: 42px;
    padding: 8px 12px;
}

.select2-container--default .select2-selection--single:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 24px;
    color: #495057;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 40px;
}

/* ========================================
   DATERANGEPICKER Z-INDEX FIXES
   ======================================== */

/* Ensure daterangepicker appears above modal */
.daterangepicker {
    z-index: 99999 !important;
    position: fixed !important;
}

/* Modal daterangepicker specific fixes */
.modal .daterangepicker {
    z-index: 99999 !important;
}

.modal-drawer .daterangepicker {
    z-index: 99999 !important;
}

/* Daterangepicker dropdown positioning */
.daterangepicker.dropdown-menu {
    z-index: 99999 !important;
    position: absolute !important;
}

/* Ensure daterangepicker appears above Select2 */
.daterangepicker {
    z-index: 99999 !important;
}

.select2-container--open {
    z-index: 9999 !important;
}

.daterangepicker {
    z-index: 99999 !important;
}

/* Force daterangepicker to appear above all modal elements */
#filterDrawer .daterangepicker,
.modal .daterangepicker,
.modal-drawer .daterangepicker {
    z-index: 99999 !important;
    position: fixed !important;
}

/* Additional daterangepicker styling for better appearance */
.daterangepicker {
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.daterangepicker .ranges li {
    border-radius: 4px;
    margin: 2px 0;
    transition: all 0.2s ease;
}

.daterangepicker .ranges li:hover {
    background: #f8f9fa;
}

.daterangepicker .ranges li.active {
    background: #007bff;
    color: #fff;
}

.daterangepicker .drp-buttons {
    border-top: 1px solid #e1e5e9;
    padding: 10px;
}

.daterangepicker .drp-selected {
    font-size: 12px;
    color: #6c757d;
}

.daterangepicker .calendar-table {
    border: none;
}

.daterangepicker .calendar-table th,
.daterangepicker .calendar-table td {
    border: none;
    padding: 8px;
}

.daterangepicker .calendar-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.daterangepicker .calendar-table td.available:hover {
    background: #f8f9fa;
    border-radius: 4px;
}

.daterangepicker .calendar-table td.active {
    background: #007bff;
    color: #fff;
    border-radius: 4px;
}

.daterangepicker .calendar-table td.in-range {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

/* Custom badge colors for Type column */
.badge.bg-purple {
    background-color: #a78bfa !important;
    color: #4c1d95 !important;
}

.badge.bg-cyan {
    background-color: #67e8f9 !important;
    color: #0891b2 !important;
}

.badge.bg-pink {
    background-color: #f9a8d4 !important;
    color: #be185d !important;
}