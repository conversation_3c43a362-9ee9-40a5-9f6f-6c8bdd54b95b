/* SIAP BMD Integration Specific Styles */

/* SIAP BMD Info Cell Styling */
.siap-bmd-info-cell {
    font-size: 0.875rem;
    line-height: 1.4;
    max-width: 250px;
    word-wrap: break-word;
}

.siap-bmd-info-cell .text-muted {
    font-size: 0.75rem;
    color: #6c757d !important;
}

/* SIAP BMD Plan Info Cell Styling */
.siap-bmd-plan-info-cell {
    font-size: 0.875rem;
    line-height: 1.4;
    max-width: 200px;
    word-wrap: break-word;
}

.siap-bmd-plan-info-cell .text-muted {
    font-size: 0.75rem;
    color: #6c757d !important;
}

/* Responsive adjustments for SIAP BMD columns */
@media (max-width: 768px) {
    .siap-bmd-info-cell,
    .siap-bmd-plan-info-cell {
        max-width: 150px;
        font-size: 0.8rem;
    }
    
    .siap-bmd-info-cell .text-muted,
    .siap-bmd-plan-info-cell .text-muted {
        font-size: 0.7rem;
    }
}

/* Enhanced table styling for SIAP BMD */
#datatable th:nth-child(5), /* SIAP BMD Activity & Sub Activity */
#datatable th:nth-child(6)  /* Rencana Kebutuhan & User */ {
    min-width: 200px;
}

/* QR Code container specific for SIAP BMD */
.qr-code-container {
    transition: transform 0.2s ease;
}

.qr-code-container:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* Filter drawer enhancements for SIAP BMD */
#filterDrawer .modal-body {
    padding: 1.5rem;
}

#filterDrawer .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Search input styling for SIAP BMD */
#searchInput::placeholder {
    color: #6c757d;
    font-style: italic;
}

/* Loading state styling */
.datatable-modern-table tbody tr.loading {
    background-color: #f8f9fa;
    opacity: 0.7;
}

/* Custom scrollbar for horizontal scroll */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
