/* ASPAK Integration specific styles */

.aspak-info-cell {
    max-width: 200px;
    word-wrap: break-word;
}


/* Created at column styling for two-line format */
.datatable-modern-table td:nth-child(8) {
    line-height: 1.2;
    padding: 8px 12px;
}

.datatable-modern-table td:nth-child(8) small.text-muted {
    display: block;
    margin-top: 2px;
    font-size: 0.85em;
}

.qr-code-container {
    transition: all 0.2s ease;
}

.qr-code-container:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Responsive table adjustments for ASPAK data */
@media (max-width: 768px) {
    .aspak-info-cell {
        max-width: 150px;
        font-size: 0.85em;
    }
}

/* DataTable responsive adjustments */
.datatable-modern-table th,
.datatable-modern-table td {
    vertical-align: middle;
}

.datatable-modern-table .text-muted {
    font-size: 0.85em;
}
