$(document).ready(function () {
    // Store column definitions for later use
    var columnDefinitions = [
        {
            data: "DT_RowIndex",
            name: "DT_RowIndex",
            orderable: false,
            searchable: false,
        },
        {
            data: "qr_code",
            name: "real_qr_code",
            orderable: false,
            render: function (v, t, r) {
                return `<div class="text-center qr-code-container" style="cursor: pointer;" data-qr-code="${r.real_qr_code}">
                    ${r.qr_code}
                </div>`;
            },
            searchable: true,
        },
        {
            data: "item_name",
            name: "item_name",
            render: function (v, t, r) {
                return `${r.item_name} <br> <span class="text-secondary">${r.asset_name || '-'}</span>`;
            },
            searchable: true,
        },
        {
            data: "serial_number",
            name: "serial_number",
            orderable: false,
            render: function (v, t, r) {
                return `${r.serial_number || '-'}`;
            },
            searchable: true,
        },
        {
            data: "aspak_info",
            name: "aspak_info",
            orderable: false,
            searchable: false,
            render: function (data, type, row) {
                return `<div class="aspak-info-cell">${data}</div>`;
            }
        },
        {
            data: "formatted_created_at",
            name: "assets.created_at",
            orderable: true,
            searchable: false,
        },
        {
            data: "action",
            name: "action",
            orderable: false,
            searchable: false,
            render: function (data, type, row) {
                var routeMatch = data.match(/href="([^"]+)"/);
                var route = routeMatch ? routeMatch[1] : '';
                return `
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary-modern btn-modern-rounded dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu datatable-dropdown-menu">
                            <li><a class="dropdown-item datatable-dropdown-item btn-edit" href="${route}"><i class="fas fa-edit me-2"></i>Edit</a></li>
                        </ul>
                    </div>
                `;
            }
        },
    ];

    $("#datatable").DataTable({
        processing: true,
        serverSide: true,
        bDestroy: true,
        ajax: {
            url: "/manajemen-aset/aspak-integration-list",
            data: function (d) {
                d.filter = $("#kategori").val();
            },
        },
        columns: columnDefinitions,
        order: [[5, 'asc']], // Default order by 'Dibuat Pada' ascending
        dom: 'rt<"d-flex justify-content-between align-items-center"ip>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: '<div class="spinner-border spinner-border-sm" role="status"></div> Loading...'
        },
        scrollX: true,
        responsive: {
            details: {
                type: 'column',
                target: 'tr'
            }
        }
    });

    // Add orderable class to columns that are actually orderable
    $('#datatable').on('init.dt', function () {
        // Iterate through each column definition
        columnDefinitions.forEach(function(column, index) {
            // Check if the column is orderable (default is true if not specified)
            if (column.orderable !== false) {
                // Add the orderable class to the corresponding header cell
                $('#datatable thead th').eq(index).addClass('orderable');
            }
        });
    });

    // Custom search functionality
    $("#searchInput").on("keyup", function () {
        $("#datatable").DataTable().search(this.value).draw();
    });

    // Filter drawer functionality
    $(".datatable-filter-icon").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("show");
    });

    // Close filter drawer
    $("#closeFilterDrawer").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("hide");
    });

    $("#filterDrawer").on("click", function (e) {
        if (e.target === this) {
            $("#filterDrawer").modal("hide");
        }
    });

    // Handle kategori filter change
    $("#kategori").on("change", function () {
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Custom rows per page functionality
    $("#rowsPerPage").on("change", function () {
        $("#datatable").DataTable().page.len($(this).val()).draw();
    });

    // Update rows per page dropdown when table is drawn
    $("#datatable").on("draw.dt", function () {
        var table = $("#datatable").DataTable();
        var pageLength = table.page.len();
        $("#rowsPerPage").val(pageLength);
    });

    // Initialize Select2 for filter dropdown
    $("#kategori").select2({
        dropdownParent: $("#filterDrawer")
    });

    // QR Code click handler
    $("#datatable").on("click", ".qr-code-container", function () {
        const qrCode = $(this).data("qr-code");
        const qrCodeImage = $(this).html(); // Get the QR code image HTML

        $("#qr-code-text").val(qrCode);
        $("#qr-code-image").html(qrCodeImage);
        $("#modal-qr-code").modal("show");
    });

    // Copy QR Code functionality
    $("#copy-qr-code").on("click", function () {
        const qrCodeText = $("#qr-code-text").val();
        const $button = $(this);
        const originalText = $button.html();

        navigator.clipboard.writeText(qrCodeText).then(function() {
            // Show success message
            $button.html('<i class="fas fa-check"></i> Copied!');
            $button.removeClass('btn-outline-secondary').addClass('btn-success');

            setTimeout(function() {
                $button.html(originalText);
                $button.removeClass('btn-success').addClass('btn-outline-secondary');
            }, 2000);
        }).catch(function(err) {
            console.error('Could not copy text: ', err);
            alert('Gagal menyalin QR Code');
        });
    });
});
